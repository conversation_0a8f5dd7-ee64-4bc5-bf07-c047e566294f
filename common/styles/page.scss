@use 'ngx-tethys/styles/variables.scss';
@use '@worktile/theia/styles/mixins.scss' as mixins;
// outside 与 app
.common-page-big-title {
    font-size: 32px;
    display: inline-flex;
    align-items: baseline;
    word-break: break-all;
    margin-bottom: 20px;
    line-height: 1.4;
}

.common-page-creator {
    display: flex;
    align-items: center;
    color: variables.$gray-500;
    font-size: variables.$font-size-sm;
    padding-top: 20px;
    .creator {
        display: flex;
        align-items: center;
    }
    .thy-divider {
        height: 14px;
    }
    .reader-count {
        span {
            color: variables.$gray-500;
        }
    }
}

.common-editor-toolbar {
    @include mixins.common-toolbar-icon-color();
}
