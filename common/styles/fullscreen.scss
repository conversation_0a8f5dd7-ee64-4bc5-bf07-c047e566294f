@use 'ngx-tethys/styles/variables.scss';
@use '@atinc/ngx-styx/styles/variables.scss' as styx-variables;
@use '../styles/variables.scss' as commonVariables;

.wiki-fullscreen {
    // 全屏时隐藏
    .fullscreen-hidden {
        display: none !important;
    }
    // 高度 100%
    .height-100 {
        height: 100%;
    }
    // 全屏头部样式，隐藏原先的内容，显示‘返回页面’
    .fullscreen-header {
        display: flex;
        position: relative;
        min-height: 52px;
        z-index: 1000;
        padding-left: 12px;
        background: variables.$bg-default;
        box-shadow: styx-variables.$layout-header-box-shadow;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        border-bottom: commonVariables.$wiki-border-default;
        > :not(.header-main) {
            display: none;
        }
        .wiki-common-fullscreen-header {
            display: flex;
            font-size: 16px;
            .thy-icon {
                font-size: 20px;
            }
        }
    }
    // 为了实现返回页面时回到原来的滚动位置，需要移除原先设置的样式
    .wiki-fullscreen-prevent-scroll {
        width: 100%;
        display: unset;
        overflow-y: unset;
    }

    // theia 内部样式
    .wiki-common-editor,
    .wiki-preview-document {
        > .the-editor {
            height: 100%;

            .the-editable-container {
                height: 100%;
                overflow-y: auto;
                overflow-x: hidden;
                padding: 0px;
                margin: 0;

                .slate-editable-container {
                    min-height: unset;
                    height: 100%;
                    padding-bottom: 0;
                    overflow: hidden;

                    .wiki-fullscreen-node {
                        position: absolute;
                        top: 0;
                        right: 0;
                        bottom: 0;
                        left: 0;
                        z-index: 999;
                        width: auto;
                        height: auto !important;
                        background: variables.$bg-default;
                        margin: 0 48px;
                    }

                    // 文本绘图
                    .slate-element-text-diagram {
                        padding: 0;
                        padding-top: 60px;

                        .text-diagram-toolbar {
                            top: 48px;
                        }
                        .text-diagram-container {
                            margin-bottom: 48px;
                            height: calc(100% - 48px) !important;
                        }
                        .thy-resizable-handle {
                            display: none;
                        }
                    }

                    .slate-block-card {
                        .slate-element-layout {
                            .slate-element-layout-column {
                                border: none;
                            }
                        }
                    }

                    // 表格
                    .slate-block-card-table {
                        height: 100%;

                        .slate-element-table {
                            padding-top: 26px;

                            .the-table-wrapper {
                                margin-top: 0px;
                                margin-bottom: 0;
                                padding-bottom: 48px;
                            }
                            .the-table-left-shadow::before,
                            .the-table-right-shadow::after {
                                bottom: 48px;
                            }
                        }
                    }

                    // 思维脑图
                    .slate-element-diagram-board {
                        margin: 0;
                        padding: 0;

                        .common-plugin-card-element {
                            border: none;
                        }
                    }

                    // 关联列表
                    .#{commonVariables.$wiki-relation-list} {
                        padding-top: 28px;
                        // 全屏时，表格内部底部间距 12px
                        .styx-table-list {
                            margin-bottom: 12px;
                        }
                    }
                }
            }
        }
    }

    //编辑页面
    .wiki-edit-document-content-body {
        padding-bottom: 0;
        // 设置宽高与父元素一致
        .wiki-common-editor {
            width: 100% !important;
            height: 100%;
        }
    }

    //预览全屏查看公共样式
    .wiki-preview-fullscreen {
        height: calc(100% - 52px);

        .preview-fullscreen-content-container {
            padding: 0;
            width: auto;
        }

        .wiki-preview-document > .the-editor {
            > .the-editable-container {
                > .slate-editable-container {
                    padding-left: commonVariables.$editable-padding-left;
                    padding-right: commonVariables.$editable-padding-left;
                    .wiki-fullscreen-node {
                        &.slate-element-table {
                            padding-top: 15px;
                        }
                        .text-diagram-container {
                            height: 100% !important;
                        }
                        &.slate-element-text-diagram {
                            padding-right: 0;
                            padding-top: 0;
                            margin: 0;
                            // 文本绘图全屏的时候无边框
                            .common-plugin-card-element {
                                border: none;
                            }
                        }
                    }
                }
            }
        }
    }

    //预览页面
    .page-detail-container .page-detail-body {
        &.page-content-body .common-document-content-container {
            height: 100%;
            margin-right: 0;
            max-width: 100vw;
        }
    }

    //文档共享
    .outside-document-content-body {
        margin: 0;
        width: auto;
    }

    //空间共享
    &.wiki-outside-space .common-document-content-container {
        padding: 0;
    }

    //共享弹框
    &.wiki-page-dialog {
        > .thy-layout {
            width: 100%;
        }
    }

    // 移除全宽编辑模式下工具栏原本的 padding
    .wiki-edit-document-max-view .wiki-edit-document-toolbar {
        padding-left: 0;
    }

    // 编辑器顶部工具栏样式修改
    .wiki-edit-document-toolbar {
        justify-content: start;
        width: 100%;
        .the-toolbar-container {
            padding: 0 40px;
            width: 100%;
            .thy-divider:last-of-type,
            the-table-toolbar-item {
                display: none;
            }
        }
    }

    // 移除 slate-block-card 原本的上下间距
    .the-editor-typo .slate-block-card:not(:last-child) {
        margin: 0;
    }

    // 全屏元素需要根据 slate-angular 节点进行定位，所以需要移除中间的父元素设置的 position: relative 属性
    .slate-element-layout,
    .slate-element-layout-column,
    .slate-block-card,
    .slate-block-card-table,
    .slate-element-toggle-list,
    .the-editor-typo.slate-editable-container,
    .the-editor-typo.slate-editable-container > .slate-element-toggle-list > .toggle-list-container,
    .card-right,
    .card-left,
    .the-editor-typo.slate-editable-container > .slate-element-toggle-list > .toggle-list-container > .slate-element-toggle-list-item {
        position: unset !important;
    }

    // 移除 1440px 屏幕下原先设置的右边距
    @media (max-width: 1440px) {
        .wiki-edit-document-content-body {
            .wiki-common-editor,
            .wiki-preview-document {
                margin-right: 0;
            }
        }
    }

    // 全屏时隐藏标签
    .wiki-page-tags {
        display: none;
    }
}

.wiki-fullscreen-toolbar-hidden {
    // 全屏下隐藏顶部
    .wiki-edit-document-toolbar,
    .styx-pilot-detail-header-container {
        display: none;
    }
}
// 全屏下，隐藏顶部工具栏的部分内容，例如 layout、 table 等
.wiki-fullscreen-toolbar {
    .fullscreen-hidden {
        display: none !important;
    }
}
// 全屏下隐藏其他元素的工具栏，比如文本绘图的操作工具栏
.wiki-fullscreen-hidden {
    display: none;
}

.fullscreen-header {
    display: none;
}
