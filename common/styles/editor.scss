/**
* 编辑器插件基础样式（包含 编辑页样式）
*/
@use 'ngx-tethys/styles/variables.scss';
@use './variables.scss' as commonVariables;

// 基础样式
.the-editor {
    .common-plugin-card-element {
        border: commonVariables.$wiki-border-default;
        border-radius: variables.$border-radius;
        // 通用的修饰内部元素边角溢出样式
        .border-radius-covering {
            border-radius: commonVariables.$border-radius-covering;
        }
        .border-radius-covering-left {
            border-top-left-radius: commonVariables.$border-radius-covering;
            border-bottom-left-radius: commonVariables.$border-radius-covering;
        }
    }
    .text-mode {
        .common-plugin-card-element {
            border: none;
        }
    }

    // 日期、标签、公式、关联元素 text 模式等元素的高度、间隔处理
    .slate-element-date,
    .slate-element-label {
        display: inline-flex;
        border: 2px solid transparent;
        border-radius: variables.$border-radius;

        &:not(.readonly).slate-focus-element {
            .wiki-common-date,
            .wiki-common-label {
                box-shadow: variables.$primary 0px 0px 0px 1px;
            }
        }
        &.readonly {
            cursor: inherit;

            .wiki-common-date,
            .wiki-common-label {
                cursor: inherit;
            }
        }
    }

    .wiki-common-date,
    .wiki-common-label {
        height: 28px;
        border-radius: variables.$border-radius;
        cursor: pointer;
    }
}

// 编辑页固定样式
.the-editor:not(.the-editor-readonly) {
    .common-plugin-card-element {
        cursor: pointer;
        &:hover {
            border-color: variables.$primary;
        }
    }
    // 文本模式
    .text-mode {
        .common-plugin-card-element {
            &:hover {
                background: commonVariables.$bg-transparent-primary;
            }
        }
    }
    // danger 模式，hover 删除时，common-plugin-card-element 背景 danger
    .danger-mode {
        .common-plugin-card-element {
            border-color: variables.$danger !important;
            background: commonVariables.$bg-transparent-danger !important;
        }
    }
    // 选中样式
    .slate-focus-element {
        .common-plugin-card-element {
            border-color: variables.$primary;
        }
    }
    .text-mode.slate-focus-element {
        .common-plugin-card-element {
            background: commonVariables.$bg-transparent-primary;
        }
    }
}

// 只读样式
.the-editor-readonly {
    .text-mode {
        .common-plugin-card-element {
            cursor: pointer;
        }
    }
}

.the-resizable-handle-bottom {
    cursor: row-resize;
    height: 8px;
    width: 45px;
    left: 50%;
    transform: translateX(-50%);
    background: variables.$gray-300;
    bottom: -5px;
    border-radius: 5px;
    position: absolute;

    &::before,
    &:after {
        content: '';
        position: absolute;
        width: 34px;
        height: 1px;
        background: variables.$bg-default;
        left: 50%;
        transform: translateX(-50%);
        top: 2px;
    }

    &:after {
        top: 5px;
    }
}
