@use 'ngx-tethys/styles/variables.scss';

$wiki-outside-primary: #9c9cfb;
$wiki-primary: #6698ff;

$page-content-toc-width: 240px;
$anchor-link-level-padding: 15px;
$anchor-right: 20px; // windows scrollbar width
$anchor-top: 156px; /* pilot高度(56px) + 大纲与pilot距离100px = 156px */
$anchor-collapsed-width: 50px;
$anchor-link-base-padding: 20px;
$anchor-padding: 15px;
$page-content-padding-left: 28px; /* sidebar 折叠后与页面内容区的距离 */
$header-height: 52px;
$attachment-dialog-height: 600px;

$bg-transparent-primary: rgba(
    $color: variables.$primary,
    $alpha: 0.1
); // 文本模式通用背景色 （主色 0.1）
$bg-transparent-danger: rgba(
    $color: variables.$danger,
    $alpha: 0.1
);
$wiki-border-default: 1px solid variables.$gray-200; // 编辑器内元素默认边框
$border-radius-covering: 3px;

$wiki-relation-list: 'slate-element-relation-work-item-list ,.slate-element-relation-test-case-list,.slate-element-relation-idea-list,.slate-element-relation-ticket-list,.slate-element-relation-objective-list';

$page-content-width: 710px;
$page-content-base-padding: 48px;
$editable-width: 870px;
$editable-padding-left: 80px;
