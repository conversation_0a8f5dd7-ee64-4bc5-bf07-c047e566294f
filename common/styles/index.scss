@use 'sass:math';
@use 'ngx-tethys/styles/variables.scss';
@use '@worktile/theia/styles/variables.scss' as theia;
@use '../../plait-editor/styles/plait-editor.scss';
@use './variables.scss' as commonVariables;

@use '../components/common-components.scss';

@use '../plugins/video/video.component.scss';
@use '../plugins/attachment/attachment.component.scss';
@use '../plugins//audio/audio.component.scss';
@use '../plugins/emoji/emoji.component.scss';
@use '../plugins/relation-page/relation-page.component';
@use '../plugins/alert/alert.component.scss';
@use '../plugins/layout/component/layout.component.scss';
@use '../plugins/label/label.component.scss';
@use '../plugins/date/date.component.scss';
@use '../plugins/mention/suggestion.component.scss';
@use '../plugins/discussion/leaf/leaf.component';
@use '../plugins/search-replace/toolbar-item.component.scss';
@use '../plugins/dnd/component/dnd.component.scss';
@use '../plugins/heading-handle/heading-handle.component.scss';
@use '../plugins/toggle-list/components/list/list.component.scss';
@use '../plugins/preview-toolbar/preview-toolbar.component.scss';
@use '../plugins/formula/formula.component.scss';
@use '../plugins/formula/edit/edit.component.scss';
@use '../plugins/text-diagram/text-diagram.component.scss';
@use '../plugins/diagram-board/diagram-board.component.scss';
@use '../plugins/relation-page-tree/relation-page-tree.component.scss';
@use '../plugins/outline/outline.component.scss';
@use '../plugins/relation-report/relation-report.component.scss';
@use '../plugins/drawio/drawio.component.scss';
@use '../plugins/drawio/graph-editor/graph-editor.component.scss';

@use './space.scss';
@use './page.scss';
@use './editor.scss';
@use './fullscreen.scss';
@import '@tethys/pro/styles/index'; // pro 基础样式，音视频组件使用

.common-placeholder-loading {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 52px;
    border-radius: variables.$border-radius;
    background: variables.$gray-80;
    color: variables.$gray-500;

    &.text-mode-placeholder {
        width: 24px;
        height: 24px;
    }
}
// 关联组件 suggestion 搜索框样式
.relation-item-suggestion {
    position: relative;
    display: block;
    width: 100%;
    .relation-input-prefix-icon {
        left: 0.625rem;
        top: calc((100% - 24px) / 2);
        color: variables.$gray-500;
        position: absolute;
        width: auto;
    }
    .relation-input-search-control {
        padding-left: 36px;
        padding-right: 36px;
    }
}

.common-editing-sidebar {
    position: fixed;
    top: 130px;
    right: 30px;
    display: block;
    max-height: calc(100vh - 130px - 36px - 36px - 12px);
    width: 300px;
    z-index: 2;
    overflow: hidden;
    &.wiki-common-toc {
        .thy-anchor-wrapper {
            max-height: calc(
                100vh - 130px - 46px - 12px - 36px - 36px
            ) !important; // top 130px, toc height 30px padding-bottom 16px , back-to-top height 36px bottom 36px , 大纲距离 back-to-top 12px
        }
    }
}

.common-empty-align-center {
    position: relative;
    top: 50%;
    transform: translateY(-100%);
}

// 通用的鼠标 hover 样式
.cursor-pointer {
    cursor: pointer;
}
.cursor-default {
    cursor: default;
}

.wiki-outside-root {
    .outside-cursor-default {
        cursor: default !important;
    }
}

// 文本模式样式
.common-inline-text-mode {
    max-width: 100%;
    height: 28px;
    color: variables.$gray-800;
    display: inline-flex;
    align-items: center;

    .styx-business-object-brand {
        height: 28px;
        line-height: 1;
    }

    img {
        height: 16px;
        padding: 0 8px;
    }
    .text-block-title {
        color: variables.$gray-800;
        line-height: 22px;
        text-decoration: none;
        margin-right: 8px;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

// 音视频 通用样式
.slate-element-audio,
.slate-element-video {
    .file-uploading {
        border: none;
        border-radius: variables.$border-radius;
        overflow: hidden;
        .progress-operation-wrapper {
            overflow: hidden;
        }
        .progress-text {
            width: 35px;
            white-space: nowrap;
        }
    }
    .common-placeholder-loading img {
        height: 16px;
    }
}
.slate-element-audio:not(.text-mode),
.slate-element-video:not(.text-mode) {
    .content-container {
        .audio-card-content,
        .video-content {
            width: 100%; // 音视频默认宽度 300px，这里需要设置全宽
        }
    }
}
.slate-element-audio.text-mode,
.slate-element-video.text-mode {
    display: inline-flex;
    max-width: 100%;

    .content-container {
        display: inline-block;
        max-width: 100%;

        .common-inline-text-mode {
            max-width: 100%;
            height: 28px;
            display: flex;
            align-items: center;

            img {
                height: 16px;
                padding: 0 8px;
            }
        }
    }
}
// 超出 tooltip 展示
.text-block-title {
    color: variables.$gray-800 !important;
    line-height: 22px;
    text-decoration: none !important;
    margin-right: 8px;
    font-size: 14px;
}
