import { StyxRelationParamsInfo } from '@atinc/ngx-styx';
import { ResponseData } from '@atinc/ngx-styx/core';
import { RelationPages } from '../plugins/relation-page/type';
import { Id } from 'ngx-tethys/types';

export interface RelationParamsInfo<T extends string> extends StyxRelationParamsInfo {
    target_name?: T;
    team_id?: Id;
    version_id?: Id;
}

export class PageRelationReference {
    relation_work_items?: ResponseData;
    relation_test_cases?: ResponseData;
    relation_ideas?: ResponseData;
    relation_pages?: RelationPages;
    relation_tickets?: ResponseData;
    relation_objectives?: ResponseData;
}

export enum RelationItemState {
    empty,
    success,
    not_found,
    loading
}
