import { AttachmentEntity, Is } from '@atinc/ngx-styx';
import { CustomElementKinds } from '@worktile/theia';
import { AttachmentElement, AudioElement, VideoElement } from '@wiki/common/custom-types';

export type AttachmentScope = 'embedment' | undefined;

export interface FilePermissions {
    get?: Is;
    upload?: Is;
    delete?: Is;
    update?: Is;
    download?: Is;
}

export interface PageAttachmentEntity extends AttachmentEntity {
    attachment_scope?: AttachmentScope;
    permissions?: FilePermissions;
}
export interface FileInfo {
    node: VideoElement | AttachmentElement | AudioElement;
    file: File;
}
export interface AttachmentInfo {
    node: AttachmentElement;
    file: File;
}

export interface AudioInfo {
    node: AudioElement;
    file: File;
}
export interface VideoInfo {
    node: VideoElement;
    file: File;
}

export interface AttachmentPluginOptions {
    allowParentTypes?: CustomElementKinds[];
}
