import { Is, MemberInfo, TagInfo } from '@atinc/ngx-styx';
import { PLViewportMode } from '@plait-editor/types/editor';
import { BaseViewport, PlaitElement, PlaitTheme } from '@plait/core';
import { DiscussionInfo } from '@wiki/app/entities/page-discussion-info';
import { PageInfo, ReviewGuidelineConfig } from '@wiki/app/entities/page-info';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { TheDocument } from '../custom-types';
import { PageRelationReference } from '../interface/relation';
import { PageAttachmentEntity } from './base-file';
import { AITableViewFields, AITableViewRecords, AITableViews } from '@ai-table/utils';

export class PageState extends PageRelationReference {
    members?: MemberInfo[];
    space?: SpaceInfo;
    content?: WikiPageContent;
    parentPages?: PageInfo[];
    discussions?: DiscussionInfo[];
    page?: PageInfo;
    isShared?: boolean;
    isFullScreen?: boolean;
    isPublish?: boolean;
    spaceMembers?: MemberInfo[];
    tags?: TagInfo[];
    review_guideline_config?: ReviewGuidelineConfig;
    report_email?: string;
}

export type PageType = 'default' | 'draft' | 'stencil';

export interface WikiEditPageInfo {
    data: WikiEditData;
    stencilId?: string;
    relationData?: WikiEditDocumentRelationData;
    boardOptions?: BoardOptions;
}

export interface WikiEditData {
    value: WikiPageContent;
    isSameValue: boolean;
    name: string;
    emojiIcon?: string;
    isSameTitle?: boolean;
}

export interface BoardOptions {
    viewport?: BaseViewport;
    theme?: PlaitTheme;
    viewportMode?: PLViewportMode;
}

export class WikiEditDocumentRelationData extends PageRelationReference {
    attachments?: PageAttachmentEntity[];
}

export type WikiPageContent = TheDocument | PlaitElement[] | AITableContent;

export enum MaterialBelongTo {
    board = 1
}

export enum MaterialType {
    system = 1,
    customized = 2,
    thirdParty = 3
}

export enum MaterialCategory {
    teamwork = 1,
    productDesign = 2,
    techDevelopment = 3
}

export interface MaterialMenu {
    key: 'all' | 'custom';
    name: string;
}

export interface MaterialScene {
    key: MaterialCategory;
    name: string;
}

export class MaterialInfo {
    _id: string;
    name: string;
    description?: string;
    belong_to: MaterialBelongTo;
    cover: string;
    is_system: Is;
    category: MaterialCategory;
    content: PlaitElement[];
}

export interface AITableContent {
    fields: AITableViewFields;
    records: AITableViewRecords;
    views: AITableViews;
}
