import { PropertyColumn, PropertyInfo, StyxTranslateService } from '@atinc/ngx-styx';
import { TheExtensionMode } from './interface/extension-mode';
import { RelationIdeaListEditor } from './plugins/relation-idea-list/relation-idea-list.editor';
import { RelationObjectiveListEditor } from './plugins/relation-objective-list/relation-objective-list.editor';
import { RelationTestCaseListEditor } from './plugins/relation-test-case-list/relation-test-case-list.editor';
import { RelationTicketListEditor } from './plugins/relation-ticket-list/relation-ticket-list.editor';
import { RelationWorkItemListEditor } from './plugins/relation-work-item-list/relation-work-item-list.editor';
import { RelationListEditor, WikiPluginTypes } from './types';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

export enum ThyAlertType {
    success = 'success',
    warning = 'warning',
    danger = 'danger',
    info = 'info',
    emoji = 'emoji',
    none = 'none'
}

export enum RelationPageTreeLevel {
    one = 1,
    two = 2,
    three = 3,
    four = 4
}

export const getRelationPageTreeLevelMenus = (translate: StyxTranslateService) => {
    return [
        { name: translate.instant<I18nSourceDefinitionType>('wiki.plugins.outline.level1'), value: RelationPageTreeLevel.one },
        { name: translate.instant<I18nSourceDefinitionType>('wiki.plugins.outline.level2'), value: RelationPageTreeLevel.two },
        { name: translate.instant<I18nSourceDefinitionType>('wiki.plugins.outline.level3'), value: RelationPageTreeLevel.three },
        { name: translate.instant<I18nSourceDefinitionType>('wiki.plugins.outline.level4'), value: RelationPageTreeLevel.four }
    ];
};

export type RelationPropertyInfo = PropertyInfo & { width?: string | number };
export interface PageModeOption {
    mode?: TheExtensionMode;
    fetchId?: string;
    shortPageId?: string;
    shortSpaceId?: string;
}

export const wikiRelationListEditorMap: { [key: string]: RelationListEditor } = {
    [WikiPluginTypes.relationWorkItemList]: RelationWorkItemListEditor,
    [WikiPluginTypes.relationIdeaList]: RelationIdeaListEditor,
    [WikiPluginTypes.relationTestCaseList]: RelationTestCaseListEditor,
    [WikiPluginTypes.relationTicketList]: RelationTicketListEditor,
    [WikiPluginTypes.relationObjectiveList]: RelationObjectiveListEditor
};

export const DefaultPropertyColumns: { [key: string]: PropertyColumn[] } = {
    [WikiPluginTypes.relationWorkItemList]: [
        { property_key: 'identifier' },
        { property_key: 'title' },
        { property_key: 'state_id' },
        { property_key: 'assignee' },
        { property_key: 'priority' },
        { property_key: 'project_id' }
    ] as PropertyColumn[],
    [WikiPluginTypes.relationIdeaList]: [
        { property_key: 'identifier' },
        { property_key: 'title' },
        { property_key: 'state_id' },
        { property_key: 'assignee' },
        { property_key: 'priority' },
        { property_key: 'product_id' }
    ] as PropertyColumn[],
    [WikiPluginTypes.relationTestCaseList]: [
        { property_key: 'identifier' },
        { property_key: 'title' },
        { property_key: 'important_level' },
        { property_key: 'maintenance_uid' },
        { property_key: 'status' },
        { property_key: 'test_library_id' }
    ] as PropertyColumn[],
    [WikiPluginTypes.relationTicketList]: [
        { property_key: 'identifier' },
        { property_key: 'title' },
        { property_key: 'state_id' },
        { property_key: 'assignee' },
        { property_key: 'priority' },
        { property_key: 'product_id' }
    ] as PropertyColumn[],
    [WikiPluginTypes.relationObjectiveList]: [
        { property_key: 'name' },
        { property_key: 'assignee' },
        { property_key: 'state' },
        { property_key: 'rate' },
        { property_key: 'due_at' },
        { property_key: 'period_id' }
    ] as PropertyColumn[]
};

export const DefaultUnremovableProperties = ['identifier', 'title'];
