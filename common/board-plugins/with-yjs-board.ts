import { WithYjsOptions, YjsEditor } from '@worktile/y-slate/main/plugin';
import { PlaitTheme } from '@plait/core';
import { YjsBoard } from '../yjs-plait/board/yjs-board';
import { SharedTheme } from '../yjs-plait/yjs-plait.type';
import { DEFAULT_BOARD_THEME } from '@plait-editor/constants/default';

export const addBoardNormalize = (board: YjsBoard) => {
    board.isNormalizing = () => false;
    board.setNormalizing = () => {};
    board.withoutNormalizing = (fn: () => void) => {
        fn();
    };
    board.normalize = () => {};
};

export function withYjsBoard<T extends YjsBoard>(
    board: T,
    sharedTheme: SharedTheme,
    initThemeValue: PlaitTheme,
    { isSynchronizeValue = true }: WithYjsOptions
): T {
    const { onChange } = board;

    let isInitialized = false;

    addBoardNormalize(board);

    board.sharedTheme = sharedTheme;

    if (isSynchronizeValue) {
        board.sharedTheme.set('theme', initThemeValue);
        board.theme = board.sharedTheme.get('theme') as PlaitTheme;
        isInitialized = true;
    }

    sharedTheme.observeDeep(events => {
        if (!YjsBoard.isLocal(board)) {
            if (!isInitialized) {
                board.theme = (board.sharedTheme.get('theme') as PlaitTheme) || DEFAULT_BOARD_THEME;
                isInitialized = true;
            } else {
                if (YjsEditor.isUndo(board as any)) {
                    YjsBoard.applyThemeEvents(board, events);
                } else {
                    YjsBoard.asRemote(board, () => {
                        YjsBoard.applyThemeEvents(board, events);
                    });
                }
            }
        }
    });

    board.onChange = () => {
        if (!YjsBoard.isRemote(board) && !YjsEditor.isUndo(board as any) && isInitialized) {
            YjsBoard.applyPlaitOperations(board, board.operations);
        }
        onChange();
    };

    return board;
}
