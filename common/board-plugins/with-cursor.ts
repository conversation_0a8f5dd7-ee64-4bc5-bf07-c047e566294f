import { PlaitOperation } from '@plait/core';
import { YjsBoard } from '../yjs-plait/board/yjs-board';
import { AWARENESS, CursorBoard } from '../yjs-plait/board/cursor-board';

export const withCursor = (cursorBoard: YjsBoard) => {
    const _cursorBoard = cursorBoard as CursorBoard;
    _cursorBoard.awareness = _cursorBoard.provider.awareness;
    AWARENESS.set(_cursorBoard, _cursorBoard.awareness);

    const { onChange } = _cursorBoard;
    _cursorBoard.onChange = () => {
        const isSetViewport = _cursorBoard.operations.some(op => (op as PlaitOperation).type === 'set_viewport');
        if (!isSetViewport) {
            setTimeout(() => {
                CursorBoard.updateCursor(_cursorBoard);
            }, 0);
        }
        onChange();
    };

    return _cursorBoard;
};
