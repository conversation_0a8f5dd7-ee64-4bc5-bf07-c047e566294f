function isValid(str: string): boolean {
    return str && typeof str === 'string' && str.length > 0;
}

/**
 * \r = CR (Carriage Return) → Used as a new line character in Mac OS before X
 * \n = LF (Line Feed) → Used as a new line character in Unix/Mac OS X
 * \r\n = CR + LF → Used as a new line character in Windows
 */
const CR = /\r/g;
const CRLF = /\r\n/g;
const LF = /\n/g;

function wordsCompute(str: string): number {
    let sLen = 0;
    // 先将回车换行符做特殊处理
    str = str.replace(/(\r\n+|\s+|　+)/g, '龘');
    // 处理英文字符数字，连续字母、数字、英文符号视为一个单词
    str = str.replace(/[\x00-\xff]/g, 'm');
    // 合并字符m，连续字母、数字、英文符号视为一个单词
    str = str.replace(/m+/g, '*');
    // 去掉回车换行符
    str = str.replace(/龘+/g, '');
    // 过滤 emoji
    str = str.replace(/\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g, '');
    // 返回字数
    sLen = str.length;
    return sLen;
}

/**
 * Extracts new line used char in a given string.
 */
function getNewLineChar(str: string) {
    let lineChar = LF;

    if (CR.test(str)) {
        lineChar = CR;
    } else if (CRLF.test(str)) {
        lineChar = CRLF;
    }

    return lineChar;
}

/**
 * 计算给定字符串的行数、字数、字符数、空格数
 */
export function textics(str: string) {
    let lines = 0;
    let words = 0;
    let chars = 0;
    let spaces = 0;

    if (!isValid(str)) {
        return {
            lines,
            words,
            chars,
            spaces
        };
    }

    const regNewLine = getNewLineChar(str);
    const regSpace = /\s/g;

    words = wordsCompute(str);

    /**
     * 获取总字符串长度
     */
    const { length: totalLength } = str;
    const splittedByLines = str.split(regNewLine);

    ({ length: lines } = splittedByLines);

    splittedByLines.forEach(line => {
        const { length: lineLength } = line;
        const trimmed = line.trim();
        const { length: trimmedLength } = trimmed;

        spaces += lineLength - trimmedLength;

        if (trimmedLength !== 0) {
            const splittedBySpaces = trimmed.split(regSpace);
            const { length: wordsInLine } = splittedBySpaces;

            if (wordsInLine > 1) {
                /**
                 * ["w1", "w2", "w3"].length = 3.
                 * How many spaces? length -1
                 */
                spaces += wordsInLine - 1;
            }
        }
    });

    if (words > 0) {
        chars = totalLength - spaces;

        if (lines > 1) {
            chars -= lines - 1;
        }
    }

    return {
        lines,
        words,
        chars,
        spaces
    };
}

export default { textics };
