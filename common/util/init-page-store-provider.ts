import { PageStore } from '../stores/page.store';
import { RelationPageStore } from '../plugins/relation-page/relation-page.store';
import { RelationWorkItemStore } from '../plugins/relation-work-item/relation-work-item.store';
import { RelationTestCaseStore } from '../plugins/relation-test-case/relation-test-case.store';
import { RelationIdeaStore } from '../plugins/relation-idea/relation-idea.store';
import { RelationTicketStore } from '../plugins/relation-ticket/relation-ticket.store';
import { RelationObjectiveStore } from '../plugins/relation-objective/relation-objective.store';

export const initPageStoreProviders = store => {
    return [
        RelationPageStore,
        RelationWorkItemStore,
        RelationTestCaseStore,
        RelationIdeaStore,
        RelationTicketStore,
        RelationObjectiveStore,
        store,
        {
            provide: PageStore,
            useExisting: store
        }
    ];
};
