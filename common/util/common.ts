import { Editor, NodeEntry, Text, Node as SlateNode, Element } from 'slate';
import { AngularEditor, hotkeys } from 'slate-angular';
import {
    ElementKinds,
    coercePixelsFromCssValue,
    TheEditor,
    isCleanEmptyParagraph,
    createEmptyParagraph,
    TheModeType
} from '@worktile/theia';
import { Discussion, NodeLevel } from '../custom-types';
import {
    AppRootContext,
    getObjectApplicationByType,
    hasPermissionTransform,
    StyxTranslateService,
    UserCustomizationContext
} from '@atinc/ngx-styx';
import { WikiPluginContext } from '../services/context/plugin.context';
import { WikiPluginTypes } from '../types/editor.types';
import { ActivatedRoute } from '@angular/router';
import { isSharedMode } from './extension-mode';
import { TheExtensionMode } from '../interface/extension-mode';
import { getCommonRelationOption } from '../types/relation-types';
import { WikiPageContent } from '../interface/page';
import { MindLayoutType } from '@plait/layouts';
import { PageTypes } from '@wiki/app/constants/page';
import { PageInfo } from '@wiki/app/entities/page-info';
import { wikiPermissionPoints } from '../constants/permission';
import { createDefaultMind } from '../plugins/diagram-board/creation';
import { DOMElement } from 'slate-dom';

export const isEmptyDocument = (document: WikiPageContent) => {
    if (document && Array.isArray(document) && document.length === 1) {
        const paragraph = document[0];
        if (paragraph.type === ElementKinds.paragraph && paragraph.children.length === 1 && (paragraph.children[0] as Text).text === '') {
            return true;
        }
    }
    if (!document) {
        return true;
    }
    return false;
};

export const isMaxView = (page: PageInfo, userCustomizationContext: UserCustomizationContext) => {
    if (!page?.viewport && userCustomizationContext) {
        return userCustomizationContext.getCustomization()['is_page_wide_screen'] as boolean;
    }
    return page?.viewport === 'full-width';
};

export function isLongId(id: string) {
    return id.length === 24;
}

export const getDecorationSelection = (editor: TheEditor, nativeSelection: Selection) => {
    if (nativeSelection.isCollapsed || nativeSelection.type !== 'Range') {
        return null;
    }

    const nativeRange = nativeSelection.getRangeAt(0);
    const editorRange = AngularEditor.toSlateRange(editor, nativeRange, { exactMatch: false, suppressThrow: false });
    const anchorNode = Editor.above(editor, { at: editorRange.anchor }) as NodeEntry<Element>;
    const selectText = Editor.string(editor, editorRange);
    const rootNode = SlateNode.get(editor, [editorRange.anchor.path[0]]) as Element;

    if (!anchorNode) {
        return null;
    }

    const discussion: Discussion = {
        quote: selectText,
        lowest_node_key: anchorNode[0].key as string,
        node_key: rootNode.key as string,
        range: {
            anchor: {
                path: editorRange.anchor.path.slice(-1),
                offset: editorRange.anchor.offset
            },
            focus: {
                path: editorRange.focus.path.slice(-1),
                offset: editorRange.focus.offset
            }
        }
    };
    return {
        selection: editorRange,
        discussion
    };
};

export const isDirectionKeydown = (event: KeyboardEvent): boolean => {
    const isMoveUp = hotkeys.isMoveUp(event);
    const isMoveDown = hotkeys.isMoveDown(event);
    const isMoveBackward = hotkeys.isMoveBackward(event);
    const isMoveForward = hotkeys.isMoveForward(event);
    return isMoveUp || isMoveDown || isMoveBackward || isMoveForward;
};

export const placeholderDecorate = (editor: TheEditor, placeholder, emptyDocumentPlaceholder) => {
    const cursorAnchor = editor.selection?.anchor;
    if (!editor?.readonly) {
        if (isEmptyDocument(editor.children as Element[])) {
            const start = Editor.start(editor, []);
            return [
                {
                    placeholder: emptyDocumentPlaceholder,
                    anchor: start,
                    focus: start
                }
            ];
        } else if (cursorAnchor && isCleanEmptyParagraph(editor)) {
            const start = Editor.start(editor, cursorAnchor);
            return [
                {
                    placeholder: placeholder,
                    anchor: start,
                    focus: start
                }
            ];
        }
    }
    return [];
};

export function findRelativeElementByPoint(editableElement: DOMElement, editor: TheEditor, x: number, y: number, mode?: NodeLevel) {
    const rectEditable = editableElement.getBoundingClientRect();
    if (x > rectEditable.x && x < rectEditable.x + rectEditable.width) {
        let { paddingLeft } = window.getComputedStyle(editableElement, null);
        const paddingLeftPixels = coercePixelsFromCssValue(paddingLeft);
        const startX = rectEditable.left + paddingLeftPixels + 10; // 增加缓冲值 10 ，否则当容器存在 border 时（1px）无法得到正确结果
        let relativeElement = document.elementFromPoint(mode === 'highest' ? startX : x, y);
        return relativeElement;
    }
    return null;
}

export function findNodeEntryByPoint(
    editableElement: DOMElement,
    editor: TheEditor,
    x: number,
    y: number,
    mode?: NodeLevel,
    target = '[data-slate-node="element"]'
): NodeEntry<Element> | null {
    let rootElement: DOMElement = null;
    let relativeElement = findRelativeElementByPoint(editableElement, editor, x, y, mode);
    // 获取最顶层的DOM
    if (mode === 'highest') {
        while (relativeElement && editableElement.contains(relativeElement)) {
            relativeElement = relativeElement.closest(target);
            if (relativeElement) {
                rootElement = relativeElement;
                relativeElement = relativeElement.parentElement;
            }
        }
    }

    if (!mode) {
        if (relativeElement && editableElement.contains(relativeElement)) {
            relativeElement = relativeElement.closest(target);
            rootElement = relativeElement;
        }
    }

    if (rootElement) {
        const node = AngularEditor.toSlateNode(editor, rootElement) as Element;
        const path = AngularEditor.findPath(editor, node);
        return [node, path];
    }
    return null;
}

export function isSlateHeading(node: HTMLElement) {
    return /slate\-element\-heading\-/.test(node.className);
}

export const createBackgroundWatermark = (ele: HTMLElement, content: string) => {
    const [degree, color, fontSize, textLineHeight, gutter] = [15, 'rgba(51, 51, 51, 0.12)', 12, 20, [250, 200]];
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const contentArr = content.split('\\n');
    const canvasWidth = Math.max(...contentArr.map(k => ctx.measureText(k).width));
    const canvasHeight = Math.sin(degree) * canvasWidth;

    canvas.setAttribute('width', '' + (canvasWidth + gutter[0]));
    canvas.setAttribute('height', '' + (canvasHeight + gutter[1] + textLineHeight * (contentArr.length - 1)));

    ctx.font = `${fontSize}px microsoft yahei`;
    ctx.textAlign = 'center' as CanvasTextAlign;
    ctx.textBaseline = 'middle' as CanvasTextBaseline;
    ctx.fillStyle = color;
    ctx.rotate(0 - (degree * Math.PI) / 180);
    contentArr.map((k, i) => {
        ctx.save();
        ctx.fillText(k, Math.abs(Math.tan(degree) * canvasHeight), canvasHeight + textLineHeight * i);
        ctx.restore();
    });
    ele.style.backgroundImage = `url(${canvas.toDataURL()})`;
    ele.style.backgroundSize = '0px';
};

export const verifyAvailableAppByBroadObjectType = (editor: TheEditor, broadObjectType: string, appRootContext?: AppRootContext) => {
    const applicationType = getObjectApplicationByType(broadObjectType);
    const context = appRootContext || editor.injector.get(AppRootContext);
    return context.team.available_applications?.some(app => app.application === applicationType);
};

export const verifyAvailableAppAndPermission = (translate: StyxTranslateService, editor: TheEditor, pluginType: WikiPluginTypes) => {
    const relationOption = getCommonRelationOption(translate)[pluginType];
    const isAvailableApp = verifyAvailableAppByBroadObjectType(editor, relationOption.broadObjectType);
    const pageContext: any = editor.injector.get(WikiPluginContext);
    return isAvailableApp && hasPermissionTransform(pageContext.permissions, wikiPermissionPoints, relationOption.permission);
};

export const verifyAvailableAppByPluginType = (
    translate: StyxTranslateService,
    editor: TheEditor | null,
    pluginType: WikiPluginTypes,
    appRootContext?: AppRootContext
) => {
    const relationOption = getCommonRelationOption(translate)[pluginType];
    return verifyAvailableAppByBroadObjectType(editor, relationOption.broadObjectType, appRootContext);
};

export const getOutsideOptions = (mode: TheModeType, route: ActivatedRoute) => {
    const options: {
        mode?: TheExtensionMode;
        shortPageId?: string;
        shortSpaceId?: string;
        fetchId?: string;
    } = {
        mode: TheExtensionMode.outsidePage,
        shortPageId: route.snapshot.paramMap.get('pageIdOrShortId')
    };
    const configMode = mode as TheExtensionMode;
    if (configMode === TheExtensionMode.print) {
        options.mode = TheExtensionMode.print;
        return options;
    }
    if (isSharedMode(configMode)) {
        options.fetchId = options.shortPageId;

        if (mode === TheExtensionMode.outsideSpace) {
            options.mode = TheExtensionMode.outsideSpace;
            options.shortSpaceId = route.parent.snapshot.paramMap.get('sid');
            options.fetchId = options.shortSpaceId;
        }
    }
    return options;
};

export const getDomainUrl = (baseUrlFormat: string, domain: string): string => {
    return baseUrlFormat.replace('{0}', domain);
};

export function createDefaultContent(translate: StyxTranslateService, type: PageTypes): WikiPageContent {
    if (type === PageTypes.board) {
        return [createDefaultMind(translate, [230, 208], 4, MindLayoutType.right)];
    }
    return [createEmptyParagraph()];
}
