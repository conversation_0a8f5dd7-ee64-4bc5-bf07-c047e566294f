import { HeaderLevelMap } from '@worktile/theia';
import { Editor, Node } from 'slate';
import { TheDocument } from '../custom-types';

export interface HeadingType {
    level: number;
    text: string;
    key: string;
    numbered?: string;
    parentKey?: string;
}

export const extractHeading: (editor: Editor, value: TheDocument) => HeadingType[] = (editor: Editor, value: TheDocument) => {
    return (value || [])
        .filter(node => {
            return HeaderLevelMap[node.type] !== undefined;
        })
        .filter(node => {
            const isVisible = editor.isVisible(node);
            const nodeText = Node.string(node);
            return isVisible && nodeText.trim() !== '';
        })
        .map(node => {
            const nodeText = Node.string(node);
            return { level: HeaderLevelMap[node.type], text: nodeText, key: node.key };
        });
};
