import { TheExtensionMode } from '../interface/extension-mode';

export const isSharedMode = (mode: TheExtensionMode) => {
    if (mode === TheExtensionMode.outsidePage || mode === TheExtensionMode.outsideSpace) {
        return true;
    }
    return false;
};

export const isMobileMode = (mode: TheExtensionMode | string) => {
    if (mode === TheExtensionMode.mobile || mode === TheExtensionMode.applet) {
        return true;
    }
    return false;
};
