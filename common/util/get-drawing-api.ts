import { AppRootContext } from '@atinc/ngx-styx';
import { API_PREFIX } from '@wiki/app/constants';
import { getDomainUrl } from '@wiki/app/util/common';
import { TheExtensionMode } from '../interface/extension-mode';

export const EXPORT_DRAWING_TOKEN = 'A41955BB-D875-9D95-DA68-847594BB8AC7';

export interface CommonDrawingOptions {
    appRootContext: AppRootContext;
    mode: string;
    type: string;
    spaceId: string;
    pageId: string;
    printResourceType?: string;
    isOnPremises?: boolean;
}

export const getDrawingApi = (options: CommonDrawingOptions): string => {
    const { appRootContext, mode, type, spaceId, pageId, printResourceType = 'svg' } = options;
    const isOnPremises = appRootContext?.globalInfo?.config?.isOnPremises;
    const baseUrlFormat = appRootContext?.globalInfo?.config?.baseUrlFormat;
    const domain = appRootContext?.team?.domain;
    const prefix = getDomainUrl(baseUrlFormat, domain);
    let apiPrefix = API_PREFIX;

    if (isOnPremises) {
        apiPrefix = `${prefix}${API_PREFIX}`;
    }
    switch (mode) {
        case TheExtensionMode.print:
            return `${apiPrefix}/drawing-${EXPORT_DRAWING_TOKEN}/${type}/${printResourceType}`;

        case TheExtensionMode.outsidePage:
            return `${apiPrefix}/pages/shared/${pageId}/drawing/${type}/svg`;

        case TheExtensionMode.outsideSpace:
            return `${apiPrefix}/spaces/shared/${spaceId}/drawing/${type}/svg`;

        default:
            return `${prefix}${API_PREFIX}/drawing/${type}/${printResourceType}`;
    }
};
