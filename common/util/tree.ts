import { Is } from '@atinc/ngx-styx';
import { CreatePageType, PageTypes } from '@wiki/app/constants/page';
import { PageInfo } from '@wiki/app/entities/page-info';
import { ThyTreeNodeData } from 'ngx-tethys/tree';

export function flattenTreeNodes(nodes: ThyTreeNodeData[], flattenedNodes = []) {
    for (let index = 0; index < nodes.length; index++) {
        const element = nodes[index];
        flattenedNodes.push(element);
        if (element.children && element.children.length > 0) {
            flattenTreeNodes(element.children, flattenedNodes);
        }
    }
    return flattenedNodes as ThyTreeNodeData[];
}

// 取上方的页面，可以是非同级
export function getPreviousNodeByKey(pageNodes: ThyTreeNodeData[], keyName: string, keyValue: string): ThyTreeNodeData {
    const flattenNodes = flattenTreeNodes(pageNodes);
    const activeIndex = flattenNodes.findIndex(node => node[keyName] === keyValue);

    if (activeIndex === -1) {
        return null;
    }

    const previousNodes = flattenNodes.slice(0, activeIndex).reverse();
    const previousNode = previousNodes.find(node => node.type !== PageTypes.group);
    return previousNode || null;
}

// 取上方的页面，只能是同级
export function getPreviousBrotherByKey(
    pageNodes: ThyTreeNodeData[],
    keyName: string,
    parentValue: string,
    keyValue: string
): ThyTreeNodeData {
    const flattenNodes = flattenTreeNodes(pageNodes);
    const parentNode = flattenNodes.find(node => node[keyName] === parentValue);
    const activeIndex = parentNode.children.findIndex(node => node[keyName] === keyValue);
    return parentNode.children[activeIndex - 1] || null;
}

export function filterFirstVisibility(pages: PageInfo[] = []): PageInfo {
    let page = null;
    for (let i = 0; i < pages.length; i++) {
        if (pages[i].is_visibility === Is.yes) {
            page = pages[i];
            break;
        }

        page = filterFirstVisibility(pages[i].children);
        if (page) {
            break;
        }
    }
    return page;
}

export function getAfterId(page: PageInfo, pageType: CreatePageType, treePages: PageInfo[]): string {
    let pageNodes = [...treePages];
    let afterId = page._id;
    if (pageType && pageType === CreatePageType.Above) {
        if (page.parent_id) {
            const node = getPreviousBrotherByKey(pageNodes, '_id', page.parent_id, page._id);
            afterId = node && node._id !== page.parent_id ? node._id : null;
        } else {
            const index = pageNodes.findIndex(item => item._id === page._id);
            afterId = index > 0 ? pageNodes[index - 1]._id : null;
        }
    }
    return afterId;
}
