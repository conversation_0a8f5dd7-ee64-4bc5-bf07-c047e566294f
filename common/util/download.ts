import { DateFormatPipe } from '@atinc/ngx-styx';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { TinyDate } from 'ngx-tethys/util';

export function download(blob: Blob | MediaSource, filename: string) {
    const a = document.createElement('a');
    const url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = filename;
    document.body.append(a);
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
}

export function downloadBase64Image(base64Data: string, pageName: string, translate: StyxTranslateService) {
    if (!base64Data.startsWith('data:image/')) {
        return;
    }

    const matches = base64Data.match(/^data:([^;]+);base64,(.+)$/);
    if (!matches) {
        console.error('Invalid base64 data format');
        return;
    }

    const mimeType = matches[1];
    if (mimeType === 'image/png') {
        const img = new Image();
        img.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const scale = 2;
            const padding = 40;

            canvas.width = (img.width + padding * 2) * scale;
            canvas.height = (img.height + padding * 2) * scale;
            canvas.style.width = `${img.width + padding * 2}px`;
            canvas.style.height = `${img.height + padding * 2}px`;

            if (ctx) {
                ctx.scale(scale, scale);
                ctx.fillStyle = '#FFFFFF';
                ctx.fillRect(0, 0, canvas.width / scale, canvas.height / scale);
                ctx.drawImage(img, padding, padding);
            }

            const dateTimeAutoFormat = new DateFormatPipe();
            const date = dateTimeAutoFormat.transform(new TinyDate().getTime(), 'yyyyMMdd');
            const imageName = `${pageName}-${translate.instant('wiki.plaitEditor.toolbar.image')}-${date}.png`;

            canvas.toBlob(blob => {
                if (blob) {
                    download(blob, imageName);
                }
            }, 'image/png');
        };
        img.src = base64Data;
    }
}
