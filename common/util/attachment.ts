import { FeedInfo, Is } from '@atinc/ngx-styx/core';
import { produce } from 'ngx-tethys/util';
import { PageAttachmentEntity } from '../interface';

export const updateAttachment = (attachments: PageAttachmentEntity[], feed: FeedInfo) => {
    const attachment = attachments.find(attachment => attachment.file_id === feed?.ids[0]);
    if (attachment) {
        attachments = produce(attachments).update(attachment._id, {
            title: feed?.data?.title || attachment.title,
            addition: {
                ...attachment.addition,
                size: feed?.data?.size || attachment.addition.size
            }
        });
        return attachments;
    }
};

export const updateAttachmentPermission = (attachments: PageAttachmentEntity[], feed: FeedInfo) => {
    const { attachment_id, latest_version_file_id } = feed.data;
    const attachment = attachments.find(attachment => attachment._id === attachment_id);
    if (attachment && attachment.file_id !== latest_version_file_id) {
        attachments = produce(attachments).update(attachment._id, {
            permissions: {
                ...attachment.permissions
            }
        });
        return attachments;
    }
};
