import { Descendant, Element } from 'slate';
import {
    CodeElement,
    ElementKinds,
    ImageElement,
    InlineCodeElement,
    LinkElement,
    ListItemElement,
    TableElement,
    TodoItemElement
} from '@worktile/theia';
import { WikiPluginTypes } from '@wiki/common/types';
import { <PERSON>downHelper } from './markdown-helper';
import { DateElement, FormulaElement, LabelElement, LayoutElement, TextDiagramElement, ToggleListElement } from '@wiki/common/custom-types';
import moment from 'moment';
import { TextElement } from '@atinc/selene';

class MarkdownTransformer {
    transformMarkdown(content: Descendant[]) {
        return this.transform(content as Element[]).join('\r\n\r\n');
    }

    transform(nodes: Element[], level: number = 0): string[] {
        const result: string[] = [];
        for (const node of nodes) {
            const { type = 'text' } = node;
            switch (type) {
                case ElementKinds.paragraph:
                    result.push(this.transformParagraph(node));
                    break;
                case ElementKinds.heading_1:
                case ElementKinds.heading_2:
                case ElementKinds.heading_3:
                case ElementKinds.heading_4:
                case ElementKinds.heading_5:
                case ElementKinds.heading_6:
                    result.push(this.transformHeading(node));
                    break;
                case ElementKinds.checkItem:
                    result.push(this.transformCheckItem(node));
                    break;
                case ElementKinds.bulletedList:
                case ElementKinds.numberedList:
                    result.push(this.transformList(node, level));
                    break;
                case ElementKinds.table:
                    result.push(this.transformTable(node));
                    break;
                case WikiPluginTypes.layout:
                    result.push(this.transformLayout(node));
                    break;
                case WikiPluginTypes.alert:
                    result.push(this.transformAlert(node));
                    break;
                case ElementKinds.blockquote:
                    result.push(this.transformBlockQuote(node));
                    break;
                case ElementKinds.code:
                    result.push(this.transformCode(node));
                    break;
                case ElementKinds.image:
                    result.push(this.transformImage(node));
                    break;
                case ElementKinds.hr:
                    result.push(this.transformHr());
                    break;
                case 'text':
                    result.push(MarkdownHelper.makeText(node as TextElement));
                    break;
                case ElementKinds.link:
                    result.push(this.transformLink(node));
                    break;
                case WikiPluginTypes.label:
                    result.push((node as LabelElement).label);
                    break;
                case WikiPluginTypes.date:
                    result.push(moment((node as DateElement).date * 1000).format('YYYY-MM-DD'));
                    break;
                case ElementKinds.inlineCode:
                    result.push(this.transformInlineCode(node));
                    break;
                case WikiPluginTypes.toggleList:
                    result.push(this.transformToggleList(node));
                    break;
                case WikiPluginTypes.toggleList:
                    result.push(this.transformToggleList(node));
                    break;
                case WikiPluginTypes.formula:
                    result.push(this.transformFormula(node));
                    break;
                case WikiPluginTypes.textDiagram:
                    result.push(MarkdownHelper.makeCode((node as TextDiagramElement).content));
                    break;
                default:
                    break;
            }
        }
        return result;
    }

    transformHr() {
        return MarkdownHelper.makeHr();
    }

    transformImage(node: Element) {
        const imageNode = node as ImageElement;
        return MarkdownHelper.makeImage(imageNode.name, imageNode.originUrl);
    }

    transformParagraph(node: Element) {
        let result = '';
        if (node.children) {
            result += this.transform(node.children as Element[]).join('  ');
        }
        return result;
    }

    transformCheckItem(node: Element) {
        let result = '';
        if (node.children) {
            result += MarkdownHelper.makeCheckItem(
                this.transform(node.children as Element[]).join('  '),
                (node as TodoItemElement).checked as boolean
            );
        }
        return result;
    }

    transformLink(node: Element) {
        let result = '';
        for (const child of node.children) {
            result += MarkdownHelper.makeText(child as TextElement);
        }
        return MarkdownHelper.makeLink(result, (node as LinkElement).url as string);
    }

    transformCode(node: Element) {
        return MarkdownHelper.makeCode((node as CodeElement).content as string);
    }

    transformHeading(node: Element) {
        let result = '';
        const levelMap = { one: 1, two: 2, three: 3, four: 4, five: 5, six: 6 };
        const level = node.type.toString().split('-')[1];
        if (node.children) {
            result += this.transform(node.children as Element[]).join('  ');
        }

        return MarkdownHelper.makeHeading(result, levelMap[level]);
    }

    transformBlockQuote(node: Element) {
        let result = '';
        if (node.children) {
            result += this.transform(node.children as Element[]).join('  ');
        }
        return MarkdownHelper.makeQuote(result);
    }

    transformList(node: Element, level: number = 0) {
        let result = '';

        const ordered = node.type === ElementKinds.numberedList;
        for (const listItem of node.children) {
            const listItemResult = this.transform((listItem as ListItemElement).children, level + 1);
            result += listItemResult.map(s => MarkdownHelper.makeList(s, ordered, level)).join('');
        }

        return result;
    }

    transformLayout(node: Element) {
        let result = '';
        for (const child of node.children) {
            result += this.transform((child as LayoutElement).children).join('\r\n');
        }
        return result;
    }

    transformToggleList(node: Element) {
        let result = '';
        for (const child of node.children) {
            result += this.transform((child as ToggleListElement).children).join('\r\n');
        }
        return result;
    }

    transformAlert(node: Element) {
        return this.transform(node.children as Element[]).join('\r\n');
    }

    transformTable(node: Element) {
        const data = [];
        const alignRow = [];

        for (const [index, row] of (node as TableElement).children.entries()) {
            const { header } = row;
            const rowData = [];
            for (const cell of row.children) {
                rowData.push(this.transform(cell.children as Element[]));
                if (header || index === 0) {
                    const { align } = cell;
                    alignRow.push(align || ' ');
                }
            }
            data.push(rowData);
        }
        return MarkdownHelper.makeTable(data, alignRow);
    }

    transformInlineCode(node: Element) {
        let result = this.transform((node as InlineCodeElement).children as Element[]).filter(s => !(s === null || s === undefined));
        return MarkdownHelper.makeInlineCode(result.join(''));
    }

    transformFormula(node: Element) {
        return MarkdownHelper.makeFormula((node as FormulaElement).content);
    }
}

export default new MarkdownTransformer();
