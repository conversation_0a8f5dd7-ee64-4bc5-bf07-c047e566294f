import { TextElement } from '@atinc/selene';
import { Alignment } from '@worktile/theia';

export class MarkdownHelper {
    constructor() {}

    static makeText(element: TextElement) {
        const { text, bold, italic, strike } = element;
        let result = '';
        if (text) {
            result = text as string;
            if (bold) {
                result = `**${result}**`;
            }
            if (italic) {
                result = `*${result}*`;
            }
            if (strike) {
                result = `~~${result}~~`;
            }
        }

        return result.replace('\n', '  \n');
    }

    static makeHeading(text: string, level: number = 1) {
        return `${'#'.repeat(level)} ${text}`;
    }

    static makeQuote(text: string) {
        return `> ${text}`;
    }

    static makeCheckItem(text: string, checked?: boolean) {
        return `- [${checked ? 'x' : ' '}] ${text}   `;
    }

    static makeCode(text: string) {
        return `\`\`\`\r\n${text}\r\n\`\`\``;
    }

    static makeHr() {
        return `---\r\n`;
    }

    static makeLink(text: string, url: string) {
        return `[${text}](${url})`;
    }

    static makeImage(text: string, url: string) {
        return `![${text}](${url})`;
    }

    static makeList(text: string, ordered: boolean, level: number = 0) {
        const indent = ' '.repeat(level * 4);
        const prefix = ordered ? '1.' : '-';

        return text.trimStart().startsWith(prefix) ? text : `${indent}${prefix} ${text}\r\n`;
    }

    static makeTable(data: string[][], aligns: Alignment[]) {
        let result = '';
        for (let i = 0; i < data.length; i++) {
            const row = data[i];
            result += '|' + row.join('|') + '|\r\n';
            if (i === 0) {
                const alignRow = [];
                for (const align of aligns) {
                    let content = '---';
                    switch (align) {
                        case Alignment.left:
                            content = ':' + content;
                            break;
                        case Alignment.center:
                            content = ':' + content + ':';
                            break;
                        case Alignment.right:
                            content = content + ':';
                            break;
                        default:
                            break;
                    }
                    alignRow.push(content);
                }
                result += '|' + alignRow.join('|') + '|\r\n';
            }
        }
        return result;
    }

    static makeInlineCode(text: string) {
        return `\`${text}\``;
    }

    static makeFormula(text: string) {
        return `$${text}$`;
    }
}
