import { PlaitBoard, ToImageOptions, toImage } from '@plait/core';

export const base64ToBlob = (base64: string) => {
    let arr = base64.split(','),
        fileType = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        l = bstr.length,
        u8Arr = new Uint8Array(l);

    while (l--) {
        u8Arr[l] = bstr.charCodeAt(l);
    }
    return new Blob([u8Arr], {
        type: fileType
    });
};

export const boardToImage = (board: PlaitBoard, options: ToImageOptions = {}) => {
    return toImage(board, {
        fillStyle: 'transparent',
        inlineStyleClassNames: '.extend,.emojis,.text',
        padding: 20,
        ratio: 4,
        ...options
    });
};
