import { coercePixelsFromCssValue } from '@worktile/theia';
import { NodeEntry } from 'slate';
import { ZOOM } from '../constants/default';
import { WikiElement } from '../custom-types';
import { HAS_BORDER_TYPES, RELATED_TYPES } from '../types/editor.types';

export function getOffsetTopAndOffsetLeft(
    rootNode: HTMLElement,
    nodeEntry: NodeEntry<WikiElement>,
    iconSize: number,
    iconOffset: number,
    spacing: number,
    isFullScreen?: boolean
) {
    const firstTextElement = rootNode.querySelector('[data-slate-node="text"]');
    const lineHeight = firstTextElement && coercePixelsFromCssValue(window.getComputedStyle(firstTextElement)['lineHeight']);
    const marginLeft = coercePixelsFromCssValue(window.getComputedStyle(rootNode)['marginLeft']);
    const paddingTop = coercePixelsFromCssValue(window.getComputedStyle(rootNode)['paddingTop']);
    const position = getLineHeightHalfPosition(nodeEntry, lineHeight, iconSize, isFullScreen);
    const offsetLeft = rootNode.offsetLeft + (<HTMLElement>rootNode.offsetParent).offsetLeft - marginLeft - iconSize - iconOffset;
    const offsetTop = paddingTop + rootNode.offsetTop + (rootNode.offsetParent as HTMLElement).offsetTop + spacing + position;
    return { x: offsetLeft + 'px', y: offsetTop + 'px' };
}

const CONTENT_INNER_PADDING = 10; // 关联页面、工作项、提示框等内部的 padding
const RELATED_INPUT_PADDING = 6; // 关联页面、工作项输入框 padding

export function getLineHeightHalfPosition(
    nodeEntry: NodeEntry<WikiElement>,
    lineHeight: number,
    iconSize: number,
    isFullScreen?: boolean
): number {
    let position = (lineHeight - iconSize) / 2;
    position = isFullScreen ? position * ZOOM - 3 : position;
    if (RELATED_TYPES.includes(nodeEntry[0].type) && !nodeEntry[0]._id) {
        position = position + RELATED_INPUT_PADDING;
        return position;
    }
    if (HAS_BORDER_TYPES.includes(nodeEntry[0].type)) {
        position = position + CONTENT_INNER_PADDING;
        return position;
    }
    return position;
}
