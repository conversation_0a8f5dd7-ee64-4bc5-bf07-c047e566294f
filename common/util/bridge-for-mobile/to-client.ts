import { Range, Editor } from 'slate';
import { Observable } from 'rxjs';
import {
    ElementKinds,
    TableCellElement,
    TableEditor,
    TheEditor,
    TheQueries,
    createTablePosition,
    isRectangularInTableCells
} from '@worktile/theia';
import { SafeAny } from 'ngx-tethys/types';
import { getActives, getElementsByPath } from './queries';
import { WikiPluginTypes } from '@wiki/common/types';

export const JS_TO_CLIENT_BRIDGE = 'js_to_client_bridge';

enum ClientActionName {
    onChange = 'on_change',
    onTitleFocus = 'on_title_focus',
    previewImage = 'preview_image',
    onTableRowSelect = 'on_table_row_select',
    onTableColSelect = 'on_table_col_select',
    onTableSelect = 'on_table_select',
    onTableDeselect = 'on_table_deselect',
    onOnline = 'on_online',
    onOffline = 'on_offline',
    onPagePublished = 'on_page_published',
    onPgaeSaved = 'on_page_saved',
    openRelationItem = 'open_relation_item'
}

export const ClientEventEmitter = {
    onChange(editor: TheEditor) {
        const isCollapsed = editor.selection && Range.isCollapsed(editor.selection);
        if (!isCollapsed) {
            return;
        }

        const elements = getElementsByPath(editor);
        const actives = getActives(editor);
        const focus = this.getTheFocusOfElements(editor, elements);

        this.postNativeClient(ClientActionName.onChange, {
            actives,
            focus
        });
    },

    onTitleFocus() {
        this.postNativeClient(ClientActionName.onTitleFocus);
    },

    onTableRowSelect(editor: TheEditor, data: SafeAny) {
        this.postNativeClient(ClientActionName.onTableRowSelect, this.getTableSelectionData(editor, data.cells));
    },

    onTableColSelect(editor: TheEditor, data: SafeAny) {
        this.postNativeClient(ClientActionName.onTableColSelect, this.getTableSelectionData(editor, data.cells));
    },

    onTableSelect(editor: TheEditor, data: SafeAny) {
        this.postNativeClient(ClientActionName.onTableSelect, this.getTableSelectionData(editor));
    },

    onTableDeselect() {
        this.postNativeClient(ClientActionName.onTableDeselect);
    },

    online() {
        this.postNativeClient(ClientActionName.onOnline);
    },

    offline() {
        this.postNativeClient(ClientActionName.onOffline);
    },

    publishPage(requester$: Observable<Object>) {
        requester$.subscribe({
            next: result => {
                this.postNativeClient(ClientActionName.onPagePublished, result);
            },
            error: error => {
                this.postNativeClient(ClientActionName.onPagePublished, error);
            }
        });
    },

    savePage(requester$: Observable<Object>) {
        requester$.subscribe({
            next: result => {
                this.postNativeClient(ClientActionName.onPgaeSaved, true);
            },
            error: error => {
                this.postNativeClient(ClientActionName.onPgaeSaved, false);
            }
        });
    },

    open(type: WikiPluginTypes, id: string) {
        this.postNativeClient(ClientActionName.openRelationItem, { type, id });
    },

    previewImage(imageInfo) {
        this.postNativeClient(ClientActionName.previewImage, imageInfo);
    },

    getTheFocusOfElements(editor: TheEditor, elements: any[]) {
        let type;
        let focusData;

        outerLoop: for (const e of elements) {
            type = e.type;

            switch (e.type) {
                case WikiPluginTypes.date:
                    focusData = { date: e.date };
                    break outerLoop;
                case WikiPluginTypes.label: {
                    const { label, color } = e;
                    const [_, path] = TheQueries.getAboveByType(editor, WikiPluginTypes.label);
                    focusData = { label, color, path };
                    break outerLoop;
                }
                case WikiPluginTypes.layout:
                    focusData = { type: e.layoutType };
                    break outerLoop;
                case WikiPluginTypes.alert:
                    focusData = { type: e.alertType };
                    break outerLoop;
                case WikiPluginTypes.textDiagram: {
                    const { content, diagramType } = e;
                    focusData = { content, type: diagramType };
                    break outerLoop;
                }
                case ElementKinds.link:
                    const [_, path] = TheQueries.getAboveByType(editor, ElementKinds.link);
                    focusData = { text: e.children[0].text, link: e.url, path };
                    break outerLoop;
                case ElementKinds.code:
                    const { content, language, autoWrap } = e;
                    focusData = { content, language, auto_wrap: autoWrap };
                    break outerLoop;
                case ElementKinds.image:
                    const { align, layout, originUrl, thumbUrl } = e;
                    focusData = { align, layout, origin_url: originUrl, thumb_url: thumbUrl };
                    break outerLoop;
                case ElementKinds.tableCell:
                    focusData = this.getTableSelectionData(editor);
                    break outerLoop;
                default:
                    break;
            }
        }

        return { type, data: focusData };
    },

    getTableSelectionData(editor: Editor, cells?: TableCellElement[]) {
        const selectedCells = cells?.filter(cell => !cell.hidden);
        const position = createTablePosition(editor);
        const { table, cell } = position;

        const { backgroundColor, colspan, rowspan } = cell;
        const { options } = table;
        const canSplit = (colspan && colspan !== 1) || (rowspan && rowspan !== 1) || false;
        const canMerge =
            (selectedCells &&
                selectedCells.length > 1 &&
                isRectangularInTableCells(editor, TableEditor.getSelectedCellPositions(editor))) ||
            false;
        const canInsertLeft = !TableEditor.hasHeaderRow(editor) || !position.isFirstColumn();
        const canInsertUp = !TableEditor.hasHeaderColumn(editor) || !position.isFirstRow();

        return {
            background_color: backgroundColor,
            can_split: canSplit,
            can_merge: canMerge,
            can_insert_left: canInsertLeft,
            can_insert_up: canInsertUp,
            table_options: options
        };
    },

    postNativeClient(name: ClientActionName, data?: SafeAny) {
        const jsToClientBridge = window[JS_TO_CLIENT_BRIDGE];
        if (jsToClientBridge) {
            jsToClientBridge({ name, data });
        } else {
            console.warn('Wiki for Mobile: can not find js_to_client_bridge');
        }
    }
};
