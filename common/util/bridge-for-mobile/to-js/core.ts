import { SafeAny } from 'slate-angular';
import { ActionName } from './actions';
import { TheEditor } from '@worktile/theia';
import { toPluginJs } from './to-plugin-js';
import { toTableJs } from './to-table-js';
import { toCodeJs } from './to-code-js';
import { toWikiJs } from './to-wiki-js';
import { toLinkJs } from './to-link-js';
import { toImageJs } from './to-image-js';
import { toLayoutJs } from './to-layout-js';
import { toAlertJs } from './to-alert-js';
import { toClientJsHandlers } from './client-to-js';
import { toLabelJs } from './to-label-js';

export const CLIENT_TO_JS_BRIDGE = 'client_to_js_bridge';

export const callToPluginJsHandlers = (editor, name, data) => {
    toClientJsHandlers.forEach(handler => {
        handler(editor, name, data);
    });
};

export const initializeWikiMobileEditor = (editor: TheEditor) => {
    toTableJs();
    toCodeJs();
    toLinkJs();
    toImageJs();
    toLayoutJs();
    toAlertJs();
    toLabelJs();
    toPluginJs();
    toWikiJs();
    window[CLIENT_TO_JS_BRIDGE] = (name: ActionName, data: SafeAny) => {
        callToPluginJsHandlers(editor, name, data);
    };
};
