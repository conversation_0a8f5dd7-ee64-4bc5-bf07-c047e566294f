import { SafeAny } from 'slate-angular';
import { TheiaActionName, WikiPluginActionName } from './actions';
import { TheEditor } from '@worktile/theia';
import { AlertEditor } from '@wiki/common/plugins/alert/alert.editor';
import { addClientToJsHandlers } from './client-to-js';

export const toAlertJs = () => {
    addClientToJsHandlers((editor: TheEditor, name: TheiaActionName | WikiPluginActionName, data: SafeAny) => {
        switch (name) {
            case WikiPluginActionName.insertAlert:
                AlertEditor.toggleAlert(editor);
                break;
            case WikiPluginActionName.setAlertType:
                if (!data) {
                    break;
                }
                AlertEditor.setAlertType(editor, data.type);
                break;
            case WikiPluginActionName.removeAlert:
                AlertEditor.removeAlert(editor, data?.element);
                break;
        }
    });
};
