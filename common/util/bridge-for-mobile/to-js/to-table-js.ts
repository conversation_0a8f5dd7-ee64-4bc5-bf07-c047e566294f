import { SafeAny } from 'slate-angular';
import { TheiaActionName, WikiPluginActionName } from './actions';
import { addClientToJsHandlers } from './client-to-js';
import { ElementKinds, TableEditor, TableElement, TheEditor, copyNode, createTablePosition } from '@worktile/theia';
import { getElementByType } from '../queries/get-element-by-type';

export const toTableJs = () => {
    addClientToJsHandlers((editor: TheEditor, name: TheiaActionName | WikiPluginActionName, data: SafeAny) => {
        const getPosition = () => {
            return createTablePosition(editor);
        };
        const getSelectedCells = () => {
            let cells = TableEditor.getSelectedCells(editor);
            if (cells.length === 0) {
                cells = [getPosition().cell];
            }

            return cells;
        };

        switch (name) {
            case TheiaActionName.insertTable:
                TableEditor.insertTable(editor);
                break;
            case TheiaActionName.insertTableRow:
                if (!data) {
                    break;
                }
                TableEditor.insertRow(editor, data.count || 1, data.index);
                break;
            case TheiaActionName.insertTableCol:
                if (!data) {
                    break;
                }
                TableEditor.insertColumn(editor, data.count || 1, data.index);
                break;
            case TheiaActionName.insertTableRowUp:
                TableEditor.insertRow(editor, 1, getPosition().getRowIndex());
                break;
            case TheiaActionName.insertTableRowDown:
                TableEditor.insertRow(editor, 1, getPosition().getRowIndex() + 1);
                break;
            case TheiaActionName.insertTableColLeft:
                TableEditor.insertColumn(editor, 1, getPosition().getColumnIndex());
                break;
            case TheiaActionName.insertTableColRight:
                TableEditor.insertColumn(editor, 1, getPosition().getColumnIndex() + 1);
                break;
            case TheiaActionName.removeTable:
                TableEditor.removeTable(editor);
                break;
            case TheiaActionName.removeTableRow:
                TableEditor.removeRow(editor, getPosition().getRowIndex());
                break;
            case TheiaActionName.removeTableCol:
                TableEditor.removeColumn(editor, getPosition().getColumnIndex());
                break;
            case TheiaActionName.copyTable:
                let element = data?.element;
                if (!element) {
                    element = getElementByType(editor, ElementKinds.table) as TableElement;
                }
                if (!element) {
                    break;
                }
                copyNode(editor, element);
                break;
            case TheiaActionName.setTableCellVerticalAlign:
                if (!data) {
                    break;
                }
                TableEditor.setVerticalAlign(editor, data.verticalAlign, data.cells);
                break;
            case TheiaActionName.setTableCellAlign:
                if (!data) {
                    break;
                }
                TableEditor.setAlign(editor, data.align, data.cells);
                break;
            case TheiaActionName.clearTableCell:
                TableEditor.clearCellsContent(editor, getSelectedCells());
                break;
            case TheiaActionName.mergeTableCell:
                TableEditor.mergeCell(editor);
                break;
            case TheiaActionName.splitTableCell:
                TableEditor.splitCell(editor);
                break;
            case TheiaActionName.setTableOptions:
                if (!data) {
                    break;
                }
                TableEditor.setTableOptions(editor, data);
                break;
            case TheiaActionName.setTableCellColor: {
                if (!data) {
                    break;
                }
                TableEditor.setCellsBackgroundColor(editor, data, getSelectedCells());
                break;
            }
            case TheiaActionName.setTableEquallyColumn:
                TableEditor.setEquallyColumn(editor);
                break;
        }
    });
};
