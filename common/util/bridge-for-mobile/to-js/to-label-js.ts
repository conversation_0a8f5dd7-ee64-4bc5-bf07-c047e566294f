import { Node, Path, Transforms } from 'slate';
import { SafeAny } from 'slate-angular';
import { TheiaActionName, WikiPluginActionName } from './actions';
import { TheEditor } from '@worktile/theia';
import { addClientToJsHandlers } from './client-to-js';
import { LabelEditor } from '@wiki/common/plugins/label/label.editor';
import { LabelElement } from '@wiki/common/custom-types';

export const toLabelJs = () => {
    addClientToJsHandlers((editor: TheEditor, name: TheiaActionName | WikiPluginActionName, data: SafeAny) => {
        switch (name) {
            case WikiPluginActionName.insertLabel:
                LabelEditor.insertLabel(editor);
                break;
            case WikiPluginActionName.setLabelColor: {
                if (!data) {
                    break;
                }
                const element = Node.get(editor, data.path) as LabelElement;
                LabelEditor.setLabelColor(editor, data.color, element);
                break;
            }
            case WikiPluginActionName.setLabelValue: {
                if (!data) {
                    break;
                }
                const element = Node.get(editor, data.path) as LabelElement;
                LabelEditor.setLabelValue(editor, data.label, element);
                const nextPath = Path.next(data.path);
                Transforms.select(editor, nextPath);
                TheEditor.focus(editor);
                Transforms.collapse(editor, { edge: 'start' });
                break;
            }
        }
    });
};
