import { DateEditor } from '@wiki/common/plugins/date/date.editor';
import { DiagramBoardEditor } from '@wiki/common/plugins/diagram-board/diagram-board.editor';
import { BaseFileEditor } from '@wiki/common/plugins/file/base-file.editor';
import { FormulaEditor } from '@wiki/common/plugins/formula/formula.editor';
import { OutlineEditor } from '@wiki/common/plugins/outline/outline.editor';
import { RelationItemEditor } from '@wiki/common/plugins/relation-item/relation-item.editor';
import { TextDiagramEditor } from '@wiki/common/plugins/text-diagram/text-diagram.editor';
import { ToggleListEditor } from '@wiki/common/plugins/toggle-list/toggle-list.editor';
import { WikiPluginTypes } from '@wiki/common/types';
import {
    AlignEditor,
    BlockquoteEditor,
    ColorEditor,
    ElementKinds,
    HeadingEditor,
    HrEditor,
    IndentEditor,
    InlineCodeEditor,
    ListEditor,
    Mark<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    MentionEditor,
    TheEditor,
    TodoItemEditor,
    createEmptyParagraph,
    refocus
} from '@worktile/theia';
import { Transforms } from 'slate';
import { SafeAny } from 'slate-angular';
import { ActionName, TheiaActionName, WikiPluginActionName } from './actions';
import { addClientToJsHandlers } from './client-to-js';

export const toPluginJs = () => {
    addClientToJsHandlers((editor: TheEditor, name: ActionName, data: SafeAny) => {
        switch (name) {
            // heading
            case TheiaActionName.setHeading:
                if (!data) {
                    Transforms.setNodes(editor, { type: ElementKinds.paragraph });
                    break;
                }
                HeadingEditor.setHeading(editor, data);
                break;
            // mark
            case TheiaActionName.toggleMark:
                if (!data) {
                    break;
                }
                MarkEditor.toggleMark(editor, data);
                break;
            // inlineCode
            case TheiaActionName.toggleInlineCode:
                InlineCodeEditor.toggleInlineCode(editor);
                break;
            // color
            case TheiaActionName.setColor:
                ColorEditor.setColor(editor, data, MarkTypes.color);
                break;
            // backgroundColor
            case TheiaActionName.setBackgroundColor:
                ColorEditor.setColor(editor, data, MarkTypes.backgroundColor);
                break;
            // align
            case TheiaActionName.setAlign:
                AlignEditor.setAlign(editor, data);
                break;
            // indent
            case TheiaActionName.setIndent:
                switch (data) {
                    case 'increase':
                        IndentEditor.setIndent(editor);
                        break;
                    case 'decrease':
                        IndentEditor.cancelIndent(editor);
                        break;
                    default:
                        break;
                }
                break;
            // list
            case TheiaActionName.toggleList:
                if (!data) {
                    break;
                }
                ListEditor.toggleList(editor, data);
                break;
            // todoItem
            case TheiaActionName.toggleTodoItem:
                TodoItemEditor.insertTodoItem(editor);
                break;
            // blockquote
            case TheiaActionName.toggleBlockquote:
                BlockquoteEditor.toggleBlockquote(editor);
                break;
            // hr
            case TheiaActionName.insertHr:
                HrEditor.insertHr(editor);
                break;
            //common operation
            case TheiaActionName.setDarkThemeColor:
                document.body.style.setProperty('--dark-theme-color', data);
                break;
            case TheiaActionName.clear:
                editor.children = [createEmptyParagraph()];
                Transforms.deselect(editor);
                break;
            case TheiaActionName.undo:
                editor.undo();
                break;
            case TheiaActionName.redo:
                editor.redo();
                break;
            case TheiaActionName.deleteBackward:
                editor.deleteBackward('character');
                Transforms.delete(editor);
                break;
            case TheiaActionName.deleteForward:
                editor.deleteForward('character');
                break;
            case TheiaActionName.refocus:
                refocus(editor);
                break;

            // Wiki
            // emoji
            case WikiPluginActionName.insertEmoji:
                if (!data) {
                    break;
                }
                MentionEditor.insertMention(editor, WikiPluginTypes.emoji, {
                    code: data
                });
                break;
            // member
            case WikiPluginActionName.insertMember:
                if (!data) {
                    break;
                }
                MentionEditor.insertMention(editor, WikiPluginTypes.mention, {
                    uid: data
                });
                break;
            // toggleList
            case WikiPluginActionName.insertToggleList:
                ToggleListEditor.insertToggleList(editor);
                break;
            // date
            case WikiPluginActionName.insertDate:
                DateEditor.insertDate(editor);
                break;
            case WikiPluginActionName.setDateValue:
                DateEditor.setDateValue(editor, data);
                break;
            // formula
            case WikiPluginActionName.insertFormula:
                FormulaEditor.insert(editor);
                break;
            // board
            case WikiPluginActionName.insertBoard:
                DiagramBoardEditor.insert(editor, WikiPluginTypes.board);
                break;
            // diagramBoard
            case WikiPluginActionName.insertDiagramBoard:
                DiagramBoardEditor.insert(editor, WikiPluginTypes.diagramBoard);
                break;
            // textDiagram
            case WikiPluginActionName.insertTextDiagram:
                TextDiagramEditor.insert(editor);
                break;
            // file
            case WikiPluginActionName.insertFile:
                BaseFileEditor.addIdFile(editor, data);
                break;
            case WikiPluginActionName.uploadFile:
                BaseFileEditor.openUpload(editor);
                break;
            // video
            case WikiPluginActionName.insertVideo:
                BaseFileEditor.openUpload(editor, WikiPluginTypes.video);
                break;
            // audio
            case WikiPluginActionName.insertAudio:
                BaseFileEditor.openUpload(editor, WikiPluginTypes.audio);
                break;
            // relation
            case WikiPluginActionName.relationIdea:
                RelationItemEditor.insert(editor, WikiPluginTypes.relationIdea, data);
                break;
            case WikiPluginActionName.relationTicket:
                RelationItemEditor.insert(editor, WikiPluginTypes.relationTicket, data);
                break;
            case WikiPluginActionName.relationWorkItem:
                RelationItemEditor.insert(editor, WikiPluginTypes.relationWorkItem, data);
                break;
            case WikiPluginActionName.relationTestCase:
                RelationItemEditor.insert(editor, WikiPluginTypes.relationTestCase, data);
                break;
            case WikiPluginActionName.relationObjective:
                RelationItemEditor.insert(editor, WikiPluginTypes.relationObjective, data);
                break;
            // page
            case WikiPluginActionName.relationPage:
                RelationItemEditor.insert(editor, WikiPluginTypes.relationPage, data);
                break;
            case WikiPluginActionName.relationPageToc:
                OutlineEditor.insert(editor);
                break;
            default:
                console.warn('Wiki for Mobile: can not find action name', name);
        }
    });
};
