import { Node } from 'slate';
import { SafeAny } from 'slate-angular';
import { TheiaActionName, WikiPluginActionName } from './actions';
import { LinkEditor, LinkElement, TheEditor, refocus } from '@worktile/theia';
import { addClientToJsHandlers } from './client-to-js';

export const toLinkJs = () => {
    addClientToJsHandlers((editor: TheEditor, name: TheiaActionName | WikiPluginActionName, data: SafeAny) => {
        switch (name) {
            case TheiaActionName.insertLink:
                if (!data) {
                    break;
                }
                refocus(editor);
                LinkEditor.wrapLink(editor, data.text, data.url);
                break;
            case TheiaActionName.setLink:
                if (!data) {
                    break;
                }
                const element = Node.get(editor, data.path) as LinkElement;
                LinkEditor.updateLink(editor, element, data.url, data.text);
                break;
            case TheiaActionName.unwrapLink:
                LinkEditor.unwrapLink(editor);
                break;
        }
    });
};
