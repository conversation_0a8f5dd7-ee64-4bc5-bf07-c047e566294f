import { SafeAny } from 'slate-angular';
import { TheiaActionName, WikiPluginActionName } from './actions';
import { TheEditor } from '@worktile/theia';
import { addClientToJsHandlers } from './client-to-js';
import { LayoutEditor } from '@wiki/common/plugins/layout/layout.editor';
import { getLayoutTypes } from '@wiki/common/plugins/layout/constants';

export const toLayoutJs = () => {
    addClientToJsHandlers((editor: TheEditor, name: TheiaActionName | WikiPluginActionName, data: SafeAny) => {
        switch (name) {
            case WikiPluginActionName.insertLayout:
                LayoutEditor.toggleLayout(editor);
                break;
            case WikiPluginActionName.setLayoutType:
                if (!data) {
                    break;
                }
                const { flexBasis } = getLayoutTypes().find(item => item.type === data.type);
                LayoutEditor.setLayoutTypeAndWidths(editor, data.type, flexBasis, null);
                break;
            case WikiPluginActionName.removeLayout:
                LayoutEditor.removeLayout(editor, data?.element);
                break;
        }
    });
};
