import { TheEditor } from '@worktile/theia';
import { SafeAny } from 'slate-angular';
import { WikiActionName } from './actions';
import { addClientToJsHandlers } from './client-to-js';
import { ClientEventEmitter } from '../to-client';
import { EDITOR_TO_MOBILE_EDIT_COMPONENT } from '../../weak-map';

export const toWikiJs = () => {
    addClientToJsHandlers((editor: TheEditor, name: WikiActionName, data: SafeAny) => {
        const mobileEditComponent = EDITOR_TO_MOBILE_EDIT_COMPONENT.get(editor);
        switch (name) {
            case WikiActionName.savePage:
                ClientEventEmitter.savePage(mobileEditComponent.save(data?.versionName));
                break;
            case WikiActionName.publishPage:
                ClientEventEmitter.publishPage(mobileEditComponent.publish(data?.options));
                break;
        }
    });
};
