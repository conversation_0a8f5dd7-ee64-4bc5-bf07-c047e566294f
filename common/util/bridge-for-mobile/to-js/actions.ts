export enum TheiaActionName {
    // mark
    toggleMark = 'toggle_mark',
    // table
    insertTable = 'insert_table',
    insertTableRow = 'insert_table_row',
    insertTableCol = 'insert_table_col',
    insertTableRowUp = 'insert_table_row_up',
    insertTableRowDown = 'insert_table_row_down',
    insertTableColLeft = 'insert_table_col_left',
    insertTableColRight = 'insert_table_col_right',
    removeTable = 'remove_table',
    removeTableRow = 'remove_table_row',
    removeTableCol = 'remove_table_col',
    mergeTableCell = 'merge_table_cell',
    splitTableCell = 'split_table_cell',
    setTableOptions = 'set_table_options',
    setTableCellColor = 'set_table_cell_color',
    copyTable = 'copy_table',
    clearTableCell = 'clear_table_cell',
    setTableCellAlign = 'set_table_cell_align',
    setTableCellVerticalAlign = 'set_table_cell_vertical_align',
    setTableEquallyColumn = 'set_table_equally_column',
    // code
    insertCode = 'insert_code',
    removeCode = 'remove_code',
    changeCodeLanguage = 'change_code_language',
    setCodeAutoWrap = 'set_code_auto_wrap',
    copyCode = 'copy_code',
    // hr
    insertHr = 'insert_hr',
    // link
    insertLink = 'insert_link',
    unwrapLink = 'unwrap_link',
    setLink = 'set_link',
    // image
    insertImage = 'insert_image',
    uploadImage = 'upload_image',
    uploadCameraPhoto = 'upload_camera_photo',
    setImageLayout = 'set_image_layout',
    setImageAlign = 'set_image_align',
    removeImage = 'remove_image',
    // list
    toggleList = 'toggle_list',
    // inlineCode
    toggleInlineCode = 'toggle_inline_code',
    // blockquote
    toggleBlockquote = 'toggle_blockquote',
    // todo
    toggleTodoItem = 'toggle_todo_item',
    // color
    setColor = 'set_color',
    // backgroundColor
    setBackgroundColor = 'set_background_color',
    // heading
    setHeading = 'set_heading',
    // align
    setAlign = 'set_align',
    // indent
    setIndent = 'set_indent',
    // clear
    clear = 'clear',
    // undo / redo
    undo = 'undo',
    redo = 'redo',
    // operation
    deleteBackward = 'delete_backward',
    deleteForward = 'delete_forward',
    refocus = 'refocus',
    setDarkThemeColor = 'set_dark_theme_color'
}

export enum WikiPluginActionName {
    // emoji
    insertEmoji = 'insert_emoji',
    // member
    insertMember = 'insert_member',
    // toggleList
    insertToggleList = 'insert_toggle_list',
    // layout
    insertLayout = 'insert_layout',
    setLayoutType = 'set_layout_type',
    removeLayout = 'remove_layout',
    // date
    insertDate = 'insert_date',
    setDateValue = 'set_date_value',
    // label
    insertLabel = 'insert_label',
    setLabelColor = 'set_label_color',
    setLabelValue = 'set_label_value',
    // alert
    insertAlert = 'insert_alert',
    setAlertType = 'set_alert_type',
    removeAlert = 'remove_alert',
    // formula
    insertFormula = 'insert_formula',
    // board
    insertBoard = 'insert_board',
    // diagramBoard
    insertDiagramBoard = 'insert_diagram',
    // textDiagram
    insertTextDiagram = 'insert_text_diagram',
    // file
    insertFile = 'insert_file',
    uploadFile = 'upload_file',
    previewFile = 'preview_file',
    downloadFile = 'download_file',
    renameFile = 'rename_file',
    removeFile = 'remove_file',
    // video
    insertVideo = 'insert_video',
    // audio
    insertAudio = 'insert_audio',
    // relation
    relationIdea = 'relation_idea',
    relationTicket = 'relation_ticket',
    relationWorkItem = 'relation_work_item',
    relationTestCase = 'relation_test_case',
    relationObjective = 'relation_objective',
    // page
    relationPage = 'relation_page',
    relationPageToc = 'relation_page_toc'
}

export enum WikiActionName {
    publishPage = 'publish_page',
    savePage = 'save_page'
}

export type ActionName = TheiaActionName | WikiPluginActionName | WikiActionName;
