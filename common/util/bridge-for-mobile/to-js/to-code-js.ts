import { ELEMENT_TO_COMPONENT, SafeAny } from 'slate-angular';
import { TheiaActionName, WikiPluginActionName } from './actions';
import { CodeEditor, CodeElement, ElementKinds, TheEditor, copyNode } from '@worktile/theia';
import { addClientToJsHandlers } from './client-to-js';
import { getElementByType } from '../queries/get-element-by-type';
import { TheCode } from '@worktile/theia/plugins/code/code.component';

export const toCodeJs = () => {
    addClientToJsHandlers((editor: TheEditor, name: TheiaActionName | WikiPluginActionName, data: SafeAny) => {
        switch (name) {
            case TheiaActionName.insertCode:
                CodeEditor.insertCode(editor);
                break;
            case TheiaActionName.removeCode:
                CodeEditor.removeCode(editor);
                break;
            case TheiaActionName.changeCodeLanguage:
                CodeEditor.setCodeAttribute(editor, { language: data.language });
                break;
            case TheiaActionName.setCodeAutoWrap:
                CodeEditor.setCodeAttribute(editor, { autoWrap: data.autoWrap || null });
                break;
            case TheiaActionName.copyCode:
                let element = data?.element || (getElementByType(editor, ElementKinds.code) as CodeElement);
                if (!element) {
                    break;
                }
                copyNode(editor, element);
                break;
        }
    });
};
