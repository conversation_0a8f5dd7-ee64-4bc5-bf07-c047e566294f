import { SafeAny } from 'slate-angular';
import { TheiaActionName, WikiPluginActionName } from './actions';
import { Element<PERSON><PERSON>s, ImageEditor, TheEditor, TheTransforms } from '@worktile/theia';
import { addClientToJsHandlers } from './client-to-js';

export const toImageJs = () => {
    addClientToJsHandlers((editor: TheEditor, name: TheiaActionName | WikiPluginActionName, data: SafeAny) => {
        switch (name) {
            case TheiaActionName.insertImage:
                if (!data) {
                    break;
                }
                TheTransforms.insertElements(editor, {
                    type: ElementKinds.image,
                    url: data.url,
                    thumbUrl: data.url,
                    originUrl: data.url,
                    children: [{ text: '' }]
                });
                break;
            case TheiaActionName.uploadImage:
                const imageInput = document.createElement('input');
                imageInput.setAttribute('type', 'file');
                imageInput.setAttribute('accept', 'image/*');
                imageInput.onchange = (event: Event) => {
                    ImageEditor.insertImages(editor, (event.target as any).files);
                };
                imageInput.click();
                break;
            case TheiaActionName.uploadCameraPhoto:
                const cameraInput = document.createElement('input');
                cameraInput.setAttribute('type', 'file');
                cameraInput.setAttribute('accept', 'image/*');
                cameraInput.setAttribute('capture', null);
                cameraInput.onchange = (event: Event) => {
                    ImageEditor.insertImages(editor, (event.target as any).files);
                };
                cameraInput.click();
                break;
            case TheiaActionName.setImageLayout:
                if (!data) {
                    break;
                }
                ImageEditor.setImageNode(
                    editor,
                    {
                        layout: data.layout
                    },
                    data.element
                );
                break;
            case TheiaActionName.setImageAlign:
                if (!data) {
                    break;
                }
                ImageEditor.setImageNode(
                    editor,
                    {
                        align: data.align,
                        layout: undefined
                    },
                    data.element
                );
                break;
            case TheiaActionName.removeImage:
                ImageEditor.removeImage(editor, data?.element);
                break;
        }
    });
};
