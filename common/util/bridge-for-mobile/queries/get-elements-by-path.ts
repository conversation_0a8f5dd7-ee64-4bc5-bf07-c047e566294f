import { TheEditor } from '@worktile/theia';
import { Range, Node, Path } from 'slate';

export function getElementsByPath(editor: TheEditor, at?: Path) {
    let path = at;
    if (!at) {
        const { selection } = editor;
        if (!selection) return [];
        const [start] = Range.edges(selection);
        path = start.path;
    }

    const elements = [];
    for (let i = 0; i < path.length; i++) {
        const pathSlice = path.slice(0, i + 1);
        const node = Node.get(editor, pathSlice);
        node.type && elements.push(node);
    }

    return elements;
}
