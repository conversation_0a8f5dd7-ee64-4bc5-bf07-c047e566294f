import {
    AlignEditor,
    Alignment,
    ColorEditor,
    ElementKinds,
    HeadingEditor,
    HeadingElementKinds,
    HrEditor,
    ImageEditor,
    InlineCodeEditor,
    LinkEditor,
    ListEditor,
    MarkEditor,
    MarkTypes,
    MentionEditor,
    TableEditor,
    TheEditor,
    TheQueries,
    TodoItemEditor
} from '@worktile/theia';
import { MentionType } from '@atinc/ngx-styx/rich-text';
import { ToggleListEditor } from '@wiki/common/plugins/toggle-list/toggle-list.editor';
import { WikiPluginTypes } from '@wiki/common/types';

export function getActives(editor: TheEditor) {
    var actives = [];

    // 文本属性
    [MarkTypes.bold, MarkTypes.italic, MarkTypes.underline, MarkTypes.strike].forEach(type => {
        if (MarkEditor.isMarkActive(editor, type)) {
            actives.push(type);
        }
    });

    // 标题
    [
        ElementKinds.heading_1,
        ElementKinds.heading_2,
        ElementKinds.heading_3,
        ElementKinds.heading_4,
        ElementKinds.heading_5,
        ElementKinds.heading_6
    ].forEach(type => {
        if (HeadingEditor.isHeadingActive(editor, type as HeadingElementKinds)) {
            actives.push(type);
        }
    });

    // 颜色
    [MarkTypes.color, MarkTypes.backgroundColor].forEach(type => {
        const color = ColorEditor.getActiveColor(editor, type as any);
        if (color) {
            actives.push(`${type}-${color}`);
        }
    });

    // 行内代码
    if (InlineCodeEditor.isInlineCodeActive(editor)) {
        actives.push(ElementKinds.inlineCode);
    }

    // 列表
    [ElementKinds.numberedList, ElementKinds.bulletedList].forEach(type => {
        if (ListEditor.isActive(editor, type as any)) {
            actives.push(type);
        }
    });

    // 待办事项
    if (TodoItemEditor.isActive(editor)) {
        actives.push(ElementKinds.checkItem);
    }

    // 折叠列表
    if (ToggleListEditor.isActive(editor)) {
        actives.push(WikiPluginTypes.toggleList);
    }

    // 引用
    if (TheQueries.isBlockActive(editor, ElementKinds.blockquote)) {
        actives.push(ElementKinds.blockquote);
    }

    // 链接
    if (LinkEditor.isActive(editor)) {
        actives.push(ElementKinds.link);
    }

    // 提及
    [MentionType.mention, MentionType.workItem, MentionType.emoji].forEach(type => {
        if (MentionEditor.isActive(editor as TheEditor, type)) {
            actives.push(type);
        }
    });

    // 表格
    if (TableEditor.isActive(editor)) {
        actives.push(ElementKinds.table);
    }

    // 图片
    if (ImageEditor.isActive(editor)) {
        actives.push(ElementKinds.image);
    }

    // 分割线
    if (HrEditor.isHrActive(editor)) {
        actives.push(ElementKinds.hr);
    }

    // 对齐方式
    [Alignment.left, Alignment.center, Alignment.right].forEach(alignment => {
        if (AlignEditor.isActive(editor, alignment)) {
            actives.push(`align-${alignment}`);
        }
    });

    // 链接
    if (LinkEditor.isActive(editor)) {
        actives.push(ElementKinds.link);
    }

    return actives;
}
