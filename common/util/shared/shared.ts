import { MindElement, getRectangleByNode } from '@plait/mind';
import { WikiPageContent } from '@wiki/common/interface/page';
import { TheEditor } from '@worktile/theia';
import { SyncElement, withYjs, toSharedType } from '@worktile/y-slate';
import { COLORS, helpers, MeInfo } from '@atinc/ngx-styx';
import { Observable } from 'rxjs';
import { WebsocketProvider } from './y-websocket';
import * as Y from 'yjs';
import { Editor } from 'slate';
import { CursorBoard } from '@wiki/common/yjs-plait/board/cursor-board';
import { PlaitBoard, PlaitElement, RectangleClient } from '@plait/core';
import { PEElement } from '@plait-editor/types/element';
import { PlaitDrawElement } from '@plait/draw';

const CURSOR_HEIGHT = 35;

export const getForeignRectangleByNode = (board: PlaitBoard, element: PEElement) => {
    if (MindElement.isMindElement(board, element)) {
        const node = MindElement.getNode(element);
        const { x, y, width, height } = node && getRectangleByNode(node);
        return {
            x: x - 2,
            y: y - 26,
            width,
            height
        };
    }
    let { x, y, width, height } = RectangleClient.getRectangleByPoints(element.points);
    if (PlaitDrawElement.isArrowLine(element)) {
        y = y - CURSOR_HEIGHT;
    }
    return {
        x,
        y,
        width,
        height: height > CURSOR_HEIGHT ? height : CURSOR_HEIGHT
    };
};

export function getSharedEditor(
    editor: CursorBoard | TheEditor,
    collaborationInfo: { uri: string; token: string },
    slug: string,
    user: MeInfo,
    refetchToken: () => Observable<string>,
    isInitializeSharedType: boolean,
    initializeValue: WikiPageContent = []
) {
    const doc = new Y.Doc();
    const sharedDoc = doc.getArray<SyncElement>('content');
    if (isInitializeSharedType) {
        toSharedType(sharedDoc, initializeValue as any);
    }

    const provider = new WebsocketProvider(collaborationInfo.uri, refetchToken, slug, doc, {
        connect: false,
        params: {
            token: collaborationInfo.token,
            client: doc.clientID
        }
    });

    editor.provider = provider;
    const yjsEditor = withYjs(editor as Editor, sharedDoc, { isSynchronizeValue: isInitializeSharedType });

    connectProvider(yjsEditor, user);

    return yjsEditor;
}

export function connectProvider(editor: CursorBoard | TheEditor, user: MeInfo) {
    const color = COLORS[Math.floor(Math.random() * COLORS.length)];
    editor.provider.awareness.setLocalState({
        alphaColor: helpers.hexToRgba(color, 0.2),
        color,
        user: {
            uid: user?.uid,
            avatar: user?.avatar,
            name: user?.name,
            display_name: user?.display_name
        }
    });

    // Super hacky way to provide a initial value from the client, if
    // you plan to use y-websocket in prod you probably should provide the
    // initial state from the server.
    editor.provider.on('sync', (isSynced: boolean) => {
        // 使用 has_shared_cache 字段判断服务端是否有 yjs 缓存后，不需要等待消息服务的结果确定是否需要同步本地数据了
    });

    editor.provider.connect();
}
