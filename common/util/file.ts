export function checkRejectFolderAndHtmlElement(dataTransfer: DataTransfer) {
    // 排除文件夹和HTML元素拖拽
    const items: DataTransferItemList | DataTransferItem[] = dataTransfer.items;
    let res = true;
    for (let index = 0; index < items.length; index++) {
        const item = items[index];
        const entry = getAsEntry(item);
        if (item.kind !== 'file' || (entry && !entry.isFile)) {
            res = false;
        }
    }
    return res;
}

function getAsEntry(item: DataTransferItem): FileSystemEntry {
    let entry: FileSystemEntry;
    if (item['getAsEntry']) {
        // https://wiki.whatwg.org/wiki/DragAndDropEntries
        entry = item['getAsEntry']();
    } else if (item.webkitGetAsEntry) {
        entry = item.webkitGetAsEntry();
    }
    return entry;
}
