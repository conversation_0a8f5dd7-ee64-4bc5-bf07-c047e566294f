import { TheEditor } from '@worktile/theia';
import { ThyPopoverRef } from 'ngx-tethys/popover';
import { Editor, Point } from 'slate';

export const COMMON_EDITOR_ORIGIN_ANCHOR: WeakMap<Editor, Point> = new WeakMap();

export const COMMON_EDITOR_POPOVER_REF: WeakMap<Editor, ThyPopoverRef<any>> = new WeakMap();

export const COMMON_SEARCH_REPLACE_KEYWORDS: WeakMap<Editor, string> = new WeakMap();

export const EDITOR_TO_MOBILE_EDIT_COMPONENT: WeakMap<TheEditor, any> = new WeakMap();
