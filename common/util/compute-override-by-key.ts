import { CustomElementKinds, HEADING_TYPES, OverrideBy<PERSON><PERSON>, Plugin<PERSON>eys } from '@worktile/theia';
import { CustomPluginKeys } from '../types/plugins.types';

export const computeOverrideByKey = (keys: string[], allowTypes: CustomElementKinds[]): OverrideByKey => {
    const overrideByKey: OverrideByKey = {};
    const keyList = [
        PluginKeys.list,
        PluginKeys.numberedList,
        PluginKeys.bulletedList,
        PluginKeys.checkItem,
        PluginKeys.image,
        PluginKeys.blockquote,
        PluginKeys.heading,
        ...HEADING_TYPES,
        CustomPluginKeys.toggleList,
        ...keys
    ];

    for (let key = 0; key < keyList.length; key++) {
        overrideByKey[keyList[key]] = {
            key: keyList[key],
            options: {
                allowParentTypes: [...allowTypes]
            }
        };
    }
    return overrideByKey;
};
