import { NodeEntry } from 'slate';
import { coercePixelsFromCssValue } from '@worktile/theia';
import { WikiElement } from '../custom-types';
import { getLineHeightHalfPosition } from './side-button';
import { Renderer2 } from '@angular/core';

export function setVisibility(htmlElement: HTMLElement, visible = true) {
    htmlElement.style.display = visible ? 'block' : 'none';
}

export function setOffset(
    rootNode: HTMLElement,
    renderer: Renderer2,
    targetElement: HTMLElement,
    nodeEntry: NodeEntry<WikiElement>,
    iconSize: number,
    iconRightOffset: number,
    basicOffsetTop: number,
    isFullScreen?: boolean
) {
    const firstTextElement = rootNode.querySelector('[data-slate-node="text"]');
    const lineHeight = coercePixelsFromCssValue(window.getComputedStyle(firstTextElement || rootNode)['lineHeight']);
    const marginLeft = coercePixelsFromCssValue(window.getComputedStyle(rootNode)['marginLeft']);
    const paddingTop = coercePixelsFromCssValue(window.getComputedStyle(rootNode)['paddingTop']);
    const position = getLineHeightHalfPosition(nodeEntry, lineHeight, iconSize, isFullScreen);
    const offsetLeft = rootNode.offsetLeft + (<HTMLElement>rootNode.offsetParent).offsetLeft - marginLeft - iconSize - iconRightOffset;
    const offsetTop = paddingTop + rootNode.offsetTop + (rootNode.offsetParent as HTMLElement).offsetTop + basicOffsetTop + position;
    const x = offsetLeft + 'px',
        y = offsetTop + 'px';

    y && renderer.setStyle(targetElement, 'top', y);
    x && renderer.setStyle(targetElement, 'left', x);

    setVisibility(targetElement, true);
}
