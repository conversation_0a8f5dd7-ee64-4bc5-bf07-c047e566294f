/**
 * 判断字符串名是否属于格式数组中的一个
 * @param formats 后缀格式数组
 * @param text 字符串
 * @return boolean
 */
export const includesTextSuffixFormat = (formats: string[], text: string) => {
    return !!formats.find(type => {
        const index = text.indexOf(type);
        if (index > 0) {
            return text.slice(index, text.length) === type;
        }
        return false;
    });
};

/**
 * 多个数组取交集
 * @param array [][]
 * @returns 交集数组[]
 */
export const getIntersection = <T>(array: T[][]): T[] => {
    let result = array[0];
    array.forEach(elements => {
        result = result.filter(ele => elements.includes(ele));
    });
    return result;
};

/**
 * 判断所有内容是否都相同
 * @param array []
 * @returns boolean
 */
export const isEveryEqual = <T>(array: T[]): boolean => {
    return array.every((type, _, arr) => type === arr[0]);
};
