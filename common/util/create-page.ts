import { Is, StyxTranslateService, UtilService } from '@atinc/ngx-styx';
import { CreatePageType, DRAFT_GROUP, EditingPageFrom, PageSystems, PageTypes } from '@wiki/app/constants/page';
import { DraftInfo, PageInfo } from '@wiki/app/entities/page-info';
import { EditingPageResult } from '@wiki/app/info/editing-page';
import { PageService } from '@wiki/app/services/util/page.service';
import { CommonEditingPageStore } from '@wiki/common/stores/editing-page.store';
import { getDefaultPageTitle } from './get-default-page-title';
import { getAfterId } from './tree';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

export function createPage(
    condition: {
        spaceId: string;
        page: PageInfo;
        pageType: PageTypes;
        createPageType: CreatePageType;
        treePages: PageInfo[];
    },
    dependencies: {
        pageService: PageService;
        editingPageStore: CommonEditingPageStore;
        util: UtilService;
    },
    translate: StyxTranslateService,
    handle?: (pageInfo: PageInfo) => void
) {
    const { pageService, editingPageStore, util } = dependencies;
    const { spaceId, page, createPageType, treePages, pageType } = condition;
    let newPage: any = {
        name: getDefaultPageTitle(pageType, translate),
        parent_id: page.system === PageSystems.home ? null : page._id,
        type: pageType
    };
    if (createPageType && createPageType !== CreatePageType.Child) {
        const afterId = getAfterId(page, createPageType, treePages);
        newPage = {
            ...newPage,
            after_id: afterId,
            parent_id: page.system === PageSystems.home ? null : page.parent_id,
            append_when_nil_after_id: !!afterId
        };
    }
    editingPageStore.createPage(spaceId, newPage, EditingPageFrom.create).subscribe(data => {
        pageService
            .openPageEdit(data.value._id, false, EditingPageFrom.create, undefined, pageType)
            .afterClosed()
            .subscribe((editingPageResult: EditingPageResult | null) => {
                handle && handle(editingPageResult);
                if (editingPageResult && editingPageResult.closeWithPublish) {
                    util.notify.success(translate.instant<I18nSourceDefinitionType>('wiki.page.publish.success'));
                }
            });
    });
}

export const getParentId = (page: PageInfo, pageType?: CreatePageType) => {
    if (!pageType || pageType === CreatePageType.Child) {
        if (!page || page?.system === PageSystems.home) {
            return null;
        } else {
            return page._id;
        }
    } else {
        return page?.parent_id;
    }
};

export const getDraftGroup = (page: PageInfo, pageNodes: PageInfo[], createPageType: CreatePageType, parentId?: string) => {
    const pageId = !parentId ? getParentId(page, createPageType) : parentId;
    let draftGroup: DraftInfo = {
        _id: DRAFT_GROUP,
        type: PageTypes.group,
        parent_id: pageId,
        is_published: Is.yes
    };
    // 非子级则需要计算插入位置
    if (createPageType !== CreatePageType.Child) {
        const afterId = getAfterId(page, createPageType, pageNodes);
        draftGroup = {
            ...draftGroup,
            after_id: afterId,
            append_when_nil_after_id: !!afterId,
            position: createPageType === CreatePageType.Above ? page.position - 1 : page.position + 1
        };
    }
    return draftGroup;
};
