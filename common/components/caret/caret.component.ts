import { ChangeDetectionStrategy, Component, ElementRef, Input, inject } from '@angular/core';
import { AwarenessInfo } from '@wiki/common/yjs-plait/yjs-plait.type';

@Component({
    selector: 'wiki-common-caret',
    templateUrl: './caret.component.html',
    host: {
        class: 'wiki-common-caret'
    },
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiCommonSharedCaretComponent {
    @Input() decorate: AwarenessInfo;

    @Input() isForward: boolean;

    elementRef = inject(ElementRef<HTMLElement>);

    nativeElement() {
        return this.elementRef.nativeElement;
    }

    constructor() {}
}
