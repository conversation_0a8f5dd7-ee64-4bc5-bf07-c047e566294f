@if (decorate) {
  <span
    contenteditable="false"
    class="wiki-shared-caret"
    [ngStyle]="{
      left: isForward ? 'calc(100% - 1px)' : '-1px'
    }"
  >
    <span
      contenteditable="false"
      class="wiki-shared-cursor"
      [ngStyle]="{
        background: decorate?.color
      }"
    >
      {{ decorate?.user?.display_name }}
    </span>
    <div
      class="wiki-shared-caret-line"
      [ngStyle]="{
        background: decorate?.color
      }"
    ></div>
  </span>
}
