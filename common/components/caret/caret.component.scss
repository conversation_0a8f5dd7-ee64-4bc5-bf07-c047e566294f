@use '../../styles/variables.scss' as commonVariables;

.wiki-shared-caret {
    position: absolute;
    pointer-events: none;
    user-select: none;
}

.wiki-shared-caret-line {
    position: absolute;
    height: 1.3em;
    width: 2px;
    top: -0.1em;
}
.wiki-shared-cursor {
    position: absolute;
    top: 2px;
    left: -2px;
    line-height: normal;
    border-radius: 2px;
    padding: 2px 5px;
    pointer-events: none;
    user-select: none;
    transform: translateY(-100%);
    font-size: 12px;
    color: white;
    white-space: nowrap;
    text-indent: initial;
    font-weight: initial;
    font-style: initial;
    text-decoration: initial;
}

.wiki-shared-content {
    position: relative;
}

.slate-element-image,
.slate-element-code,
.slate-element-relation-page,
.slate-element-attachment,
.slate-element-text-diagram,
.slate-element-relation-work-item,
.slate-element-diagram-board,
.#{commonVariables.$wiki-relation-list} {
    .wiki-shared-caret {
        left: -3px !important;
        top: -6px;
    }
}
