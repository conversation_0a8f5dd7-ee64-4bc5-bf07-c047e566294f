@if (uploadingItems$ && uploadingItems$ | async; as uploadingItems) {
  <thy-image-group>
    @for (file of uploadingItems; track file.scopeId) {
      <div class="font-size-sm wiki-file-item file-item-uploading">
        <styx-file-thumbnail
          [styxCodeSnippetOpenType]="codeSnippetOpenType"
          styxSize="md"
          [styxFile]="file | getFileEntity"
          [styxShowImageThumbnail]="false"
        ></styx-file-thumbnail>
        <span thyFlexibleText [thyTooltipContent]="getFileName(file)" class="ml-2 name font-size-base">
          {{ getFileName(file) }}
        </span>
        @if (!(file.result?.status | styxFileUploadingDone)) {
          <thy-progress
            class="progress progress-sm ml-3"
            [thyShape]="'circle'"
            [thyValue]="getPercentage(file)"
            [thyMax]="100"
            [thySize]="16"
            [thyStrokeWidth]="14"
          ></thy-progress>
          <div class="progress-text text-muted mx-2">{{ getPercentage(file) }}%</div>
          <a
            class="link-danger-weak progress-cancel"
            href="javascript:;"
            (click)="removeFile(file, $event)"
            styxI18nTracking
            [thyTooltip]="'common.uploadCancel' | translate"
          >
            <thy-icon thyIconName="close" class="font-size-sm"></thy-icon>
          </a>
        }
      </div>
    }
  </thy-image-group>
}
