.wiki-common-file-uploading {
    width: 100%;
    .wiki-file-item {
        position: relative;
        display: inline-flex;
        flex-direction: row;
        margin-bottom: 0;
        align-items: center;
        width: 100%;
        .name {
            cursor: default;
            line-height: 28px;
            max-width: calc(100% - 106px);
        }

        .file-thumbnail {
            position: relative;
            top: 3px;
            display: inline-block;
            height: fit-content;

            .thumbnail-wrap {
                display: inline-flex;
                width: 16px;
                height: 16px;
            }
        }

        .progress {
            display: inline-flex;
        }

        .progress-circle {
            line-height: initial;
        }

        .progress-text {
            text-wrap: nowrap;
        }

        &.file-item-uploading-simple {
            padding: 0px 0.75rem;
            border-top: none;
            border-radius: 2px;
            max-height: 28px;
        }

        .progress-cancel {
            line-height: 28px;
        }
    }
}
