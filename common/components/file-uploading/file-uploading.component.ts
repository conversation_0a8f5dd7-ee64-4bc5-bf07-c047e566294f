import { ChangeDetectionStrategy, Component, ContentChild, EventEmitter, Input, OnInit, Output, TemplateRef } from '@angular/core';
import { FileUploadingItem, StyxFileUploader } from '@atinc/ngx-styx';
import { Observable } from 'rxjs';

@Component({
    selector: 'file-uploading-items',
    templateUrl: './file-uploading.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'wiki-common-file-uploading d-block px-2'
    },
    standalone: false
})
export class WikiCommonFileUploadingItemsComponent implements OnInit {
    /**
     * Scope Id, 当前文件上传所属 Id，与上传服务设置的 Scope Id 对应，比如工作项附件上传，一般设置为: `work-item-attachments-xxxxx`
     */
    @Input() scopeId: string;

    /**
     * 代码片段打开类型
     * @type 'show | edit | add'
     */
    @Input() codeSnippetOpenType = 'show';

    /**
     * 自定义列表前置显示模板
     */
    @ContentChild('prepend', { static: false }) prependTemplate: TemplateRef<any>;

    /**
     * 取消上传或者删除已上传的文件
     */
    @Output() uploadingAttachmentRemove: EventEmitter<FileUploadingItem> = new EventEmitter<FileUploadingItem>();

    uploadingItems$: Observable<FileUploadingItem[]>;

    uploadingItems: FileUploadingItem[];

    constructor(private fileUploader: StyxFileUploader) {}

    ngOnInit(): void {
        this.uploadingItems$ = this.fileUploader.getUploadingItems$(this.scopeId);
    }

    trackByFn(index: number, item: FileUploadingItem) {
        return item.scopeId;
    }

    removeFile(item: FileUploadingItem, event: Event) {
        event.stopPropagation();
        this.fileUploader.removeUploadingItem(item);
        this.uploadingAttachmentRemove.emit(item);
    }

    getFileName(uploadingItem: FileUploadingItem): string {
        if (uploadingItem.result) {
            return uploadingItem.result.uploadFile.fileName;
        } else {
            return uploadingItem.file['title'] || uploadingItem.file.name;
        }
    }

    getPercentage(uploadingItem: FileUploadingItem): number {
        if (uploadingItem.result) {
            return uploadingItem.result.uploadFile.progress.percentage;
        } else {
            return 0;
        }
    }
}
