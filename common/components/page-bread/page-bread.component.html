<ng-container>
  <thy-breadcrumb thySeparator="slash">
    @if (space && isShowSpaceName) {
      <thy-breadcrumb-item (click)="spaceBreadcrumbSelect()">
        <ng-template
          *ngTemplateOutlet="
            breadcrumbItem;
            context: {
              $implicit: {
                name: space?.name,
                icon: 'pc:app-wiki-fill',
                color: 'rgb(156, 156, 251)',
                is_visibility: true,
                is_space: true
              }
            }
          "
        >
        </ng-template>
      </thy-breadcrumb-item>
    }
    @if (rootPage) {
      <thy-breadcrumb-item (click)="breadcrumbSelect(rootPage?._id)">
        <ng-template *ngTemplateOutlet="breadcrumbItem; context: { $implicit: rootPage }"></ng-template>
      </thy-breadcrumb-item>
    }

    @if (currentPage?.parent_ids?.length > maxParentLength) {
      <thy-breadcrumb-item>...</thy-breadcrumb-item>
    }
    @if (secondToLastPage) {
      <thy-breadcrumb-item class="parent-breadcrumb" (click)="breadcrumbSelect(secondToLastPage._id)">
        <ng-template *ngTemplateOutlet="breadcrumbItem; context: { $implicit: secondToLastPage }"></ng-template>
      </thy-breadcrumb-item>
    }
    <thy-breadcrumb-item class="current-page-breadcrumb" (click)="onPageInputEdit(currentPage)">
      <ng-template *ngTemplateOutlet="breadcrumbItem; context: { $implicit: currentPage }"></ng-template>
      @if (!isShared && currentPage?.is_lock) {
        <span class="lock-status ml-2" styxI18nTracking [thyTooltip]="'common.locked' | translate">
          <thy-icon thyIconName="lock"></thy-icon>
        </span>
      }
      @if (!isShared && isDraftPage) {
        <span
          class="after-page-title-label"
          thyColor="warning"
          thyTheme="weak-fill"
          thyTag
          thySize="sm"
          translate="styx.draft"
          [translateParams]="{ isTitle: true, isPlural: false }"
        ></span>
      }
    </thy-breadcrumb-item>
  </thy-breadcrumb>
</ng-container>

<ng-template #breadcrumbItem let-value>
  <styx-awesome-text
    [ngClass]="{
      'ml-n1': value?.emoji_icon || !value?.parent_ids?.length,
      'has-parent-page': value?.parent_ids?.length > 0,
      'parent-breadcrumb': value._id !== currentPage?._id,
      'cursor-pointer': value._id === currentPage?._id && !readonly,
      'edit-title': editPageId === value._id
    }"
    styxI18nTracking
    [styxText]="value?.name || 'wiki.page.untitled.document' | translate"
    [styxPlaceholder]="placeholder"
    styxAutofocus="true"
    [styxCode]="value?.emoji_icon"
    [styxIconColor]="value | pageIconColor"
    [styxDisabled]="
      (isDisabledClick || (value | isPageGroup) || (!value?.is_visibility && !isShowSpaceName)) &&
      !value.is_space &&
      value._id !== currentPage?._id
    "
    [styxIcon]="value?.icon ? value.icon : !isShowSpaceName && !value?.parent_ids?.length && (value | pageIcon)"
    [styxEdit]="editPageId === value._id"
    (styxBlur)="onPageBlur($event)"
    [styxMaxLength]="titleMaxLength"
  ></styx-awesome-text>
</ng-template>
