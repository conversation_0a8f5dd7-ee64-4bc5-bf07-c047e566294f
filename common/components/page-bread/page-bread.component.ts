import { Component, EventEmitter, inject, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Id, injectBizValidatorMaxLengths, StyxAwesomeTextInputEvent, StyxTranslateService } from '@atinc/ngx-styx';
import { PageInfo } from '@wiki/app/entities/page-info';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { MAX_PARENT_BREADCRUMB } from '@wiki/common/constants/editor';
import { WikiPageBreadcrumbPipe } from '@wiki/common/pipe/page-bread.pipe';
import { IsPageGroupPipe } from '@wiki/common/pipe/page.pipe';
import { getDefaultPageTitle } from '@wiki/common/util/get-default-page-title';

@Component({
    selector: 'wiki-common-page-bread',
    templateUrl: './page-bread.component.html',
    providers: [WikiPageBreadcrumbPipe],
    host: {
        class: 'wiki-common-page-bread'
    },
    standalone: false
})
export class CommonPageBreadComponent implements OnInit, OnChanges {
    translate = inject(StyxTranslateService);

    @Input() isShared: boolean;

    @Input() readonly: boolean = true;

    @Input() isDisabledClick: boolean;

    @Input() currentPage: PageInfo;

    @Input() isDraftPage: boolean;

    @Input() pages: PageInfo[];

    @Input() isShowSpaceName: boolean;

    @Input() space: SpaceInfo;

    @Output()
    selectItem: EventEmitter<{ id: Id; identifier?: string; isSpace?: boolean }> = new EventEmitter();

    @Output() pageTitleChange: EventEmitter<StyxAwesomeTextInputEvent> = new EventEmitter();

    public maxParentLength = MAX_PARENT_BREADCRUMB;

    public rootPage: PageInfo;

    public secondToLastPage: PageInfo;

    protected placeholder: string = '';

    protected editPageId: string = '';

    titleMaxLength = injectBizValidatorMaxLengths().longTitle;

    constructor(
        public pageBreadcrumb: WikiPageBreadcrumbPipe,
        public isPageGroup: IsPageGroupPipe
    ) {}

    ngOnInit() {
        this.getRootPage();
        this.getSecondToLastPage();
        this.placeholder = getDefaultPageTitle(this.currentPage?.type, this.translate);
    }

    ngOnChanges(changes: SimpleChanges): void {
        const currentPageChange = changes.currentPage;
        if (currentPageChange && currentPageChange.currentValue && !currentPageChange.firstChange) {
            this.getRootPage();
            this.getSecondToLastPage();
        }
    }

    spaceBreadcrumbSelect() {
        this.selectItem.emit({
            id: this.space._id,
            identifier: this.space.identifier,
            isSpace: true
        });
    }

    breadcrumbSelect(id: Id) {
        const page = this.pages.find(x => x._id === id);
        if (!this.isDisabledClick && !this.isPageGroup.transform({ type: page.type })) {
            this.selectItem.emit({
                id: page.short_id ?? page._id
            });
        }
    }

    getRootPage() {
        if (this.currentPage?.parent_ids?.length > 0) {
            const pageId = this.currentPage?.parent_ids[0];
            this.rootPage = this.pageBreadcrumb.transform(pageId, this.pages);
        } else {
            this.rootPage = null;
        }
    }

    getSecondToLastPage() {
        if (this.currentPage?.parent_ids?.length >= this.maxParentLength) {
            const pageId = this.currentPage?.parent_ids[this.currentPage?.parent_ids.length - 1];
            this.secondToLastPage = this.pageBreadcrumb.transform(pageId, this.pages);
        } else {
            this.secondToLastPage = null;
        }
    }

    onPageInputEdit(currentPage: PageInfo) {
        if (!this.readonly) {
            this.editPageId = currentPage._id;
        }
    }

    onPageBlur(data: StyxAwesomeTextInputEvent) {
        this.editPageId = null;
        if (data.value !== this.currentPage?.name || data.emoji !== this.currentPage?.emoji_icon) {
            this.pageTitleChange.emit({
                ...data,
                value: data.value || getDefaultPageTitle(this.currentPage?.type, this.translate)
            });
        }
    }
}
