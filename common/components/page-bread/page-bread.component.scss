@use 'ngx-tethys/styles/bootstrap/variables.scss' as variables;
@use '../../styles/variables.scss' as commonVariables;

.wiki-common-page-bread {
    display: flex;
    .thy-breadcrumb-item {
        align-items: center;
        display: inline-flex;
        .disabled,
        .styx-awesome-text-disabled .styx-awesome-text-content {
            pointer-events: none;
            cursor: default;
            color: variables.$gray-600;
        }
        .styx-awesome-text {
            &.edit-title {
                min-width: 400px;
                height: 50px;
                border-bottom: 1px solid commonVariables.$wiki-primary;
            }
            // 编辑页没有表情时，设置不展示表情
            &.edit-hidden-emoji {
                .styx-awesome-text-input {
                    .input-group-prefix {
                        display: none;
                    }
                }
            }
            &-input {
                .thy-input-group {
                    border: 0;
                }
                .hover-icon {
                    color: variables.$primary;
                }
            }
        }
        .styx-awesome-text,
        .default-breadcrumb-text {
            max-width: 12em;
        }
        .styx-awesome-text:not(.styx-awesome-text-disabled) {
            .styx-awesome-text-content {
                color: inherit;
            }
        }
        .parent-breadcrumb:not(.styx-awesome-text-disabled) {
            cursor: pointer;
            &:hover {
                .styx-awesome-text-content,
                .thy-icon {
                    color: variables.$primary;
                }
            }
        }
        .default-breadcrumb-text {
            &:hover {
                color: variables.$primary;
            }
        }
        &:last-child {
            margin-right: 0;
        }
    }
    .current-page-breadcrumb {
        .disabled,
        .styx-awesome-text-disabled .styx-awesome-text-content {
            color: variables.$gray-800;
        }
    }
}
.wiki-page-bread {
    padding-left: 48px;

    .thy-breadcrumb-item a,
    .current-page,
    .has-parent-page {
        vertical-align: bottom;
    }

    .current-page {
        max-width: 690px;
    }

    .has-parent-page {
        max-width: 10em;
    }
}

@media (max-width: 870px) {
    .wiki-page-bread {
        .current-page {
            max-width: 250px;
        }
        .thy-breadcrumb-item {
            .styx-awesome-text-words {
                max-width: 5em;
            }
        }
    }
}

@media screen and (min-width: 2500px) {
    .wiki-page-bread {
        .current-page {
            max-width: 1200px;
        }

        .thy-breadcrumb-item {
            .styx-awesome-text,
            .default-breadcrumb-text,
            .has-parent-page {
                max-width: 20em;
            }
        }
    }
}
