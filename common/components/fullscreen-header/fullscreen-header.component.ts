import { ChangeDetectionStrategy, Component, ElementRef, Input } from '@angular/core';
import { WIKI_FULLSCREEN } from '@wiki/common/constants/page';
import { TheEditor } from '@worktile/theia';
import { FullscreenService } from '../../services/fullscreen.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ThyFullscreen } from 'ngx-tethys/fullscreen';

@Component({
    selector: 'wiki-common-fullscreen-header',
    template: `
        <a thyAction thyTheme="lite" thyIcon="angle-left" href="javascript:;" (pointerup)="exitFullscreen($event)">{{
            'wiki.common.maxView.back' | translate
        }}</a>
    `,
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'wiki-common-fullscreen-header'
    },
    standalone: false
})
export class WikiCommonFullscreenHeaderComponent {
    @Input() editor: TheEditor;

    constructor(
        public element: ElementRef<HTMLElement>,
        public route: ActivatedRoute,
        public router: Router,
        private fullscreenService: FullscreenService
    ) {}

    exitFullscreen(event: MouseEvent | KeyboardEvent) {
        event.stopPropagation();
        const fullscreenContainer = this.element.nativeElement.closest(`.${WIKI_FULLSCREEN}`) as HTMLElement;
        if (this.editor && fullscreenContainer) {
            const fullscreen = this.editor.injector.get(ThyFullscreen);
            fullscreen.exit();
        }
    }
}
