import { Component, computed, Input, OnInit, viewChild, ViewChild } from '@angular/core';
import { Element } from 'slate';
import { PlaitElement } from '@plait/core';
import { WikiPreviewDocumentBaseComponent } from './document/document.base';
import { PageStore } from '@wiki/common/stores/page.store';
import { WikiPreviewDocumentComponent } from './document/document.component';
import { WikiPreviewBoardComponent } from './board/board.component';

@Component({
    selector: 'wiki-common-preview',
    templateUrl: './preview.component.html',
    host: {
        class: 'wiki-common-preview height-100'
    },
    standalone: false
})
export class WikiCommonPreviewComponent extends WikiPreviewDocumentBaseComponent implements OnInit {
    @Input() value: PlaitElement[] | Element[];

    get page() {
        return this.pageStore.snapshot.page;
    }

    readonly editor = computed(() => {
        return this.previewDocumentComponent()?.editor();
    });

    get board() {
        return this.previewBoardComponent?.board;
    }

    readonly previewDocumentComponent = viewChild(WikiPreviewDocumentComponent);

    @ViewChild(WikiPreviewBoardComponent)
    previewBoardComponent: WikiPreviewBoardComponent;

    constructor(private pageStore: PageStore) {
        super();
    }

    ngOnInit() {}
}
