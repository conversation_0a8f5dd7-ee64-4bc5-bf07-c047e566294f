import {
    ChangeDetectorRef,
    Component,
    HostBinding,
    inject,
    Input,
    OnChanges,
    OnInit,
    signal,
    SimpleChanges,
    TemplateRef,
    ViewContainerRef
} from '@angular/core';
import {
    AgileBroadObjectTypes,
    BroadObjectInfo,
    GlobalPublicPathPipe,
    ResponseData,
    ShipBroadObjectTypes,
    StyxBusinessObjectDispatcher,
    StyxTranslateService,
    TesthubBroadObjectTypes
} from '@atinc/ngx-styx';
import { PageExtensionItemInfo } from '@wiki/app/entities/page-extendsion';
import { PageApiService } from '@wiki/app/services/page-api.service';
import { WIKI_EDIT_SCROLL_CONTAINER } from '@wiki/common/constants/page';
import { Discussion, RelationItemElement, WikiElement } from '@wiki/common/custom-types';
import { WikiCommonPlugins } from '@wiki/common/plugins';
import { createPageHistoryPlugin, editableForCheckItem } from '@wiki/common/plugins/common.plugin';
import { DiscussionEditor } from '@wiki/common/plugins/discussion/discussion.editor';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { PageStore } from '@wiki/common/stores/page.store';
import { WikiEditorOptions, WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiPluginMenuSvgs } from '@wiki/common/types/plugin-menu';
import { SyncPageOperation, SyncPageOperationType, WikiPluginEmptySvgs } from '@wiki/common/types/relation-types';
import { getDecorationSelection, verifyAvailableAppAndPermission } from '@wiki/common/util/common';
import { ElementKinds, TableElement, TheDataMode, TheEditor, ThePlugin, TheQueries, TodoItemElement } from '@worktile/theia';
import { TheTable } from '@worktile/theia/plugins/table/components/table.component';
import { ThyIconRegistry } from 'ngx-tethys/icon';
import { ThyPopover } from 'ngx-tethys/popover';
import { Observable, of } from 'rxjs';
import { BaseRange, Element, Node, NodeEntry, Operation, Range, Transforms } from 'slate';
import { AngularEditor, ELEMENT_TO_COMPONENT, IS_SAFARI } from 'slate-angular';
import { WikiPreviewDocumentBaseComponent } from './document.base';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { HeadingHandleScene } from '@wiki/common/plugins/heading-handle/constants';
import { createDisabledHeadingFoldPlugin } from '@wiki/common/plugins/heading-handle/with-disabled-heading-fold.plugin';

interface DecorationInfo {
    selection: Range;
    discussion: Discussion;
}

@Component({
    selector: 'wiki-preview-document',
    templateUrl: './document.component.html',
    providers: [GlobalPublicPathPipe],
    standalone: false
})
export class WikiPreviewDocumentComponent extends WikiPreviewDocumentBaseComponent implements OnInit, OnChanges {
    translate = inject(StyxTranslateService);

    @Input() value: Element[] = [];

    @HostBinding('class')
    hostClass = 'wiki-preview-document h-100';

    headingHandleScene = HeadingHandleScene.preview;

    editor = signal<TheEditor>(null);

    options: WikiEditorOptions = {
        readonly: true,
        mode: TheDataMode.json,
        scrollContainer: WIKI_EDIT_SCROLL_CONTAINER,
        placeholderDecorate: (e: TheEditor) => []
    };

    isSafari = IS_SAFARI;

    customPlugins: ThePlugin[] = [];

    discussions$: Observable<Discussion[]>;

    discussions: Discussion[];

    selection: BaseRange;

    resources = [
        {
            relationType: ShipBroadObjectTypes.idea,
            pluginType: WikiPluginTypes.relationIdea,
            icon: 'bulb',
            title: this.translate.instant<I18nSourceDefinitionType>('wiki.resource.productRequirement')
        },
        {
            relationType: AgileBroadObjectTypes.workItem,
            pluginType: WikiPluginTypes.relationWorkItem,
            icon: 'task-board',
            title: this.translate.instant<I18nSourceDefinitionType>('styx.workItem', { isTitle: true, isPlural: false })
        },
        {
            relationType: TesthubBroadObjectTypes.testCase,
            pluginType: WikiPluginTypes.relationTestCase,
            icon: 'app-testhub',
            title: this.translate.instant<I18nSourceDefinitionType>('styx.testCase', { isTitle: true, isPlural: false })
        }
    ];

    broadObjectType = AgileBroadObjectTypes.workItem;

    decorationInfo: DecorationInfo;

    createWorkItemActive = false;

    get page() {
        return this.pageStore.snapshot.page;
    }

    haveResources = true;

    discussionDecorate = (editor: TheEditor, nodeEntry: NodeEntry): Range[] => [];

    constructor(
        public wikiPluginContext: WikiPluginContext,
        private styxBusinessObjectDispatcher: StyxBusinessObjectDispatcher,
        private thyPopover: ThyPopover,
        private pageStore: PageStore,
        private pageApiService: PageApiService,
        private viewContainerRef: ViewContainerRef,
        private iconRegistry: ThyIconRegistry,
        private cdr: ChangeDetectorRef
    ) {
        super();
    }

    ngOnInit() {
        this.options = {
            ...this.options,
            form: this.form
        };
        this.initializePlugins();
        this.initializeMenuIcon();
        this.initializeDecorate();
        this.discussions$ = this.wikiPluginContext.getDiscussions();
        this.discussions$.subscribe(discussions => {
            this.discussions = discussions;
            this.initializeDecorate();
            this.cdr.detectChanges();
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        const isFullscreen = changes.isFullScreen;
        if (isFullscreen) {
            const tableNode = this.editor()?.children.filter(item => item.type === ElementKinds.table) as TableElement[];
            if (tableNode) {
                tableNode.map(item => {
                    const tableComponent = ELEMENT_TO_COMPONENT.get(item) as TheTable;
                    this.editor.update(editor => {
                        editor.options.scrollContainer = WIKI_EDIT_SCROLL_CONTAINER;
                        return editor;
                    });
                    tableComponent.isStickyTop = false;
                    tableComponent.setHeaderRowStyle();
                });
            }
        }
    }

    initializePlugins() {
        const customPlugins = WikiCommonPlugins(this.translateService);
        if (this.form === 'history') {
            customPlugins.push(createPageHistoryPlugin());
        }
        if (this.hasEditPermission) {
            editableForCheckItem(customPlugins);
        }
        if (!this.hasHeadingFold()) {
            customPlugins.push(createDisabledHeadingFoldPlugin(this.translateService));
        }
        this.customPlugins = customPlugins;
    }

    initializeResourcesMenu() {
        this.resources = this.resources.filter(resource => {
            return verifyAvailableAppAndPermission(this.translate, this.editor(), resource.pluginType);
        });
        this.haveResources = this.resources.length > 0;
    }

    initializeDecorate() {
        this.discussionDecorate = (editor: TheEditor, nodeEntry: NodeEntry) => {
            return DiscussionEditor.discussionDecorate(editor, nodeEntry, this.discussions);
        };
    }

    initializeMenuIcon() {
        WikiPluginMenuSvgs.forEach(menuItem => {
            this.iconRegistry.addSvgIconLiteral(menuItem.key, menuItem.svg);
        });
        WikiPluginEmptySvgs.forEach(menuItem => {
            this.iconRegistry.addSvgIconLiteral(menuItem.key, menuItem.svg);
        });
    }

    editorCreate(editor: TheEditor) {
        this.editor.set(editor);
        if (this.isDecoration) {
            this.initializeResourcesMenu();
        }
    }

    theValueChange(value: Element[]) {
        const editor = this.editor();
        if (this.hasEditPermission) {
            editor.operations
                .filter(x => x.type === 'set_node')
                .forEach(x => {
                    if (Operation.isNodeOperation(x)) {
                        const element = Node.get(editor, x.path) as WikiElement;
                        if (element.type === ElementKinds.checkItem) {
                            this.pageApiService
                                .updateOnlyPageCheckItem(this.page._id, element.key, (element as TodoItemElement).checked)
                                .subscribe();
                        }
                    }
                });
        }
    }

    getDiscussionLeafElement(decoration: DecorationInfo) {
        const editor = this.editor();
        const slateNode = Node.get(editor, decoration.selection.anchor.path);
        const nodeElement = AngularEditor.toDOMNode(editor, slateNode);
        const targetLeafElement = nodeElement.querySelector('.the-discussion-leaf') as HTMLElement;
        return targetLeafElement;
    }

    initializeDiscussion(event: Event) {
        const native = window.getSelection();
        const decoration = getDecorationSelection(this.editor(), native);
        this.pageStore.pureAddDiscussions([decoration.discussion]);

        setTimeout(() => {
            const targetLeafElement = this.getDiscussionLeafElement(decoration);
            this.openDiscussion(targetLeafElement, decoration.discussion);
            this.initializeDecorate();
            this.cdr.detectChanges();
        });
    }

    openCreateResource(event: Event, template: TemplateRef<HTMLElement>) {
        event.preventDefault();

        const editor = this.editor();
        const native = window.getSelection();
        const origin = (event.target as HTMLElement)?.closest('.expanding-toolbar ') as HTMLElement;

        this.decorationInfo = getDecorationSelection(editor, native);
        this.createWorkItemActive = true;
        this.selection = editor.selection;

        const popoverRef = this.thyPopover.open(template, {
            hasBackdrop: true,
            backdropClosable: true,
            insideClosable: true,
            placement: 'rightTop',
            panelClass: 'wiki-create-resources',
            offset: 0,
            origin,
            viewContainerRef: this.viewContainerRef
        });
        popoverRef?.afterClosed().subscribe(() => {
            this.createWorkItemActive = false;
        });
    }

    addResource(event: Event, resource: BroadObjectInfo) {
        const editor = this.editor();
        this.styxBusinessObjectDispatcher.openObjectCreation(resource.relationType, {
            defaultValues: {
                title: this.decorationInfo.discussion.quote
            },
            hideContinued: true,
            onSuccess: (data: ResponseData<PageExtensionItemInfo, any>) => {
                if (this.addRelationResource) {
                    this.addRelationResource(data.value as PageExtensionItemInfo, resource.relationType);
                }

                const selectionEnd = Range.end(this.selection);
                const nodeEntry = TheQueries.getBlockAbove(editor, { at: selectionEnd });
                const node: RelationItemElement = {
                    type: resource.pluginType,
                    mode: 'text',
                    _id: data.value._id,
                    children: [{ text: '' }]
                };
                Transforms.insertNodes(editor, node, { at: selectionEnd });
                const newNode = TheQueries.getNode(editor, nodeEntry[1]);
                const operation: SyncPageOperation = {
                    type: SyncPageOperationType.replaceChildren,
                    node: nodeEntry[0],
                    newNode
                };

                this.pageApiService.syncPageContent(this.page._id, (newNode as WikiElement).key, operation).subscribe(() => {});
                return of(null);
            }
        });
    }
}
