import { Directive, inject, input, Input, ViewChild } from '@angular/core';
import { TheEditorComponent } from '@worktile/theia';
import { PageExtensionItemInfo } from '@wiki/app/entities/page-extendsion';
import { StyxTranslateService } from '@atinc/ngx-styx';

@Directive()
export class WikiPreviewDocumentBaseComponent {
    @Input() isFullScreen: boolean;

    @Input() isDecoration = false;

    hasHeadingFold = input(true);

    hasHeadingAnchor = input(false);

    @Input() isStencil = false;

    @Input() hasEditPermission: boolean;

    @Input() pageContentContainer: string;

    @Input() form: string;

    @Input() openDiscussion: (target: HTMLElement, discussion: any) => {};

    @Input() addRelationResource?: (resource: PageExtensionItemInfo, type: string) => void;

    @ViewChild(TheEditorComponent)
    theEditorComponent: TheEditorComponent;

    public translateService = inject(StyxTranslateService);
}
