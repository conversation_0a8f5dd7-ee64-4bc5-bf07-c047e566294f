<ng-container>
  @if (isDecoration && !page?.is_deleted) {
    <wiki-common-expanding-toolbar [editor]="editor()" [afterTemplate]="hoverAfterTemplate">
      <ng-template #hoverAfterTemplate>
        <a href="javascript:;" thyAction thyActionIcon="comment-add" (click)="initializeDiscussion($event)">
          <span translate="wiki.preview.comment"></span>
        </a>
        @if (haveResources && !page?.is_lock && !isStencil) {
          <thy-divider class="mx-2" [thyVertical]="true" thyColor="light"></thy-divider>
          <a
            href="javascript:;"
            class="the-editor-operation"
            thyAction
            thyActionIcon="plus-circle-thin"
            [thyActionActive]="createWorkItemActive"
            (click)="openCreateResource($event, createResourceTemplate)"
          >
            <span translate="wiki.preview.createItem"></span>
          </a>
        }
      </ng-template>
    </wiki-common-expanding-toolbar>
  }

  <the-editor
    [ngClass]="{ 'is-safari-full-screen': isSafari && !!isFullScreen }"
    [ngModel]="value"
    [theOptions]="options"
    [thePlugins]="customPlugins"
    [theDecorate]="discussionDecorate"
    (ngModelChange)="theValueChange($event)"
    (theEditorCreated)="editorCreate($event)"
  ></the-editor>

  @if (editor() && (hasHeadingFold() || hasHeadingAnchor())) {
    <div
      wikiHeadingHandle
      [isFullScreen]="isFullScreen"
      [editor]="editor()"
      [triggerOriginContainerClass]="pageContentContainer"
      [scene]="headingHandleScene"
      [hasHeadingAnchor]="hasHeadingAnchor() || false"
      [hasHeadingFold]="hasHeadingFold() || true"
    ></div>
  }

  @if (editor() && !isFullScreen) {
    <div class="fullscreen-hidden" wikiPreviewToolbar [page]="page" [editor]="editor()" [pageContentContainer]="pageContentContainer"></div>
  }

  @if (isDecoration) {
    <side-discussions class="fullscreen-hidden" [discussions]="discussions$ | async" [theValue]="value"></side-discussions>
  }
</ng-container>

<ng-template #createResourceTemplate>
  <div class="thy-dropdown-menu">
    @for (resource of resources; track $index) {
      <a thyDropdownMenuItem (click)="addResource($event, resource)">
        <thy-icon [thyIconName]="resource.icon" thyDropdownMenuItemIcon></thy-icon>
        <span thyDropdownMenuItemName>{{ resource.title }}</span>
      </a>
    }
  </div>
</ng-template>
