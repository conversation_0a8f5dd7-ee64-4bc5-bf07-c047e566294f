@use 'ngx-tethys/styles/variables.scss';

.is-safari-full-screen input[type='checkbox']:after {
    top: 2px;
    left: 0px;
    width: 7px;
    height: 10px;
}

.is-safari-full-screen {
    .slate-element-alert .the-alert {
        padding-left: 42px;
    }

    .thy-alert .thy-alert-icon {
        top: 11px;
    }

    .the-check-item input[type='checkbox'] {
        transform: translateY(1px);
    }
}

.wiki-preview-document {
    position: relative;
    display: block;

    .the-editor-readonly .the-editor-typo {
        font-size: 15px;
    }
}
