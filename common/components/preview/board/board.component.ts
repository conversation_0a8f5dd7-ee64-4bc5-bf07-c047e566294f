import {
    AfterViewInit,
    ChangeDetector<PERSON>ef,
    Component,
    Inject,
    Input,
    NgZone,
    OnChanges,
    OnInit,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import { PlaitEditorComponent } from '@plait-editor/editor/editor.component';
import { PLOptions } from '@plait-editor/types/editor';
import { BoardTransforms, PlaitBoard, PlaitElement, Point, calcNewViewBox, getViewportContainerRect } from '@plait/core';
import { BOARD_EXPORT_CONFIG } from '@wiki/common/constants/page';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { BoardOptions } from '@wiki/common/interface/page';
import { THE_MODE_TOKEN, TheModeConfig } from '@worktile/theia';
import { take } from 'rxjs';

@Component({
    selector: 'wiki-preview-board',
    templateUrl: './board.component.html',
    host: {
        class: 'wiki-preview-board'
    },
    standalone: false
})
export class WikiPreviewBoardComponent implements OnInit, AfterViewInit, OnChanges {
    @Input() set value(value: PlaitElement[]) {
        if (!value) {
            this._value = [];
        } else {
            this._value = value;
        }
    }

    get value() {
        return this._value;
    }

    @Input() boardOptions: BoardOptions;

    options: Partial<PLOptions> = {
        readonly: true,
        hideScrollbar: false
    };

    board!: PlaitBoard;

    _value: PlaitElement[];

    isInitPlaitEditor = false;

    @ViewChild(PlaitEditorComponent)
    plaitEditorComponent: PlaitEditorComponent;

    constructor(
        private ngZone: NgZone,
        @Inject(THE_MODE_TOKEN)
        private modeConfig: TheModeConfig,
        private cdr: ChangeDetectorRef
    ) {}

    ngOnInit() {
        if (this.modeConfig.mode === TheExtensionMode.print) {
            this.options = {
                ...this.options,
                hideScrollbar: true,
                mode: 'embed'
            };
        }
    }

    ngAfterViewInit() {
        // 解决 dialog 动画导致 board 获取宽高时不准确的问题
        setTimeout(() => {
            this.isInitPlaitEditor = true;
            this.cdr.markForCheck();
        }, 150);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes?.value && !changes.value.isFirstChange()) {
            this.ngZone.onStable.pipe(take(1)).subscribe(() => {
                if (this.boardOptions?.viewportMode !== 'useEditSetting') {
                    this.focusCenter(this.board);
                } else {
                    BoardTransforms.updateViewport(this.board, this.boardOptions.viewport?.origination, this.boardOptions?.viewport?.zoom);
                }
            });
        }
    }

    plaitBoardInitialized(board: PlaitBoard) {
        this.board = board;
        this.ngZone.onStable.pipe(take(1)).subscribe(() => {
            if (this.modeConfig.mode === TheExtensionMode.print) {
                // 限制高度为 A4 内容可用高度
                BoardTransforms.fitViewportWidth(this.board, {
                    limitHeight: BOARD_EXPORT_CONFIG.a4Height,
                    maxWidth: BOARD_EXPORT_CONFIG.a4Width,
                    containerClass: 'wiki-preview-board',
                    autoFitPadding: BOARD_EXPORT_CONFIG.autoFitPadding
                });
                return;
            }
            this.plaitEditorComponent?.focus();
            if (this.boardOptions?.viewportMode !== 'useEditSetting') {
                this.focusCenter(this.board);
            }
        });
    }

    focusCenter(board: PlaitBoard, customWidth?: number) {
        const newViewportContainerRect = getViewportContainerRect(board);
        const zoom = 1;
        const viewBox = calcNewViewBox(board, zoom);
        const centerX = viewBox[0] + viewBox[2] / 2;
        const centerY = viewBox[1] + viewBox[3] / 2;
        const autoFitPadding = 16;

        let x = 0;
        if (customWidth) {
            x = centerX - (customWidth + autoFitPadding) / 2 / zoom;
        } else {
            x = centerX - newViewportContainerRect.width / 2 / zoom;
        }
        const newOrigination = [x, centerY - newViewportContainerRect.height / 2 / zoom] as Point;
        BoardTransforms.updateViewport(board, newOrigination, zoom);
    }
}
