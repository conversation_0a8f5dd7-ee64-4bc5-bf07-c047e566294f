<div class="d-block common-plugin-card-element" style="overflow: hidden">
  @if (!isEmpty) {
    <div
      contenteditable="false"
      class="relation-list"
      contenteditable="false"
      thyResizable
      [thyMinHeight]="minHeight"
      [thyBounds]="resizeBounds"
      [style.height]="height"
      (thyResize)="onResize($event)"
      (thyResizeEnd)="onEndResize()"
    >
      <div
        class="relation-list-scroll border-radius-covering"
        [ngClass]="{ 'edit-setover-height': (resizeHeight || element?.height) && !readonly }"
      >
        <styx-pivot-table
          [styxType]="option.broadObjectType"
          [styxData]="data"
          styxMode="lite"
          [styxColumns]="columns"
          [styxReferences]="references"
          [styxLoadingDone]="loadingDone"
          [styxSize]="isFullscreen ? 'default' : 'sm'"
          [styxHeight]="isFullscreen ? 'calc(100vh - 80px)' : height"
          [styxRowClickable]="!isSharedMode && isAvailableApp"
          (styxRowClick)="styxRowClick($event)"
        ></styx-pivot-table>
      </div>
      @if (isCollapsedAndNonReadonly && !isFullscreen) {
        <thy-resize-handle thyDirection="bottom" class="relation-list-resize-handle"></thy-resize-handle>
      }
    </div>
  } @else {
    <common-empty-content [iconName]="iconName" [text]="emptyText" [showCardStyle]="false"></common-empty-content>
  }
</div>

<ng-template #toolbar>
  <thy-actions thySize="xxs">
    @if (isAvailableApp) {
      <a
        thyAction
        href="javascript:;"
        thyActiveClass="active"
        thyActionIcon="edit"
        styxI18nTracking
        [thyTooltip]="'common.edit' | translate"
        (click)="openSelect()"
      >
      </a>
      <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
      @if (!isEmpty) {
        <a
          href="javascript:;"
          thyAction
          thyActionIcon="horizontal-two-lines"
          styxI18nTracking
          [thyTooltip]="'common.setUp' | translate"
          thyTooltipPlacement="top"
          (click)="openEdit($event)"
        ></a>
        <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
      }
    }
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      thyTooltipPlacement="top"
      (click)="removeNode($event)"
    ></a>
  </thy-actions>
</ng-template>

<ng-template #placeholderTemplate>
  <div class="common-placeholder-loading">
    <thy-icon thyIconName="task-board"></thy-icon>
  </div>
</ng-template>
