@use 'ngx-tethys/styles/variables.scss';

.common-relation-list-container {
    .relation-list {
        position: inherit !important; // 组件库 resiable 指令，setStyle position: relative
    }
    .relation-list-resize-handle {
        cursor: row-resize;
        height: 8px;
        width: 45px;
        left: 50%;
        transform: translateX(-50%);
        background: variables.$gray-300;
        bottom: -4px;
        border-radius: 5px;
        position: absolute;

        &::before,
        &:after {
            content: '';
            position: absolute;
            width: 34px;
            height: 1px;
            background: variables.$bg-default;
            left: 50%;
            transform: translateX(-50%);
            top: 2px;
        }

        &:after {
            top: 5px;
        }
    }
    // 业务组件库表格 .styx-table-body 设置了 height:0，导致预览页面不会根据内容自动展开
    &:not(.wiki-fullscreen-node) {
        .relation-list-scroll:not(.edit-setover-height) {
            .styx-table .styx-table-body {
                height: auto;
            }
        }
    }
}

.the-editor.the-editor-readonly {
    .common-relation-list-container {
        // 工作项列表需要复写预览页无边框
        .common-plugin-card-element {
            border: none;
        }
    }
}
