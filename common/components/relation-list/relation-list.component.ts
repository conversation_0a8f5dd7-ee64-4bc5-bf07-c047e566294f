import { Overlay } from '@angular/cdk/overlay';
import { Component, HostBinding, OnD<PERSON>roy, OnInit, TemplateRef, ViewChild, ViewContainerRef, inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
    PropertyColumn,
    StyxBroadObjectDispatcher,
    TeamsBroadObjectTypes,
    broadObjectDefinitions,
    getObjectApplicationByType
} from '@atinc/ngx-styx';
import { ApplicationContext, ResponseData } from '@atinc/ngx-styx/core';
import { StyxTranslateService } from '@atinc/ngx-styx/i18n/core';
import { StyxPivotTableReferenceInfo } from '@atinc/ngx-styx/pivot/table/entities';
import { PageExtensionInfo, PageExtensionReferences } from '@wiki/app/entities/page-extendsion';
import { PageApiService } from '@wiki/app/services';
import {
    DefaultPropertyColumns,
    DefaultUnremovableProperties,
    PageModeOption,
    wikiRelationListEditorMap
} from '@wiki/common/custom-constants';
import {
    RelationIdeaListElement,
    RelationObjectiveListElement,
    RelationTestCaseListElement,
    RelationTicketListElement,
    RelationWorkItemListElement
} from '@wiki/common/custom-types';
import { RelationApiService } from '@wiki/common/services';
import { FullscreenService, FullscreenState } from '@wiki/common/services/fullscreen.service';
import { PageStore } from '@wiki/common/stores/page.store';
import { getCommonRelationOption, RelationListEditor, RelationListOption } from '@wiki/common/types/relation-types';
import { ClientEventEmitter } from '@wiki/common/util/bridge-for-mobile';
import {
    TheBaseElement,
    TheContextService,
    TheModeType,
    TheQueries,
    createEmptyParagraph,
    getMode,
    topLeftPosition,
    updatePopoverPosition
} from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { ThyResizeEvent } from 'ngx-tethys/resizable';
import { ThySlideService } from 'ngx-tethys/slide';
import { ThyTableRowEvent } from 'ngx-tethys/table';
import { SafeAny } from 'ngx-tethys/types';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { Editor, Transforms } from 'slate';
import { AngularEditor, SlateElementContext } from 'slate-angular';
import { TheExtensionMode } from '../../interface/extension-mode';
import { getOutsideOptions, verifyAvailableAppAndPermission } from '../../util/common';
import { isMobileMode, isSharedMode } from '../../util/extension-mode';
import { CommonPropertySettingComponent } from '../property-setting/property-setting.component';

type CommonRelationElement =
    | RelationWorkItemListElement
    | RelationTestCaseListElement
    | RelationIdeaListElement
    | RelationTicketListElement
    | RelationObjectiveListElement;

@Component({
    selector: 'common-relation-list, [commonRelationList]',
    templateUrl: './relation-list.component.html',
    standalone: false
})
export class CommonRelationListComponent extends TheBaseElement<CommonRelationElement, Editor> implements OnInit, OnDestroy {
    translate = inject(StyxTranslateService);

    @HostBinding('class') className = 'common-relation-list-container';

    popoverRef: ThyPopoverRef<any>;

    resizeHeight: number;

    isFullscreen: boolean;

    fullscreen$: Subscription;

    resizeBounds = null;

    minHeight: number;

    option: RelationListOption;

    loadingDone: boolean;

    data: SafeAny;

    references: StyxPivotTableReferenceInfo;

    columns: PropertyColumn[] = [];

    get height(): string {
        return this.readonly ? '100%' : `${this.resizeHeight || this.element.height}px`;
    }

    get isToolbarOpened() {
        return this.popoverRef && this.popoverRef.getOverlayRef() && this.popoverRef.getOverlayRef().hasAttached();
    }

    @ViewChild('toolbar', { read: TemplateRef, static: true })
    toolbar: TemplateRef<any>;

    isAvailableApp = false;

    isSharedMode: boolean;

    iconName: string;

    emptyText: string;

    relationEditor: RelationListEditor;

    mode: TheModeType;

    get extensionId() {
        return this.element?.extension_id;
    }

    get pageId() {
        return this.pageStore.snapshot.page._id;
    }

    get isEmpty() {
        return !(this.data?.length || !this.loadingDone);
    }

    isInPingCodePortal() {
        return !isMobileMode(this.mode) && !isSharedMode(this.mode as TheExtensionMode);
    }

    outsideOptions: PageModeOption = {};

    public router = inject(Router);
    public pageStore = inject(PageStore);
    public relationApiService = inject(RelationApiService);
    public viewContainerRef = inject(ViewContainerRef);
    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);
    private contextService = inject(TheContextService);
    private fullscreenService = inject(FullscreenService);
    private thySlideNewService = inject(ThySlideService);
    private applicationContext = inject(ApplicationContext);
    private pageApiService = inject(PageApiService);
    private route = inject(ActivatedRoute);
    private styxBroadObjectDispatcher = inject(StyxBroadObjectDispatcher);

    ngOnInit() {
        super.ngOnInit();
        this.fullscreenChange();
        this.isSharedMode = isSharedMode(this.mode as TheExtensionMode);
        this.outsideOptions = getOutsideOptions(this.mode, this.route);
        this.fetchPageExtension();
        this.setRelationEditor();
        this.setOption();
        this.resizeBounds = {
            nativeElement: this.contextService.getEditableElement()
        };
        if (this.isInPingCodePortal()) {
            this.isAvailableApp = verifyAvailableAppAndPermission(this.translate, this.editor, this.element.type);
        }
        this.iconName = getCommonRelationOption(this.translate)[this.element.type].icon;
        this.emptyText = getCommonRelationOption(this.translate)[this.element.type].notFoundText;
    }

    onContextChange() {
        super.onContextChange();
        if (!this.mode) {
            this.mode = getMode(this.editor);
        }
        if (isMobileMode(this.mode)) {
            return;
        }
        if (TheQueries.isGlobalCollapsed(this.editor) && this.isCollapsedAndNonReadonly) {
            this.openToolbar();
        } else {
            this.closeToolbar();
        }
    }

    beforeContextChange(context: SlateElementContext<CommonRelationElement>) {
        if (this.initialized) {
            if (context.element?.extension_id !== this.extensionId) {
                this.fetchPageExtension(context.element?.extension_id);
            } else {
                if (context.element.data.columns !== this.element.data.columns) {
                    this.buildColumns(context.element.data.columns);
                }
            }
        }
    }

    ngOnDestroy() {
        this.fullscreen$?.unsubscribe();
    }

    fetchPageExtension(extensionId?: string) {
        let mode: TheExtensionMode;
        let fetchId = this.pageId;
        switch (this.mode) {
            case TheExtensionMode.print:
            case TheExtensionMode.outsideSpace:
            case TheExtensionMode.outsidePage:
                mode = this.outsideOptions.mode;
                fetchId = this.outsideOptions.fetchId;
                break;
            case TheExtensionMode.stencils:
                mode = this.mode;
                break;
            default:
                mode = TheExtensionMode.default;
        }

        this.loadingDone = false;
        this.pageApiService
            .fetchPageExtension(fetchId, extensionId || this.extensionId, mode)
            .pipe(finalize(() => (this.loadingDone = true)))
            .subscribe((response: ResponseData<PageExtensionInfo, PageExtensionReferences>) => {
                const { items, ...arg } = response.references;
                this.data = items || [];
                const properties = arg.properties ? this.updateProperty(arg) : [];
                this.references = {
                    ...arg,
                    properties
                };
                this.buildColumns(this.element.data.columns);
                this.updateHeightLimit();
                this.cdr.markForCheck();
            });
    }

    buildColumns(columns: PropertyColumn[]) {
        // 如果没有列，则按照插件类型添加默认列
        if (!columns) {
            columns = DefaultPropertyColumns[this.element.type] || [];
        }
        const properties = this.references.properties;
        if (properties?.length) {
            this.columns = columns.filter(column => properties.some(property => property.raw_key === column.property_key));
        }
    }

    setRelationEditor() {
        this.relationEditor = wikiRelationListEditorMap[this.element.type];
    }

    //TODO： 各个子产品统一完下拉单选字段类型后可删除
    updateProperty(references: StyxPivotTableReferenceInfo) {
        let priorityProperty = references.properties.find(item => item.raw_key === 'priority');
        let priorityIndex = references.properties.findIndex(item => item.raw_key === 'priority');
        if (priorityProperty) {
            references.properties[priorityIndex] = {
                ...priorityProperty,
                type: 6,
                option_style: priorityProperty.option_style ? priorityProperty.option_style : 2,
                options: priorityProperty.options?.map(item => {
                    return {
                        _id: item._id,
                        bg_color: item.bg_color,
                        text: item.text
                    };
                })
            };
        }
        return references.properties;
    }

    openSelect = () => {
        this.relationEditor.selectItems(
            this.editor,
            (data: PageExtensionInfo, references) => {
                const { items, ...arg } = references;
                const properties = this.updateProperty(arg);
                this.references = {
                    ...arg,
                    properties
                };
                this.data = items || [];
                this.relationEditor.setAttribute(this.editor, this.element, { extension_id: data._id });
                this.updateHeightLimit();
                this.cdr.markForCheck();
            },
            this.element.key,
            this.data || []
        );
    };

    updateColumns = (columns: PropertyColumn[]) => {
        this.relationEditor.setAttribute(this.editor, this.element, { data: { columns } });
    };

    setOption() {
        const option = getCommonRelationOption(this.translate)[this.element.type];
        this.option = option;
        if (!(this.isSharedMode || this.mode === TheExtensionMode.print)) {
            const applicationInfo = this.getApplicationInfo();
            if (applicationInfo) {
                const { api_prefix } = applicationInfo;
                this.option.apiPrefix = api_prefix;
            }
        }
    }

    fullscreenChange() {
        this.fullscreen$ = this.fullscreenService.subscribe(this.element, value => {
            this.isFullscreen = value.state === FullscreenState.on;
            this.cdr.detectChanges();
        });
    }

    getApplicationInfo() {
        const broadObjectDefinitionInfo = broadObjectDefinitions[this.option.broadObjectType];
        return this.applicationContext.getApp(broadObjectDefinitionInfo.application);
    }

    openEdit(e: Event) {
        const unremovableProperties =
            this.option.broadObjectType === TeamsBroadObjectTypes.objective ? ['name'] : DefaultUnremovableProperties;
        this.thySlideNewService.open(CommonPropertySettingComponent, {
            panelClass: ['thy-slide', 'property-setting-slide-container'],
            initialState: {
                option: this.option,
                columns: this.columns,
                unremovableProperties,
                updateColumns: this.updateColumns
            }
        });
    }

    openToolbar() {
        if (this.isToolbarOpened) {
            return;
        }

        const origin = this.elementRef.nativeElement;
        this.popoverRef = this.thyPopover.open(this.toolbar, {
            origin,
            viewContainerRef: this.viewContainerRef,
            panelClass: 'the-plugin-toolbar-popover',
            minWidth: 0,
            placement: 'topLeft',
            hasBackdrop: false,
            manualClosure: true,
            insideClosable: false,
            outsideClosable: false,
            scrollStrategy: this.overlay.scrollStrategies.reposition(),
            autoAdaptive: true
        });

        const overlayRef = this.popoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    closeToolbar() {
        if (this.isToolbarOpened) {
            this.popoverRef.close();
        }
    }

    updateHeightLimit() {
        const tableHeaderHeight = 52;
        const itemHeight = 52;
        if (this.data?.length > 0) {
            this.minHeight = tableHeaderHeight + itemHeight;
        }
    }

    onResize({ height }: ThyResizeEvent) {
        this.resizeHeight = height;
    }

    onEndResize() {
        if (!this.readonly) {
            const at = AngularEditor.findPath(this.editor, this.element);
            Transforms.setNodes(this.editor, { height: this.resizeHeight }, { at });
        }
    }

    removeNode(e: Event) {
        const at = AngularEditor.findPath(this.editor, this.element);
        Transforms.removeNodes(this.editor, { at });
        Transforms.insertNodes(this.editor, createEmptyParagraph(), { at });
        AngularEditor.focus(this.editor);
    }

    styxRowClick(event: ThyTableRowEvent) {
        setTimeout(() => {
            const application = getObjectApplicationByType(this.option.broadObjectType);
            this.styxBroadObjectDispatcher.dispatchOpenBroadObjectEvent({
                _id: event.row.short_id || event.row._id,
                type: this.option.broadObjectType,
                application: application
            });
        });
        if (isMobileMode(this.mode)) {
            ClientEventEmitter.open(this.element.type, event.row._id);
            return;
        }
    }
}
