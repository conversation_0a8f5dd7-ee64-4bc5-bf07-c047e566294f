<thy-layout>
  <thy-sidebar thyHasBorderRight="true" thyTheme="light" class="h-100" thyWidth="260">
    <thy-sidebar-header styxI18nTracking [thyTitle]="'wiki.material.library' | translate"></thy-sidebar-header>
    <thy-sidebar-content>
      <thy-menu thyTheme="loose" class="pt-0">
        @for (menu of menus; track menu.key) {
          <a href="javascript:;" thyMenuItem [class.active]="menu.key === activeMenu?.key" (click)="selectMenu(menu)">
            <span thyMenuItemName>{{ menu.name }}</span>
          </a>
        }
        <thy-divider></thy-divider>
        <thy-menu-group styxI18nTracking [thyTitle]="'wiki.material.useCase' | translate">
          @for (scene of scenes; track scene.key) {
            <a href="javascript:;" thyMenuItem [class.active]="scene.key === activeScene?.key" (click)="selectScene(scene)">
              <span thyMenuItemName>{{ scene.name }}</span>
            </a>
          }
        </thy-menu-group>
      </thy-menu>
    </thy-sidebar-content>
  </thy-sidebar>
  <thy-layout>
    <thy-dialog-header [thyTitle]="activeMenu?.name || activeScene?.name"></thy-dialog-header>
    <thy-dialog-body>
      <div class="d-flex align-items-center">
        <div class="wiki-material-operations">
          <thy-input-search
            styxI18nTracking
            [placeholder]="'common.search' | translate"
            [(ngModel)]="keywords"
            (thyEnter)="searchKeywords()"
            (clear)="clearKeywords()"
          ></thy-input-search>
        </div>
      </div>

      @if (materialFetcher.loadingDone()) {
        @if (materials.length > 0) {
          <ul class="wiki-material-list">
            @for (item of materials; track item?._id) {
              <li class="wiki-material-item">
                <div class="image">
                  <img [src]="item.cover" [alt]="item.name" />

                  <div class="operation">
                    <span class="operation-desc">{{ item.description }}</span>
                    <button class="operation-button" thyButton="primary" thySize="md" (click)="useMaterial($event, item)" translate="wiki.material.use"></button>
                  </div>
                </div>
                <div class="title">{{ item.name }}</div>
              </li>
            }
          </ul>
        } @else {
          <thy-layout class="d-flex justify-content-center">
            <thy-empty></thy-empty>
          </thy-layout>
        }
      } @else {
        <thy-loading [thyDone]="materialFetcher.loadingDone()"></thy-loading>
      }
    </thy-dialog-body>
  </thy-layout>
</thy-layout>
