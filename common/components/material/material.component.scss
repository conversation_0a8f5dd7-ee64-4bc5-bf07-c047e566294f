@use 'ngx-tethys/styles/variables.scss';

.wiki-material {
    height: 650px;

    &-operations {
        .thy-input-search {
            width: 380px;
        }
    }
    &-list {
        display: flex;
        flex-wrap: wrap;
        margin-top: 16px;
        padding: 0;
        justify-content: flex-start;
        gap: 16px;
    }
    &-item {
        display: flex;
        flex-direction: column;
        margin-bottom: 32px;

        &:nth-child(3n) {
            margin-right: 0;
        }

        .image {
            background-color: variables.$gray-80;
            border: 1px solid variables.$border-color;
            border-radius: variables.$border-radius;
            padding: 16px;
            position: relative;
            cursor: pointer;

            img {
                width: 174px;
                height: 128px;
                border-radius: variables.$border-radius;
            }

            &:hover {
                .operation {
                    display: flex;
                }
            }
        }
        .title {
            margin-top: 12px;
            font-size: variables.$font-size-base;
            color: variables.$text-color;
        }

        .operation {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: none;
            flex-direction: column;
            width: 100%;
            height: 100%;
            padding: 16px;
            background-color: rgba(250, 250, 250, 0.8);

            &-desc {
                flex: 1;
                font-size: variables.$font-size-base;
                color: variables.$text-color;
                display: block;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 4;
                overflow: hidden;
                text-overflow: ellipsis;
                max-height: calc(1.5em * 4);
            }
            &-button {
                position: absolute;
                bottom: 16px;
                width: calc(100% - 32px);
            }
        }
    }
}
