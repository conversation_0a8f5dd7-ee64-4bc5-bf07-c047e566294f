import { ChangeDetectionStrategy, Component, inject, Input, OnInit } from '@angular/core';
import { PublicPathPipe, ResponseData, StyxTranslateService } from '@atinc/ngx-styx';
import { PlaitElement } from '@plait/core';
import { asyncBehavior } from '@tethys/cdk/behaviors';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { PageApiService } from '@wiki/app/services';
import { SpaceStore } from '@wiki/app/stores/space.store';
import { MaterialInfo, MaterialMenu, MaterialScene } from '@wiki/common/interface';

@Component({
    selector: 'wiki-material',
    templateUrl: './material.component.html',
    host: {
        class: 'wiki-material'
    },
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [PublicPathPipe],
    standalone: false
})
export class Material implements OnInit {
    translate = inject(StyxTranslateService);

    @Input() useMaterialAction: (e: Event, elements: PlaitElement[]) => void;

    menus: MaterialMenu[] = [
        {
            key: 'all',
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.material.all')
        }
    ];

    scenes: MaterialScene[] = [];

    activeMenu: MaterialMenu;

    activeScene: MaterialScene;

    materials: MaterialInfo[] = [];

    keywords: string = '';

    private pageApiService = inject(PageApiService);

    private spaceStore = inject(SpaceStore);

    private publicPathPipe = inject(PublicPathPipe);

    get spaceId() {
        return this.spaceStore.snapshot.pilot._id;
    }

    materialFetcher = asyncBehavior(() => {
        return this.pageApiService.fetchMaterial(this.keywords, this.activeScene?.key);
    });

    ngOnInit() {
        this.selectMenu(this.menus[0]);
    }

    fetchMaterial() {
        this.materialFetcher().execute((data: ResponseData<MaterialInfo[]>) => {
            if (this.scenes.length === 0) {
                this.scenes = data.references.categories;
            }
            this.materials = this.systemMaterialTransform(data.value);
        });
    }

    selectMenu(item: MaterialMenu) {
        this.keywords = '';
        this.activeScene = null;
        this.activeMenu = item;
        this.searchKeywords();
    }

    selectScene(item: MaterialScene) {
        this.keywords = '';
        this.activeMenu = null;
        this.activeScene = item;
        this.searchKeywords();
    }

    searchKeywords() {
        this.fetchMaterial();
    }

    clearKeywords() {
        this.searchKeywords();
    }

    systemMaterialTransform(materials: MaterialInfo[]) {
        return materials.map(material => {
            if (material.is_system) {
                material.cover = this.publicPathPipe.transform(`/assets/images/material/${material._id}.png`);
            }
            return material;
        });
    }

    useMaterial(e: Event, material: MaterialInfo) {
        this.useMaterialAction(e, material.content);
    }
}
