import { Component, OnInit, HostBinding, Input } from '@angular/core';
import { AttachmentEntity, AttachmentSizes } from '@atinc/ngx-styx';
import { InputBoolean } from 'ngx-tethys/core';

@Component({
    selector: 'common-empty-content',
    template: `
        @if (isAttachment) {
            <styx-file-thumbnail [styxFile]="attachment" [styxSize]="size" thyStopPropagation class="mr-2 "></styx-file-thumbnail>
        } @else {
            <thy-icon class="common-empty-icon mr-2" [thyIconName]="iconName"></thy-icon>
        }
        <span class="common-empty-text d-inline-flex align-self-center text-desc" [ngClass]="{ 'attachment-text': isAttachment }">{{
            text
        }}</span>
    `,
    host: {
        class: 'common-empty-content d-flex align-items-center p-2',
        '[class.py-3]': 'isAttachment',
        '[class.px-4]': 'isAttachment'
    },
    standalone: false
})
export class CommonEmptyContentComponent implements OnInit {
    @HostBinding('class.common-plugin-card-element')
    get cardStyle(): boolean {
        return this.showCardStyle;
    }

    @Input() iconName: string;

    @Input() isAttachment = false;

    @Input() text: string;

    @Input() attachment: AttachmentEntity;

    @Input() size: AttachmentSizes = AttachmentSizes.md;

    @Input() @InputBoolean() showCardStyle: boolean = true;

    ngOnInit() {}
}
