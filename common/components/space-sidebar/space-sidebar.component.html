<thy-sidebar
  #sidebarTemplate
  [ngClass]="{ 'collapse-visible': collapseVisible, 'collapse-hidden': collapseHidden, 'common-collapsed': isCollapsed }"
  thyResizable
  thyTheme="light"
  thyBounds="window"
  [thyCollapsible]="true"
  [thyCollapsed]="isCollapsed"
  [thyWidth]="sidebarWidth"
  [thyHasBorderRight]="!isCollapsed"
  [thyCollapsedWidth]="collapsedWidth"
  [style.minWidth.px]="sidebarMinWidth"
  [thyMaxWidth]="sidebarMaxWidth"
  (thyResize)="onSideResize($event)"
  (thyResizeStart)="onSideResizeStart()"
  (thyResizeEnd)="onSideResizeEnd($event)"
  (thyCollapsedChange)="collapsedChange($event)"
>
  @if (!isCollapsed) {
    <thy-resize-handle
      thyDirection="right"
      [thyLine]="true"
      (mouseenter)="resizeHandleHover($event, 'enter')"
      (mouseleave)="resizeHandleHover($event, 'leave')"
    >
    </thy-resize-handle>
  }
  <ng-content></ng-content>
</thy-sidebar>
