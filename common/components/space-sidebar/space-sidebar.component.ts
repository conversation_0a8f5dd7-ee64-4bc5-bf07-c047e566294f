import { Component, ElementRef, EventEmitter, HostBinding, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { cache } from '@atinc/ngx-styx';
import { SPACE_SIDEBAR_COLLAPSED, SPACE_SIDEBAR_WIDTH } from '@wiki/common/constants/page';
import { ThyResizeEvent } from 'ngx-tethys/resizable';

@Component({
    selector: 'wiki-common-space-sidebar',
    templateUrl: './space-sidebar.component.html',
    standalone: false
})
export class WikiCommonSpaceSidebarComponent implements OnInit {
    @HostBinding('class') hostClass = 'space-sidebar-container';

    @Input() mode: 'inside' | 'outside' = 'inside';

    @Input() isCollapsed: boolean;

    @Output() isCollapsedChange = new EventEmitter();

    private _sidebarWidth: number;

    set sidebarWidth(value: number) {
        this._sidebarWidth = value;
    }

    get sidebarWidth() {
        return this._sidebarWidth || Number(cache.get(this.cacheSidebarWidthKey)) || 300;
    }

    sidebarMaxWidth: number;

    sidebarMinWidth: number;

    collapsedWidth = 28;

    isHideSearchIcon: boolean;

    private originWidth: number;

    collapseVisible: boolean;

    collapseHidden: boolean;

    cacheCollaspeKey: string;

    cacheSidebarWidthKey: string;

    pilotTitleBoundary = 240;

    pilotTitleHalfBoundary = this.pilotTitleBoundary / 2;

    pilotIconBoundary = 280;

    searchIconBoundary = 280;

    constructor(private elementRef: ElementRef) {}

    ngOnInit(): void {
        this.cacheSidebarWidthKey = this.mode === 'inside' ? `wiki-${SPACE_SIDEBAR_WIDTH}` : `outside-${SPACE_SIDEBAR_WIDTH}`;
        this.cacheCollaspeKey = this.mode === 'inside' ? `wiki-${SPACE_SIDEBAR_COLLAPSED}` : `outside-${SPACE_SIDEBAR_COLLAPSED}`;
        this.isHideSearchIcon = this.sidebarWidth < this.searchIconBoundary;
        this.sidebarMaxWidth = this.elementRef.nativeElement.parentElement.clientWidth / 2;
        if (this.isCollapsed) {
            this.foldSidebar();
        }
        this.initSidebarMinWidth();
    }

    initSidebarMinWidth() {
        !this.isCollapsed && (this.sidebarMinWidth = this.pilotTitleBoundary / 2);
    }

    clearSidebarMinWidth() {
        this.sidebarMinWidth && (this.sidebarMinWidth = 0);
    }

    updateCollapsedStatus(isCollapsed: boolean) {
        if (isCollapsed !== this.isCollapsed) {
            this.isCollapsed = isCollapsed;
            this.isCollapsedChange.emit(this.isCollapsed);
        }
    }

    collapsedChange(isCollapsible: boolean) {
        this.updateCollapsedStatus(isCollapsible);
        if (this.isCollapsed) {
            this.foldSidebar();
            cache.set(this.cacheCollaspeKey, 'true');
        } else {
            this.unfoldSidebar();
            cache.remove(this.cacheCollaspeKey);
        }
    }

    foldSidebar() {
        this.clearSidebarMinWidth();
        this.updateCollapsedStatus(true);
    }

    unfoldSidebar() {
        this.initSidebarMinWidth();
        this.updateCollapsedStatus(false);
    }

    calcLtIconHandler(width: number) {
        // 如果位于标题宽度一半以内, 则折叠侧边栏
        if (width > this.collapsedWidth && width <= this.pilotTitleHalfBoundary) {
            this.clearSidebarMinWidth();
            this.sidebarWidth = this.collapsedWidth;
            this.updateCollapsedStatus(true);
        }
        // 如果位于标题宽度一半与标题之间，则展开侧边栏
        else {
            this.initSidebarMinWidth();
            if (width > this.pilotTitleHalfBoundary && width < this.pilotTitleBoundary) {
                this.sidebarWidth = this.pilotTitleBoundary;
                this.updateCollapsedStatus(false);
            }
            // 如果位于标题与icon之间，隐藏icon
            else if (width > this.pilotTitleBoundary && width < this.pilotIconBoundary) {
                this.sidebarWidth = width;
            }
        }
        // 折叠逻辑
        this.updateCollapsedStatus(!(this.sidebarWidth > this.collapsedWidth));
        if (this.isCollapsed) {
            this.sidebarWidth = this.originWidth;
            !cache.get(this.cacheCollaspeKey) && cache.set(this.cacheCollaspeKey, 'true');
        } else {
            cache.get(this.cacheCollaspeKey) && cache.remove(this.cacheCollaspeKey);
        }
    }

    onSideResizeStart() {
        this.originWidth = this.sidebarWidth;
        this.collapseHidden = true;
    }

    /**
     * 1. 往左移动：小于icon边界 大于标题边界 隐藏icon；往右移动则恢复 icon
     * 2. 小于标题边界一半，收起/折叠
     * 3. 展开，还原到上次移动的宽度（如果宽度在第一条规则内，按照第一条，否则还原到大于300的宽度）
     */
    onSideResize({ width }: ThyResizeEvent): void {
        if (this.isCollapsed) {
            return;
        }
        // 移动至icon以内，隐藏icon
        if (width < this.pilotIconBoundary) {
            this.calcLtIconHandler(width);
        }
        // 移动至icon以外，展示icon
        else {
            this.sidebarWidth = width;
        }
        this.isHideSearchIcon = width < this.searchIconBoundary ? true : false;
    }

    onSideResizeEnd({ width }: ThyResizeEvent) {
        this.collapseHidden = false;
        cache.set(this.cacheSidebarWidthKey, this.sidebarWidth.toString());
    }

    resizeHandleHover(event: MouseEvent, type: 'enter' | 'leave') {
        this.collapseVisible = !!(type === 'enter');
    }
}
