@use 'ngx-tethys/styles/variables.scss';

.wiki:not(.wiki-fullscreen-active) {
    .space-sidebar-container {
        z-index: 1;
    }
}

.space-sidebar-container {
    display: flex;
    .collapse-visible {
        .sidebar-collapse {
            visibility: visible;
        }
    }

    .collapse-hidden {
        .sidebar-collapse {
            visibility: hidden;
        }
    }

    .common-collapsed {
        background-color: variables.$bg-default;
    }

    .thy-layout-sidebar {
        transition: none;
        &:hover {
            transition: all 0.2s;
        }
    }

    .thy-layout-sidebar.thy-resizable-resizing {
        transition: none;

        .thy-tree-node-wrapper {
            pointer-events: none;
            cursor: ew-resize;

            &:hover {
                background: none;
            }
        }

        .thy-tree-node-actions {
            visibility: hidden;
        }
    }
}
