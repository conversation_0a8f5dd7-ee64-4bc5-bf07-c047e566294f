<thy-slide-layout>
  <thy-slide-header styxI18nTracking [thyTitle]="'common.setUp' | translate"></thy-slide-header>
  @if (loadingDone) {
    <thy-slide-body>
      <div class="slide-body-title text-secondary pt-4 pb-2" translate="common.property"></div>
      <styx-columns-list-selection
        [styxProperties]="properties"
        [styxUnremovableColumns]="unremovableProperties"
        [(ngModel)]="selectedProperties"
      ></styx-columns-list-selection>
    </thy-slide-body>
    <thy-slide-footer class="pt-0 d-flex justify-content-center align-items-center border-top-0">
      <button class="update-button" thyButton="primary" thyBlock (click)="update()" translate="styx.apply"></button>
    </thy-slide-footer>
  } @else {
    <thy-loading [thyDone]="loadingDone"></thy-loading>
  }
</thy-slide-layout>
