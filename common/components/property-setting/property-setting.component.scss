@use 'ngx-tethys/styles/variables.scss';
@use '../../styles/variables.scss' as commonVariables;

$slide-top: 52px;
$slide-body-header: 46px;
$slide-body-search: 55px;
$slide-footer: 60px;
$slide-body-padding: 12px;
$styx-columns-list-selection-border: 2px;

.common-property-setting {
    .thy-slide-header {
        height: commonVariables.$header-height;

        .close {
            color: variables.$gray-600;
        }
    }
    .styx-list-selection-content {
        overflow: auto;
        max-height: calc(
            100vh - $slide-top - commonVariables.$header-height - $slide-body-header - $slide-body-search - $slide-body-padding -
                $slide-footer - $styx-columns-list-selection-border
        );
    }
    .thy-slide-footer {
        height: $slide-footer;
    }
}
.property-setting-slide-container {
    top: commonVariables.$header-height;

    .thy-slide-container {
        max-height: calc(100vh - $slide-top) !important;
    }
}
