import { HttpClient } from '@angular/common/http';
import { Component, inject, Input, OnInit } from '@angular/core';
import { PropertyColumn } from '@atinc/ngx-styx';
import { PropertyInfo, UtilService } from '@atinc/ngx-styx/core';
import { StyxTranslateService } from '@atinc/ngx-styx/i18n/core';
import { RelationListOption } from '@wiki/common/types';
import { MixinBase, mixinUnsubscribe } from 'ngx-tethys/core';
import { takeUntil } from 'rxjs/operators';

@Component({
    selector: 'common-property-setting',
    host: {
        class: 'common-property-setting'
    },
    templateUrl: './property-setting.component.html',
    standalone: false
})
export class CommonPropertySettingComponent extends mixinUnsubscribe(MixinBase) implements OnInit {
    translate = inject(StyxTranslateService);

    loadingDone = false;

    properties: PropertyInfo[] = [];

    selectedProperties: string[];

    @Input() option: RelationListOption;

    @Input() columns: PropertyColumn[];

    @Input() unremovableProperties: string[];

    @Input() updateColumns: (keys: PropertyColumn[]) => void;

    constructor(
        private http: HttpClient,
        private util: UtilService
    ) {
        super();
    }

    ngOnInit() {
        this.fetchAllProperties();
        this.selectedProperties = this.columns.map(column => column.property_key);
    }

    fetchAllProperties() {
        this.loadingDone = false;
        const { apiRoute, apiPrefix } = this.option;
        const apiUrl = `${apiPrefix}/${apiRoute}/properties`;
        return this.http
            .get(apiUrl)
            .pipe(takeUntil(this.ngUnsubscribe$))
            .subscribe((x: { value: PropertyInfo[] }) => {
                this.properties = x.value;
                this.loadingDone = true;
            });
    }

    update() {
        const columns = this.selectedProperties.map(key => {
            return { property_key: key };
        }) as PropertyColumn[];
        this.updateColumns(columns);
        this.util.notify.success(this.translate.instant('wiki.common.property_setting.update_success'));
    }
}
