@use 'ngx-tethys/styles/variables.scss';
@use '@worktile/theia/styles/variables.scss' as theia;
@use '@worktile/theia/styles/mixins.scss' as mixins;

.wiki-common-expanding-toolbar {
    .expanding-toolbar {
        @include mixins.the-action-disabled();

        &.expanding-toolbar-read {
            width: auto;
            display: inline-flex;
            position: fixed;
            align-items: center;
            margin-top: -6px;
            transition: opacity 0.75s;
            opacity: 1;
            z-index: 1000;

            &.hidden {
                display: none;
            }

            .thy-icon-nav-link {
                margin-right: inherit;
            }
        }
    }

    .thy-icon-nav-link {
        width: auto;

        .thy-icon {
            margin-right: 6px;
        }
    }
}
