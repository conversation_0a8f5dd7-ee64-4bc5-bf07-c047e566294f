import {
    Component,
    OnInit,
    OnDestroy,
    Input,
    ElementRef,
    NgZone,
    ChangeDetectionStrategy,
    TemplateRef,
    ViewChild,
    HostBinding
} from '@angular/core';
import { ScrollDispatcher } from '@angular/cdk/overlay';
import { merge, fromEvent, Subject } from 'rxjs';
import { takeUntil, filter, debounceTime } from 'rxjs/operators';
import { AngularEditor } from 'slate-angular';
import { Element, Node } from 'slate';
import { CustomElement, TheEditor, TheQueries } from '@worktile/theia';
import { getDecorationSelection } from '@wiki/common/util/common';

@Component({
    selector: 'wiki-common-expanding-toolbar',
    templateUrl: 'expanding-toolbar.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiCommonExpandingToolbarComponent implements OnInit, OnDestroy {
    @HostBinding('class') className = 'wiki-common-expanding-toolbar';

    @Input() editor: TheEditor;

    @Input() afterTemplate: TemplateRef<any>;

    @ViewChild('inlineToolbar', { static: true, read: ElementRef })
    inlineToolbar: ElementRef<any>;

    destroy$ = new Subject<void>();

    eventTarget: HTMLElement;

    constructor(
        private scrollDispatcher: ScrollDispatcher,
        private ngZone: NgZone
    ) {}

    ngOnInit() {
        this.scrollDispatcher
            .scrolled(50)
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
                this.updateInlineToolbar();
            });
        this.ngZone.runOutsideAngular(() => {
            merge(
                fromEvent(document, 'mouseup').pipe(
                    filter((e: MouseEvent) => {
                        const isEditorOperation = (e.target as HTMLElement)?.closest('.the-editor-operation');
                        return e.button !== 2 && !isEditorOperation;
                    })
                ),
                fromEvent(document, 'keyup').pipe(filter((e: KeyboardEvent) => !e.shiftKey))
            )
                .pipe(takeUntil(this.destroy$), debounceTime(200))
                .subscribe((event: Event) => {
                    this.eventTarget = event.target as HTMLElement;
                    this.updateInlineToolbar();
                });
        });
    }

    updateInlineToolbar() {
        const native = window.getSelection();

        if (native.isCollapsed || native.type !== 'Range') {
            this.removeToolbar();
            return;
        }

        if (!native.anchorNode?.parentElement || !native.focusNode?.parentElement) {
            return;
        }

        const hasEditorAnchor = AngularEditor.hasDOMNode(this.editor, native.anchorNode);
        const hasEditorFocus = AngularEditor.hasDOMNode(this.editor, native.focusNode);

        if (hasEditorAnchor && hasEditorFocus) {
            const decoration = getDecorationSelection(this.editor, native);
            if (!decoration?.discussion?.quote.trim()) {
                return;
            }

            const isEditorContent = this.eventTarget && AngularEditor.hasDOMNode(this.editor, this.eventTarget);
            if (!isEditorContent) {
                this.removeToolbar();
                return;
            }
            const domRange = native.getRangeAt(0);

            const slateRange = AngularEditor.toSlateRange(this.editor, domRange, { exactMatch: false, suppressThrow: false });
            const fragment = Node.fragment(this.editor, slateRange);

            if (TheQueries.isAcrossBlocks(this.editor, fragment as CustomElement[]) || TheQueries.isCollapsed(slateRange)) {
                this.removeToolbar();
                return;
            }

            this.updatePosition(domRange);
        }
    }

    updatePosition(range) {
        const toolbarElement: HTMLElement = this.inlineToolbar.nativeElement;
        toolbarElement.classList.remove('hidden');
        const boundary = range.getBoundingClientRect();
        const toolbarHeight = toolbarElement.offsetHeight;
        const toolbarWidth = toolbarElement.offsetWidth;
        const halfOffsetWidth = toolbarWidth / 2;
        const defaultLeft = -halfOffsetWidth;
        const positions: any = {};
        positions.top = boundary.top - toolbarHeight - 3;
        positions.left = boundary.left + boundary.width / 2 + defaultLeft;
        positions.right = 'initial';
        ['top', 'left', 'right'].forEach(key => {
            toolbarElement.style[key] = positions[key] + (isNaN(positions[key]) ? '' : 'px');
        });
    }

    removeToolbar() {
        const toolbarElement: HTMLElement = this.inlineToolbar.nativeElement;
        toolbarElement.classList.add('hidden');
        this.eventTarget = null;
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }
}
