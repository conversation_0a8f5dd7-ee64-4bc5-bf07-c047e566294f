import { Overlay } from '@angular/cdk/overlay';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    inject,
    Input,
    OnDestroy,
    OnInit,
    Optional,
    TemplateRef,
    ViewChild,
    ViewContainerRef
} from '@angular/core';
import {
    AppRootContext,
    GlobalPublicPathPipe,
    GlobalUsersStore,
    Is,
    StyxErrorService,
    StyxTranslateService,
    UserCustomizationContext,
    helpers,
    injectBizValidatorMaxLengths
} from '@atinc/ngx-styx';
import { StyxEmojiComponent, StyxEmojiPickerComponent } from '@atinc/ngx-styx/emoji';
import { TheiaUtils } from '@atinc/selene';
import { AUTO_SAVE_THROTTLE_IN_MS, EditingPageFrom } from '@wiki/app/constants/page';
import { PageViewPort } from '@wiki/app/entities/page-info';
import { EDITOR_INLINE_TOOLBAR_VISIBLE } from '@wiki/common/constants/editor';
import { getDefaultDocumentTitle, WIKI_EDIT_SCROLL_CONTAINER } from '@wiki/common/constants/page';
import { TheDocument } from '@wiki/common/custom-types';
import { WikiEditDocumentRelationData } from '@wiki/common/interface/page';
import { WikiCommonPlugins } from '@wiki/common/plugins';
import { SearchReplaceEditor } from '@wiki/common/plugins/search-replace/search-replace.editor';
import { toggleListDecorate } from '@wiki/common/plugins/toggle-list/utils/decorate';
import { CommonPageApiService } from '@wiki/common/services/page-api-service';
import { PageEventBus } from '@wiki/common/services/page-event-bus';
import { CommonEditingPageStore } from '@wiki/common/stores/editing-page.store';
import { PageStore } from '@wiki/common/stores/page.store';
import { WikiPluginTypes } from '@wiki/common/types';
import { WikiPluginMenu } from '@wiki/common/types/plugin-menu';
import { GlobalExtraElementOptions, GlobalToolbarDefinition, InlineToolbarDefinition } from '@wiki/common/types/toolbar';
import { ClientEventEmitter, initializeWikiMobileEditor } from '@wiki/common/util/bridge-for-mobile';
import { isEmptyDocument, isMaxView, placeholderDecorate, verifyAvailableAppByPluginType } from '@wiki/common/util/common';
import {
    DomEventDataInfo,
    ErrorCodes,
    FontSizes,
    TheDataMode,
    TheEditor,
    TheEditorComponent,
    TheOptions,
    normalizeValue,
    refocus
} from '@worktile/theia';
import { CursorEditor, YjsEditor, useCursors } from '@worktile/y-slate';
import { ThyAction } from 'ngx-tethys/action';
import { ThyNotifyService } from 'ngx-tethys/notify';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { Subject, interval } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, takeUntil } from 'rxjs/operators';
import { Editor, Element, NodeEntry, Range, Transforms } from 'slate';
import { AngularEditor, SlateError } from 'slate-angular';
import { WikiEditDocumentBaseComponent } from './document.base';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { PlaitElement } from '@plait/core';
import { HeadingHandleScene } from '@wiki/common/plugins/heading-handle/constants';

@Component({
    selector: 'wiki-edit-document',
    templateUrl: './document.component.html',
    providers: [GlobalPublicPathPipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'wiki-edit-document-container h-100 thy-layout',
        '[class.wiki-edit-document-max-view]': 'isMaxView',
        '[class.wiki-edit-document-max-view-with-side]': 'isMaxView && isCollapsedToc && isCollapseStencil'
    },
    standalone: false
})
export class WikiEditDocumentComponent extends WikiEditDocumentBaseComponent implements OnInit, AfterViewInit, OnDestroy {
    translate = inject(StyxTranslateService);

    headingHandleScene = HeadingHandleScene.editing;

    @Input() set isFetchDone(value) {
        this._isFetchDone = value;
        if (value) {
            this.initialize();
        }
    }

    public get isFetchDone(): boolean {
        return this._isFetchDone;
    }

    @Input()
    set isStencil(value) {
        this._isStencil = value;
        if (value) {
            this.updateEditorLoadingState(true);
        }
    }

    @Input() public set readonly(value: boolean) {
        this._readonly = value;
        this.theOptions.readonly = value;
    }

    public get readonly(): boolean {
        return this._readonly;
    }

    @Input() initCollaboration: (board: TheEditor) => void;

    @Input() isMobileMode: boolean;

    @ViewChild('theEditor', { static: false })
    editorComponent: TheEditorComponent;

    thyPopoverRef: ThyPopoverRef<StyxEmojiPickerComponent>;

    private initData: {
        name: string;
        emojiIcon: string;
        value: TheDocument;
    };

    isCollapsedToc = false;

    isCollapseStencil = true;

    isEditorLoadingDone = false;

    isDragging = false;

    activeMenu = 'toc';

    get isMaxView() {
        return isMaxView(this.page, this.userCustomizationContext);
    }

    isNeededScrollIntoView = false;

    theOptions: TheOptions = {
        mode: TheDataMode.json,
        placeholder: this.translate.instant<I18nSourceDefinitionType>('wiki.plugin.placeholder'),
        inlineToolbarVisible: EDITOR_INLINE_TOOLBAR_VISIBLE,
        readonly: this.readonly,
        fontSize: FontSizes.fontSize15,
        richMedia: true,
        scrollContainer: WIKI_EDIT_SCROLL_CONTAINER,
        toolbar: {
            global: GlobalToolbarDefinition,
            inline: InlineToolbarDefinition
        },
        menu: WikiPluginMenu(this.translateService),
        extraElementOptions: GlobalExtraElementOptions,
        neededScrollIntoView: (e: TheEditor) => {
            return this.isNeededScrollIntoView;
        },
        placeholderDecorate: (e: TheEditor) => {
            const decorations = [];
            decorations.push(...toggleListDecorate(e));
            decorations.push(
                ...placeholderDecorate(
                    e,
                    this.theOptions?.placeholder,
                    this.translate.instant<I18nSourceDefinitionType>('wiki.document.placeholder')
                )
            );
            return decorations;
        }
    };

    customPlugins = WikiCommonPlugins(this.translateService);

    isLocalChange = false;

    // for report error
    nearRange: Range;

    pageWordsCount = 0;

    isStencilContent: boolean;

    private interval = 3000;

    private valueChange$ = new Subject<void>();

    public isShowHint: boolean = false;

    titleMaxLength = injectBizValidatorMaxLengths().longTitle;

    get editor(): TheEditor | YjsEditor | CursorEditor {
        return this.editorComponent && this.editorComponent.editor;
    }

    get isSameTitle() {
        return this.initData.name === this.page.name && this.initData.emojiIcon == this.page.emoji_icon;
    }

    get isSameValue() {
        return helpers.isEqual(this.page.document, this.initData.value);
    }

    get isOpened() {
        return this.thyPopoverRef && this.thyPopoverRef.getOverlayRef() && this.thyPopoverRef.getOverlayRef().hasAttached();
    }

    decorate = (e: TheEditor, nodeEntry: NodeEntry): Range[] => [];

    cursorsDecorate = (nodeEntry: NodeEntry): Range[] => [];

    constructor(
        public userStore: GlobalUsersStore,
        private cdr: ChangeDetectorRef,
        public notifyService: ThyNotifyService,
        private errorService: StyxErrorService,
        private globalPageStore: PageEventBus,
        private editingPageStore: CommonEditingPageStore,
        private pageContentEditStore: PageStore,
        public appRootContext: AppRootContext,
        private pageApiService: CommonPageApiService,
        private thyPopover: ThyPopover,
        public viewContainerRef: ViewContainerRef,
        private overlay: Overlay,
        @Optional() private userCustomizationContext: UserCustomizationContext
    ) {
        super();
    }

    ngOnInit() {
        if (this.isMobileMode) {
            this.theOptions.placeholder = '';
        }
        this.initializeDecorate();
        this.isStencilContent = this.editingPageStore.snapshot.from === EditingPageFrom.stencil;
        this.valueChange$
            .asObservable()
            .pipe(debounceTime(this.interval))
            .subscribe(() => {
                this.wordsCount();
            });

        this.editingPageStore.getDecorateState().subscribe(() => {
            this.initializeDecorate();
        });

        const isShowProjectManage = verifyAvailableAppByPluginType(
            this.translate,
            null,
            WikiPluginTypes.relationWorkItem,
            this.appRootContext
        );
        if (!isShowProjectManage) {
            this.theOptions.menu = this.theOptions.menu.filter(x => x !== WikiPluginTypes.relationWorkItem);
        }
    }

    ngAfterViewInit() {
        if (this.isFetchDone && this.isPublished) {
            this.initShared();
        }
        if (this.isFetchDone) {
            this.syncEditor.emit(this.editor);
        }
    }

    editorCreate() {
        if (this.isMobileMode) {
            initializeWikiMobileEditor(this.editor);
            const { onChange } = this.editor;
            this.editor.onChange = () => {
                onChange();
                ClientEventEmitter.onChange(this.editor);
            };
        }
    }

    updateEditorLoadingState(done: boolean) {
        this.isEditorLoadingDone = done;
        if (this.isEditorLoadingDone) {
            // 聚焦逻辑：无标题文档页面聚焦标题，否则聚焦文档（包含模板聚焦|草稿聚焦 场景）
            setTimeout(() => {
                if (this.page.name) {
                    refocus(this.editor);
                }
            }, 10);
        }
    }

    initializeDecorate() {
        this.decorate = (e: TheEditor, nodeEntry: NodeEntry) => {
            const decorations = [];
            decorations.push(...SearchReplaceEditor.searchDecorate(e, nodeEntry));

            if (this.cursorsDecorate) {
                decorations.push(...this.cursorsDecorate(nodeEntry));
            }
            return decorations;
        };
        this.cdr.markForCheck();
    }

    initialize() {
        this.initData = {
            name: this.page.name || getDefaultDocumentTitle(this.translateService),
            emojiIcon: this.page.emoji_icon,
            value: this.page.document as TheDocument
        };
        if (!this.isPublished) {
            this.updateEditorLoadingState(true);
        }
        if (isEmptyDocument(this.page.document) && !this.page?.is_published) {
            this.toggleStencilExpand();
        }
        this.wordsCount();
        this.initComplete();
    }

    initShared() {
        this.initCollaboration(this.editor);
        const decorate$ = useCursors(this.editor as CursorEditor);
        decorate$
            .pipe(
                takeUntil(this.ngUnsubscribe$),
                distinctUntilChanged((previous, current) => {
                    return JSON.stringify(previous.cursors) === JSON.stringify(current.cursors);
                })
            )
            .subscribe(data => {
                this.cursorsDecorate = data.decorate;
                this.initializeDecorate();
            });
    }

    theOnErrorHandler = (error: SlateError) => {
        if (error.code === ErrorCodes.IMAGE_ERR_SIZE_LIMIT) {
            this.notifyService.warning(
                this.translate.instant('common.tip'),
                this.translate.instant<I18nSourceDefinitionType>('wiki.document.imageError.sizeLimit')
            );
        }
        if (error.code === ErrorCodes.IMAGE_ERR_MIME_FORMAT) {
            this.notifyService.warning(
                this.translate.instant('common.tip'),
                this.translate.instant<I18nSourceDefinitionType>('wiki.document.imageError.formatError')
            );
        }
        this.reportError(error);
    };

    reportError(error: SlateError) {
        if (error.nativeError) {
            const { extractedError } = this.errorService.captureError(error.nativeError, {
                module: 'editor',
                tags: {
                    origin: 'onError',
                    code: String(error.code)
                },
                context: {
                    pageId: this.page._id,
                    versionId: this.page.version_id,
                    nearElementEntry: JSON.stringify(this.getNearElementEntry(this.nearRange))
                }
            });
            console.error(extractedError);
        }
    }

    initComplete() {
        this.cdr.markForCheck();
        // use auto save for unpublished pages
        if (this.isAutoSave) {
            interval(AUTO_SAVE_THROTTLE_IN_MS)
                .pipe(
                    filter(() => this.isAutoSave && !this.readonly),
                    takeUntil(this.ngUnsubscribe$)
                )
                .subscribe(() => {
                    this.onSave();
                });
        }
    }

    wordsCount() {
        this.pageWordsCount = TheiaUtils.getWordCount((this.page?.document ?? []) as TheDocument);
    }

    pageTitleFocus() {
        if (this.page.name === getDefaultDocumentTitle(this.translateService)) {
            this.page.name = '';
        }

        if (this.isMobileMode) {
            ClientEventEmitter.onTitleFocus();
        }
    }

    pageTitleBlur() {
        if (this.page.name === '') {
            this.page.name = getDefaultDocumentTitle(this.translateService);
        }
        this.dispatchChange();
    }

    pageTitleEnter(event: KeyboardEvent & { isComposing: boolean }) {
        if (event.isComposing) {
            return;
        }
        event.preventDefault();
        const startPoint = Editor.start(this.editor, [0]);
        const firstNode = Editor.above(this.editor, {
            at: startPoint,
            match: n => Element.isElement(n) && !Editor.isInline(this.editor, n)
        });
        const lastPoint = Editor.end(this.editor, firstNode[1]);

        Transforms.select(this.editor, lastPoint);
        TheEditor.focus(this.editor as TheEditor);
    }

    isCollapsed() {
        if (this.editor?.selection && Range.isCollapsed(this.editor.selection)) {
            return true;
        }
        return false;
    }

    neededScrollIntoView(e: TheEditor) {
        if (
            !YjsEditor.isRemote(e as YjsEditor) &&
            this.isCollapsed() &&
            this.editor.operations.some(op => op.type === 'insert_node' || op.type === 'insert_text' || op.type === 'split_node')
        ) {
            return true;
        }
        return false;
    }

    getEditableElementHeight() {
        const editable = AngularEditor.toDOMNode(this.editor as TheEditor, this.editor);
        return editable.clientHeight;
    }

    onChange(value) {
        if (!this.isEditorLoadingDone) {
            this.updateEditorLoadingState(!!value.length);
        }
        if (!this.isEditorLoadingDone) {
            return;
        }
        if (!helpers.isEqual(value, this.page.document)) {
            this.page.document = value;
            if (!this.isPublished) {
                if (isEmptyDocument(value) || this.isStencilContent) {
                    if (this.isCollapseStencil) {
                        this.toggleStencilExpand();
                    }
                }
                if (!isEmptyDocument(value) && !this.isCollapseStencil) {
                    this.toggleTocExpand();
                    this.isStencilContent = false;
                }
            }
            this.dispatchChange();
            if (!YjsEditor.isRemote(this.editor as YjsEditor)) {
                this.isLocalChange = true;
            }
        }
        // 协同数据初始化完毕，尝试修复错误数据
        if (this.editor.operations.length === 0 && value.length > 0) {
            normalizeValue(this.editor, this.editor.children);
        }
        this.valueChange$.next();
        this.errorService.addScopeBreadcrumb({
            type: 'change',
            level: 'debug',
            category: '',
            message: JSON.stringify(this.editor.operations)
        });
        if (this.editor.selection) {
            this.nearRange = this.editor.selection;
        }
        this.isNeededScrollIntoView = this.neededScrollIntoView(this.editor);
    }

    onDragChange = isDragging => {
        this.isDragging = isDragging;
    };

    theOnDOMEvent(event: { nativeEvent: Event; data: DomEventDataInfo }) {
        if (this.isDragging) {
            event.nativeEvent.preventDefault();
        }
        this.errorService.addScopeBreadcrumb({
            type: 'interaction',
            level: 'debug',
            category: '',
            message: JSON.stringify({
                type: event.nativeEvent.type,
                data: event.data
            })
        });
    }

    getNearElementEntry(range: Range) {
        try {
            const nearLength = 2;
            if (range && range.anchor) {
                const document = this.page.document as TheDocument | PlaitElement[];
                const [start, end] = Range.edges(range);
                const startIndex = start.path[0] - nearLength >= 0 ? start.path[0] - nearLength : 0;
                const endIndex = end.path[0] + nearLength <= document.length - 1 ? end.path[0] + nearLength : document.length - 1;
                return [document.slice(startIndex, endIndex + 1), [startIndex, endIndex]];
            }
        } catch (error) {
            console.error(error);
        }
        return [];
    }

    onTitleChange() {
        this.dispatchChange();
    }

    dispatchChange() {
        this.dataChange.emit({
            data: {
                value: this.page.document,
                isSameValue: this.isSameValue,
                name: this.page.name,
                isSameTitle: this.isSameTitle,
                emojiIcon: this.page.emoji_icon
            }
        });
    }

    onSave() {
        if (this.isSameTitle && this.isSameValue) {
            return;
        }
        if (!this.saveHandle) {
            return;
        }
        // 只有本地修改才会触发保存，远程修改不触发内容保存
        if (!this.isLocalChange) {
            return;
        }
        this.isLocalChange = false;
        this.saveHandle(this.page.document as TheDocument).subscribe(res => {
            if (res) {
                // sync the attachments in the stencil to the store
                if (res.attachments?.length && res.document) {
                    this.page.document = res.document as TheDocument;
                    res.attachments.map(attachment => {
                        this.pageContentEditStore.pureAddAttachment(attachment);
                    });
                }
                this.dispatchChange();
            }
        });
    }

    tocCollapsedChange(isCollapsed) {
        this.isCollapsedToc = isCollapsed;
    }

    toggleStencilChange = isCollapseStencil => {
        this.isCollapseStencil = isCollapseStencil;
    };

    toggleStencilExpand() {
        this.isCollapseStencil = !this.isCollapseStencil;
        this.isCollapsedToc = true;
        this.activeMenu = 'stencil';
    }

    toggleTocExpand(event?: MouseEvent) {
        this.isCollapsedToc = !this.isCollapsedToc;
        this.isCollapseStencil = true;
        this.activeMenu = 'toc';
        event?.preventDefault();
    }

    ngOnDestroy() {
        super.ngOnDestroy();
        this.valueChange$.complete();
    }

    useStencilContent = (stencilData: { document: TheDocument; stencilId: string } & WikiEditDocumentRelationData) => {
        const {
            document,
            stencilId,
            relation_pages,
            relation_work_items,
            relation_test_cases,
            relation_ideas,
            relation_tickets,
            relation_objectives,
            attachments
        } = stencilData;
        this.page.document = document;
        this.isStencilContent = true;

        this.dataChange.emit({
            data: {
                value: this.page.document,
                isSameValue: this.isSameValue,
                name: this.page.name,
                emojiIcon: this.page.emoji_icon,
                isSameTitle: this.isSameTitle
            },
            stencilId: stencilId,
            relationData: {
                relation_pages,
                relation_work_items,
                relation_test_cases,
                relation_ideas,
                relation_tickets,
                relation_objectives,
                attachments
            }
        });
    };

    onUploadingStatus(isUploading: boolean) {
        this.uploadingStatusChange.emit(isUploading);
    }

    updateViewport() {
        if (this.isStencil) {
            this.updateStencilViewport();
        } else {
            this.updatePageViewport();
        }
    }

    updatePageViewport() {
        const viewport: PageViewPort = this.isMaxView ? 'default' : 'full-width';
        this.page.viewport = viewport;
        this.pageApiService.updatePageViewPort(this.page._id, viewport).subscribe(() => {
            TheEditor.deselect(this.editor);
            this.globalPageStore.emitPageInfo({ ...this.page, viewport });
        });
    }

    updateStencilViewport() {
        const viewport: PageViewPort = this.isMaxView ? 'default' : 'full-width';
        this.page.viewport = viewport;
        this.pageApiService.updateStencilViewPort(this.page._id, viewport).subscribe(() => {
            this.globalPageStore.emitPageInfo({ ...this.page, viewport, is_stencil: Is.yes });
        });
    }

    openEmoji(event: Event, templateRef: TemplateRef<ThyAction | StyxEmojiComponent>) {
        this.thyPopoverRef = this.thyPopover.open(StyxEmojiPickerComponent, {
            initialState: {
                styxBindKeyEventContainer: templateRef?.elementRef
            },
            origin: event.currentTarget as HTMLElement,
            placement: 'bottomLeft',
            offset: 15,
            outsideClosable: true,
            hasBackdrop: false,
            viewContainerRef: this.viewContainerRef,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });
        if (this.thyPopoverRef?.componentInstance) {
            this.thyPopoverRef.componentInstance.styxSelect.subscribe((event: string) => {
                this.page.emoji_icon = event;
                this.closePopover();
            });
            this.thyPopoverRef.componentInstance.styxRandom.subscribe((event: string) => {
                this.page.emoji_icon = event;
                this.closePopover();
            });
        }
        this.thyPopoverRef.afterClosed().subscribe(() => {
            this.dispatchChange();
        });
    }

    closePopover() {
        if (this.thyPopoverRef) {
            this.thyPopoverRef.close();
        }
    }

    emojiSelectShow() {
        this.isShowHint = true;
    }

    emojiSelectHide() {
        this.isShowHint = false;
    }

    emojiStateChange(event: Event, isShow: boolean, templateRef: TemplateRef<ThyAction | StyxEmojiComponent>): void {
        event.stopPropagation();
        if (this.isOpened) {
            this.closePopover();
            return;
        }
        if (!isShow) {
            this.openEmoji(event, templateRef);
        } else {
            this.page.emoji_icon = '';
            this.dispatchChange();
        }
    }
}
