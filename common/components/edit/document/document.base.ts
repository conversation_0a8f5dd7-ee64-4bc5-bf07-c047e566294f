import { OnDestroy, Input, TemplateRef, Output, EventEmitter, Directive, inject } from '@angular/core';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { DraftInfo, PageInfo } from '@wiki/app/entities/page-info';
import { CollaborationInfo } from '@wiki/app/entities/sharing.info';
import { TheDocument } from '@wiki/common/custom-types';
import { WikiEditPageInfo, WikiPageContent } from '@wiki/common/interface/page';
import { TheEditor } from '@worktile/theia';
import { MixinBase, mixinUnsubscribe } from 'ngx-tethys/core';
import { Observable } from 'rxjs';

@Directive()
export class WikiEditDocumentBaseComponent extends mixinUnsubscribe(MixinBase) implements OnDestroy {
    @Input() public set readonly(value: boolean) {
        this._readonly = value;
    }

    public get readonly(): boolean {
        return this._readonly;
    }

    @Input() isAutoSave = true;

    @Input() public set page(value: DraftInfo) {
        this._page = value;
    }

    public get page(): DraftInfo {
        return this._page;
    }

    @Input() collaborationInfo: CollaborationInfo;

    @Input() editId: string;

    @Input() isPublished: boolean;

    @Input() saveHandle: (value: TheDocument) => Observable<DraftInfo>;

    @Input() public set isFetchDone(value: boolean) {
        this._isFetchDone = value;
    }

    public get isFetchDone(): boolean {
        return this._isFetchDone;
    }

    @Input() public set isStencil(value: boolean) {
        this._isStencil = value;
    }

    public get isStencil(): boolean {
        return this._isStencil;
    }

    @Input() stencilSideTemplate: TemplateRef<any>;

    @Output() dataChange: EventEmitter<WikiEditPageInfo> = new EventEmitter();

    @Output() syncEditor: EventEmitter<TheEditor> = new EventEmitter();

    @Output() uploadingStatusChange: EventEmitter<boolean> = new EventEmitter();

    public _page: PageInfo;

    public _value: WikiPageContent;

    public _isStencil: boolean;

    public _isFetchDone: boolean;

    public _readonly: boolean;

    public translateService = inject(StyxTranslateService);

    onChange(data: WikiEditPageInfo) {
        this.dataChange.emit(data);
    }

    syncEditorHandle(editor: TheEditor) {
        this.syncEditor.emit(editor);
    }

    statusChange(value: boolean) {
        this.uploadingStatusChange.emit(value);
    }

    ngOnDestroy(): void {
        super.ngOnDestroy();
    }
}
