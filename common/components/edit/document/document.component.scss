@use 'ngx-tethys/styles/variables.scss';
@use '../../../styles/variables.scss' as commonVariables;
@use '@atinc/ngx-styx/styles/variables.scss' as styx-variables;

$max-view-padding: 60px;
$wiki-edit-document-toolbar-height: 52px;

.wiki-edit-document-content {
    background: variables.$bg-default;
}
.wiki-edit-document-content-body {
    position: relative;
    padding-bottom: 40px;
    .wiki-common-editor {
        width: commonVariables.$editable-width;
        margin: 0 auto;
        background: variables.$bg-default;
        position: relative;
        .wiki-edit-document-title-emoji {
            display: flex;
            width: 100%;
            height: 60px;
            justify-content: space-between;
            &-fill {
                height: 30px;
            }
        }
        .wiki-edit-document-title-container {
            padding: 0 commonVariables.$editable-padding-left;
            width: 100%;
            display: inline-flex;
            align-items: center;
            .title-emoji-action {
                cursor: pointer;
                border-radius: 4px;
                min-width: 44px;
                height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
                &:hover {
                    background: variables.$gray-100;
                }
            }
            .wiki-edit-document-title {
                width: 100%;
                font-size: 32px;
                font-weight: bold;
                line-height: 1.4;
                height: auto;
                display: block;
                border: none;
                resize: none;
                padding: 0;
                white-space: nowrap;
                box-sizing: border-box;
                background: variables.$bg-default;
                color: variables.$gray-800;
                text-overflow: ellipsis;
                &:focus {
                    outline: none;
                }
                &::placeholder {
                    color: variables.$gray-400;
                }
            }
        }

        .editor-hr {
            margin-top: 0.4rem;
            margin-left: commonVariables.$editable-padding-left;
            margin-right: commonVariables.$editable-padding-left;
        }
    }

    .the-editor {
        border: none;
        opacity: 1;
        &.hidden {
            opacity: 0;
        }
        .the-editable-container {
            overflow-y: unset;
            padding: 0px;
            margin-top: 24px;

            .the-editor-typo {
                min-height: 100vh;
                padding-top: 0;
                font-size: 15px;
            }

            .the-editor-typo.drag-snapshot-container {
                min-height: 0;
            }

            > .slate-editable-container {
                padding-left: commonVariables.$editable-padding-left;
                padding-right: commonVariables.$editable-padding-left;
            }
        }
    }
}
.wiki-edit-document-toolbar {
    height: $wiki-edit-document-toolbar-height;
    display: flex;
    min-height: $wiki-edit-document-toolbar-height;
    align-items: center;
    justify-content: center;
    .the-toolbar-container {
        height: 100%;
        padding: 0px;
        background-color: transparent;
    }
    .short-bottom-line {
        border-bottom: commonVariables.$wiki-border-default;
    }
}

.max-bottom-line {
    border-bottom: commonVariables.$wiki-border-default;
    .the-toolbar-container {
        border-bottom: 0;
    }
}
.wiki-edit-document-page-words-count {
    position: fixed;
    right: 81px;
    bottom: 40px;
    z-index: 10;
    max-width: 150px;
    width: auto;
    padding: 2px 9px;
    border-radius: 4px;
    font-size: variables.$font-size-base;
    color: variables.$gray-600;
}

.the-error-notify-content {
    display: flex;
}

.wiki-edit-document-max-view {
    .wiki-edit-document-toolbar {
        justify-content: start;
        padding-left: 48px;
        transition: none;
    }
    .wiki-edit-document-content-body {
        .wiki-common-editor {
            width: calc(100% - 40px - #{commonVariables.$page-content-toc-width} - 20px);
            margin: 0;
            .the-editable-container > .slate-editable-container,
            .wiki-edit-document-title-container {
                padding-left: $max-view-padding;
            }
        }
    }
    .wiki-edit-document-toolbar {
        justify-content: start;
        padding-left: 48px;
        transition: none;
    }
}

.wiki-edit-document-max-view-with-side {
    .wiki-edit-document-content-body {
        .wiki-common-editor {
            width: 100%;
            margin: 0;
            .the-editable-container > .slate-editable-container,
            .wiki-edit-document-title-container {
                padding: 0 $max-view-padding;
            }
            .max-view-icon {
                right: $max-view-padding;
            }
        }
    }
}

@media (max-width: 1440px) {
    .wiki-edit-document-content-body {
        .wiki-common-editor {
            margin-right: 280px;
        }
    }
}

@media (max-width: 1200px) {
    .wiki-edit-document-content {
        .wiki-common-toc {
            width: calc(100% - 870px);
            max-width: 300px;
        }
    }
}
@media (max-width: 870px) {
    .wiki-edit-document-max-view {
        .wiki-edit-document-content-body {
            .wiki-common-editor {
                width: calc(100% - 40px - 20px);
                margin: 0;
                transition: width 0.2s;
            }
        }
    }
}
