@if (!isMobileMode) {
  <styx-entity-detail-header class="fullscreen-header">
    <ng-template #headerMain>
      <wiki-common-fullscreen-header [editor]="editor"></wiki-common-fullscreen-header>
    </ng-template>
  </styx-entity-detail-header>
  <div class="wiki-edit-document-toolbar" [ngClass]="{ 'max-bottom-line': isMaxView }">
    <the-toolbar #globalToolbar [ngClass]="{ 'short-bottom-line': !isMaxView }" [isMore]="false" [afterTemplate]="afterTemplate">
      <ng-template #afterTemplate>
        <a
          href="javascript:;"
          thyAction
          class="fullscreen-hidden"
          [thyActionActive]="!isCollapsedToc && activeMenu === 'toc'"
          (mousedown)="toggleTocExpand($event)"
          thyTooltipPlacement="top"
          styxI18nTracking
          [thyTooltip]="'wiki.plugin.outline' | translate"
        >
          <thy-icon thyIconName="toc"></thy-icon>
        </a>
        @if (!page?.is_published && !isStencil) {
          <a
            href="javascript:;"
            class="ml-1 fullscreen-hidden"
            thyAction
            [thyActionActive]="!isCollapseStencil && activeMenu === 'stencil'"
            (mousedown)="toggleStencilExpand()"
            thyTooltipPlacement="top"
            styxI18nTracking
            [thyTooltip]="'styx.template' | translate"
          >
            <thy-icon thyIconName="magic"></thy-icon>
          </a>
        }
      </ng-template>
    </the-toolbar>
  </div>
  <styx-entity-detail-body class="wiki-edit-document-content height-100">
    <styx-entity-detail-body-content
      cdkScrollable
      class="scroll-container wiki-edit-document-content-body wiki-fullscreen-prevent-scroll wiki-editor-scroll-container m-0 mt-0"
    >
      <div class="wiki-common-editor">
        <div class="wiki-edit-document-title-container fullscreen-hidden">
          <div class="wiki-edit-document-title-emoji pt-4 pb-3" (mouseenter)="emojiSelectShow()" (mouseleave)="emojiSelectHide()">
            <ng-container>
              @if (isShowHint || isOpened) {
                <a
                  #emojiChange
                  href="javascript:;"
                  thyAction
                  [thyActionIcon]="page.emoji_icon ? 'close' : 'smile-plus'"
                  (click)="emojiStateChange($event, !!page.emoji_icon, emojiChange)"
                  styxI18nTracking
                >
                  {{ page.emoji_icon ? ('wiki.document.emoji.remove' | translate) : ('wiki.document.emoji.add' | translate) }}
                </a>
              }
            </ng-container>
            @if (!isShowHint) {
              <div class="wiki-edit-document-title-emoji-fill"></div>
            }
            <a
              thyAction
              href="javascript:;"
              styxI18nTracking
              [thyTooltip]="isMaxView ? ('wiki.document.emoji.standardMode' | translate) : ('wiki.page.operation.fullWidth' | translate)"
              [thyActionIcon]="isMaxView ? 'min-view' : 'max-view'"
              (click)="updateViewport()"
            ></a>
          </div>
        </div>
        <div class="wiki-edit-document-title-container fullscreen-hidden" (mouseenter)="emojiSelectShow()" (mouseleave)="emojiSelectHide()">
          @if (page.emoji_icon) {
            <div class="title-emoji-action mr-1">
              <styx-emoji
                #titleEmoji
                thyTooltipPlacement="bottom"
                styxI18nTracking
                [thyTooltip]="'wiki.document.emoji.change' | translate"
                [styxCode]="page.emoji_icon"
                [styxSize]="32"
                (click)="openEmoji($event, titleEmoji)"
              ></styx-emoji>
            </div>
          }
          <input
            class="wiki-edit-document-title fullscreen-hidden"
            type="text"
            styxI18nTracking
            [placeholder]="'wiki.page.untitled.document' | translate"
            [maxlength]="titleMaxLength"
            [thyAutofocus]="!page.name"
            [(ngModel)]="page.name"
            (ngModelChange)="onTitleChange()"
            (focus)="pageTitleFocus()"
            (blur)="pageTitleBlur()"
            (keydown.enter)="pageTitleEnter($event)"
          />
        </div>
        @if (!isEditorLoadingDone) {
          <thy-loading></thy-loading>
        }
        <ng-template [ngTemplateOutlet]="editorTemplate" [ngTemplateOutletContext]="{ globalToolbar }"></ng-template>
        @if (editor && !readonly) {
          <div class="drag-container fullscreen-hidden">
            <div wikiDnd [editor]="editor" (dragChange)="onDragChange($event)"></div>
            <div class="drag-snapshot-container"></div>
          </div>
          <div
            wikiHeadingHandle
            class="fullscreen-hidden"
            [isFullScreen]="isFullScreen"
            [editor]="editor"
            [scene]="headingHandleScene"
            [hasHeadingAnchor]="false"
            [hasHeadingFold]="true"
          ></div>
        }
      </div>
      @if (page && activeMenu === 'toc') {
        <wiki-common-toc
          type="edit"
          [editor]="editor"
          [value]="editor?.children"
          [container]="'.scroll-container'"
          [title]="page.name"
          [isCollapsed]="isCollapsedToc"
          class="fullscreen-hidden"
          (collapsedChange)="tocCollapsedChange($event)"
        ></wiki-common-toc>
      }
      @if (page && activeMenu === 'stencil' && !isCollapseStencil && !page?.is_published && !isStencil && stencilSideTemplate) {
        <ng-template
          *ngTemplateOutlet="
            stencilSideTemplate;
            context: {
              $implicit: {
                value: page.document,
                page,
                isMaxView,
                isStencilContent,
                useStencilContent,
                toggleStencilChange
              }
            }
          "
        ></ng-template>
      }
    </styx-entity-detail-body-content>
  </styx-entity-detail-body>
  <div class="wiki-edit-document-page-words-count fullscreen-hidden" styxI18nTracking>
    {{ 'wiki.document.wordCount' | translate: { count: pageWordsCount } }}
  </div>
  <wiki-common-back-top [container]="'.scroll-container'" class="fullscreen-hidden"></wiki-common-back-top>
}

@if (isMobileMode) {
  <div class="wiki-edit-document-title">
    @if (page.emoji_icon) {
      <styx-emoji #titleEmoji class="title-emoji-action mr-1" [styxCode]="page.emoji_icon" [styxSize]="32"></styx-emoji>
    }
    <input
      class="wiki-edit-document-name"
      type="text"
      styxI18nTracking
      [placeholder]="'wiki.page.untitled.document' | translate"
      [maxlength]="titleMaxLength"
      [thyAutofocus]="!page.name"
      [(ngModel)]="page.name"
      (ngModelChange)="onTitleChange()"
      (focus)="pageTitleFocus()"
      (blur)="pageTitleBlur()"
      (keydown.enter)="pageTitleEnter($event)"
    />
  </div>
  <ng-template [ngTemplateOutlet]="editorTemplate"></ng-template>
}

<ng-template #editorTemplate let-globalToolbar="globalToolbar">
  <the-editor
    #theEditor
    [class.hidden]="!isEditorLoadingDone"
    [ngModel]="!isPublished ? page.document : null"
    [theOptions]="theOptions"
    [theGlobalToolbar]="globalToolbar"
    [thePlugins]="customPlugins"
    [theOnError]="theOnErrorHandler"
    [theDecorate]="decorate"
    (ngModelChange)="onChange($event)"
    (theOnSave)="onSave()"
    (theEditorCreated)="editorCreate($event)"
    (theOnDOMEvent)="theOnDOMEvent($event)"
    (theUploadingStatus)="onUploadingStatus($event)"
  ></the-editor>
</ng-template>
