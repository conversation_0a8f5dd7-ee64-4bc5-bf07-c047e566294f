import {
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    EventEmitter,
    inject,
    Injector,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewContainerRef
} from '@angular/core';
import { AppRootContext, ResponseData } from '@atinc/ngx-styx';
import { DEFAULT_BOARD_THEME } from '@plait-editor/constants/default';
import { ESCAPE, PlaitElement, PlaitTheme } from '@plait/core';
import { PageInfo } from '@wiki/app/entities/page-info';
import { withCursor } from '@wiki/common/board-plugins/with-cursor';
import { withYjsBoard } from '@wiki/common/board-plugins/with-yjs-board';
import { getDefaultDocumentTitle, WIKI_FULLSCREEN } from '@wiki/common/constants';
import { TheExtensionMode } from '@wiki/common/interface';
import { IsBoardPipe, IsDocumentPipe } from '@wiki/common/pipe/page.pipe';
import { withShared as withDocumentShared } from '@wiki/common/plugins/shared/shared.plugin';
import { FullscreenService } from '@wiki/common/services/fullscreen.service';
import { CommonPageApiService } from '@wiki/common/services/page-api-service';
import { createDefaultContent } from '@wiki/common/util/common';
import { isMobileMode } from '@wiki/common/util/extension-mode';
import { getSharedEditor } from '@wiki/common/util/shared/shared';
import { YjsBoard } from '@wiki/common/yjs-plait/board/yjs-board';
import { THE_MODE_TOKEN, TheEditor } from '@worktile/theia';
import { withUndoManager } from '@worktile/y-slate';
import Debug from 'debug';
import * as decoding from 'lib0/decoding';
import { ThyDialogRef } from 'ngx-tethys/dialog';
import { ThySlideService } from 'ngx-tethys/slide';
import { catchError, filter, map, of } from 'rxjs';
import { messageYjsSyncStep1, messageYjsSyncStep2, messageYjsUpdate } from 'y-protocols/sync';
import { CommonShortcutsHelpComponent } from '../shortcuts-help/shortcuts-help.component';
import { WikiEditDocumentBaseComponent } from './document/document.base';
import { ThyFullscreen } from 'ngx-tethys/fullscreen';
import { TheDocument } from '@wiki/common/custom-types';

const collaborationDebug = Debug('collaboration-message');
const timeDebug = Debug('wiki-time');

@Component({
    selector: 'wiki-common-edit',
    templateUrl: './edit.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'wiki-common-edit h-100 w-100 d-block',
        '[class.wiki-mobile-edit]': 'isMobileMode'
    },
    standalone: false
})
export class WikiCommonEditComponent extends WikiEditDocumentBaseComponent implements OnInit, OnDestroy {
    private editor: YjsBoard | TheEditor;

    public isMobileMode: boolean;

    private isBoardType: boolean;

    private isDocumentType: boolean;

    @Input() escHandle: () => {};

    @Input() dialogRef: ThyDialogRef<any>;

    @Output() initPageValueAndTitle: EventEmitter<PageInfo> = new EventEmitter();

    constructor(
        private injector: Injector,
        private thySlideNewService: ThySlideService,
        public viewContainerRef: ViewContainerRef,
        private isDocument: IsDocumentPipe,
        private isBoard: IsBoardPipe,
        private elementRef: ElementRef<HTMLElement>,
        public appRootContext: AppRootContext,
        public pageApiService: CommonPageApiService
    ) {
        super();
    }

    ngOnInit(): void {
        if (this.dialogRef) {
            this.dialogRef
                .keydownEvents()
                .pipe(filter(event => event.keyCode === ESCAPE))
                .subscribe(event => {
                    const fullscreenContainer = this.elementRef.nativeElement.closest(`.${WIKI_FULLSCREEN}`) as HTMLElement | null;
                    if (fullscreenContainer) {
                        this.dialogRef.disableClose = true;
                        const fullscreen = this.editor.injector.get(ThyFullscreen);
                        fullscreen.exit();
                    } else {
                        this.escHandle();
                    }
                });
        }
        const modeConfig = this.injector.get(THE_MODE_TOKEN);
        this.isMobileMode = isMobileMode(modeConfig.mode as TheExtensionMode);
        this.isBoardType = this.isBoard.transform({ type: this.page.type });
        this.isDocumentType = this.isDocument.transform({ type: this.page.type });
        this.initPage();
    }

    openShortcutHelp() {
        this.thySlideNewService.open(CommonShortcutsHelpComponent, {
            drawerContainer: 'wiki-common-edit',
            backdropClosable: false,
            hasBackdrop: false,
            viewContainerRef: this.viewContainerRef,
            initialState: {
                pageType: this.page.type
            }
        });
    }

    initPage() {
        let value = this.page.document as TheDocument | PlaitElement[];
        if (((this.isDocumentType || this.isStencil) && !value?.length) || (this.isBoardType && !value)) {
            value = createDefaultContent(this.translateService, this.page.type) as TheDocument | PlaitElement[];
        }
        this.page = {
            ...this.page,
            document: value,
            name: this.page.name === getDefaultDocumentTitle(this.translateService) ? '' : this.page.name
        };
        this.initPageValueAndTitle.emit(this.page);
    }

    initCollaboration = (editor: YjsBoard | TheEditor) => {
        if (!this.isPublished) {
            return;
        }
        const me = this.appRootContext.globalInfo.me;
        const isDocument = this.isDocument.transform({ type: this.page.type });
        const isBoard = this.isBoard.transform({ type: this.page.type });
        const isSynchronizeValue = !this.page.has_shared_cache;
        const sharedEditor = getSharedEditor(
            editor,
            this.collaborationInfo,
            this.page._id,
            me,
            () => {
                return this.pageApiService.fetchCollaborationConnectInfo(this.editId).pipe(
                    map((collaborationData: ResponseData<{ token: string; uri: string }>) => {
                        return collaborationData.value.token;
                    }),
                    catchError(() => {
                        // handle error to avoid break reconnect
                        return of(this.collaborationInfo.token);
                    })
                );
            },
            isSynchronizeValue,
            this.page.document
        );
        this.editor = sharedEditor;
        if (isDocument) {
            withDocumentShared(withUndoManager(sharedEditor, sharedEditor.sharedType));
        }

        if (isBoard) {
            const sharedTheme = sharedEditor.sharedType.doc.getMap<Partial<PlaitTheme>>();
            withCursor(
                withUndoManager(
                    withYjsBoard(sharedEditor as YjsBoard, sharedTheme, this.page?.board_options?.theme || DEFAULT_BOARD_THEME, {
                        isSynchronizeValue
                    }),
                    [sharedEditor.sharedType, sharedTheme]
                )
            );
        }
        if (localStorage.getItem('debug')?.includes('collaboration-message')) {
            this.sharedDebug();
        }
    };

    sharedDebug() {
        const { onChange } = this.editor;
        this.editor.provider.on('status', data => {
            if (data.status === 'connected' && this.editor.provider.ws) {
                const { onmessage } = this.editor.provider.ws;
                this.editor.provider.ws.onmessage = event => {
                    const decoder = decoding.createDecoder(new Uint8Array(event.data));
                    const messageType = decoding.readVarUint(decoder);
                    if (messageType === 0) {
                        const syncMessageType = decoding.readVarUint(decoder);
                        if (syncMessageType === messageYjsSyncStep1) {
                            collaborationDebug(`syncMessageType: step1`);
                        }
                        if (syncMessageType === messageYjsSyncStep2) {
                            collaborationDebug(`syncMessageType: step2`);
                        }
                        if (syncMessageType === messageYjsUpdate) {
                            collaborationDebug(`syncMessageType: update`);
                        }
                    }
                    onmessage(event);
                };
            }
        });
        this.editor.onChange = () => {
            timeDebug('onChangeStart');
            onChange();
            timeDebug('onChangeEnd');
        };
    }

    ngOnDestroy(): void {
        if (this.editor && this.editor.provider) {
            this.editor.provider.destroy();
        }
    }
}
