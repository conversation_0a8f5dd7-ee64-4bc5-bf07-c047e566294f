@if (isDisplayPlaitEditor()) {
  <plait-editor
    #plaitEditor
    [options]="options"
    [value]="!isPublished ? page.document : []"
    [theme]="!isPublished ? page.board_options?.theme : null"
    [viewport]="page.board_options?.viewport"
    (editorInitialized)="plaitBoardInitialized($event)"
    (editorValueChange)="change($event)"
  ></plait-editor>
}
@if (isConnection()) {
  <thy-loading class="is-connecting"></thy-loading>
}
