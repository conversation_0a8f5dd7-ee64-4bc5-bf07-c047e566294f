import { AfterViewInit, Component, EventEmitter, inject, Input, Output, signal } from '@angular/core';
import { PLOptions } from '@plait-editor/types/editor';
import { OnChangeData } from '@plait/angular-board';
import { BoardTransforms, PlaitBoard, PlaitElement, Transforms } from '@plait/core';
import { PageInfo } from '@wiki/app/entities/page-info';
import { WikiEditPageInfo } from '@wiki/common/interface/page';
import { BoardCursorService } from '@wiki/common/services/board-cursor.service';
import { CursorBoard } from '@wiki/common/yjs-plait/board/cursor-board';
import { YjsBoard } from '@wiki/common/yjs-plait/board/yjs-board';
import useCursors from '@wiki/common/yjs-plait/utils/use-cursors';
import { ThyDialogRef } from 'ngx-tethys/dialog';
import { distinctUntilChanged } from 'rxjs';

@Component({
    selector: 'wiki-edit-board',
    templateUrl: './board.component.html',
    host: {
        class: 'wiki-edit-board h-100 d-block'
    },
    providers: [BoardCursorService],
    standalone: false
})
export class WikiEditBoardComponent implements AfterViewInit {
    @Input() page: PageInfo;

    @Input() isPublished: boolean;

    @Input() set readonly(value) {
        this.options.readonly = value;
    }

    @Input() initCollaboration: (board: PlaitBoard) => void;

    @Input() isMobileMode: boolean;

    @Output() dataChange: EventEmitter<WikiEditPageInfo> = new EventEmitter();

    @Output() syncEditor: EventEmitter<CursorBoard> = new EventEmitter();

    options: Partial<PLOptions> = {
        readonly: false,
        hideScrollbar: false
    };

    board!: CursorBoard;

    isDisplayPlaitEditor = signal(false);

    isConnection = signal(false);

    constructor(private boardCursorService: BoardCursorService) {}

    ngAfterViewInit() {
        // 解决 dialog 动画导致 board 获取宽高时不准确的问题
        setTimeout(() => {
            this.isDisplayPlaitEditor.set(true);
            this.options = {
                ...this.options,
                viewportMode: this.page.board_options?.viewportMode || 'autoFit'
            };
        }, 200);
    }

    plaitBoardInitialized(value: CursorBoard) {
        this.board = value;
        if (this.isPublished) {
            this.initShared();
        } else {
            !this.page.board_options?.viewport && BoardTransforms.fitViewport(this.board);
            Transforms.setSelection(this.board, { anchor: [0, 0], focus: [0, 0] });
        }
    }

    initShared() {
        this.isConnection.set(true);
        this.initCollaboration(this.board);
        const decorate$ = useCursors(this.board);
        decorate$
            .pipe(
                distinctUntilChanged((previous, current) => {
                    return JSON.stringify(previous.data) === JSON.stringify(current.data);
                })
            )
            .subscribe(value => {
                this.boardCursorService.removeCursors();
                if (value.data && value.data.length) {
                    value.data.map(item => {
                        const { nodes } = item;
                        if (nodes && nodes.length === 1) {
                            this.boardCursorService.render(this.board, item.nodes[0].path, item);
                        }
                    });
                }
            });
        // 协同初始化完成，plait 把 change 事件放到 afterChange 之后导致 协同初始化后 onChange 不触发了
        const onChange = this.board.onChange;
        this.board.onChange = () => {
            onChange();
            if (this.isConnection() && (this.board as YjsBoard).isInitialized) {
                this.isConnection.set(false);
                Transforms.setSelection(this.board, { anchor: [0, 0], focus: [0, 0] });
            }
        };
        this.syncEditor.emit(this.board);
    }

    change(event: OnChangeData) {
        if (this.isPublished && !(this.board as YjsBoard).isInitialized) {
            return;
        }
        if (event.operations && event.operations.length > 0 && event.operations[0].type === 'set_selection') {
            return;
        }
        this.dataChange.emit({
            data: {
                value: event.children,
                name: this.page.name,
                isSameValue: false
            },
            boardOptions: {
                viewport: event.viewport,
                theme: event.theme
            }
        });
    }
}
