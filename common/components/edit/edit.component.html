@if (page | isDocument) {
  <wiki-edit-document
    [saveHandle]="saveHandle"
    [isAutoSave]="isAutoSave"
    [page]="page"
    [isMobileMode]="isMobileMode"
    [collaborationInfo]="collaborationInfo"
    [initCollaboration]="initCollaboration"
    [editId]="editId"
    [readonly]="readonly"
    [isPublished]="isPublished"
    [stencilSideTemplate]="stencilSideTemplate"
    (dataChange)="onChange($event)"
    (syncEditor)="syncEditorHandle($event)"
    (uploadingStatusChange)="statusChange($event)"
    [isFetchDone]="isFetchDone"
  >
  </wiki-edit-document>
}

@if (page | isBoard) {
  <wiki-edit-board
    [page]="page"
    [initCollaboration]="initCollaboration"
    [isFetchDone]="isFetchDone"
    [isPublished]="isPublished"
    [readonly]="readonly"
    [isMobileMode]="isMobileMode"
    (dataChange)="onChange($event)"
    (syncEditor)="syncEditorHandle($event)"
    (uploadingStatusChange)="statusChange($event)"
  ></wiki-edit-board>
}

@if (!isMobileMode) {
  <div class="shortcut-tip fullscreen-hidden" (click)="openShortcutHelp()">
    <thy-icon
      styxI18nTracking
      [thyTooltip]="'wiki.shortcuts.title' | translate"
      class="text-muted font-size-lg"
      thyIconName="shortcut"
    ></thy-icon>
  </div>
}
