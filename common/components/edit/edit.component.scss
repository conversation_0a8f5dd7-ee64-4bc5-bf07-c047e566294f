@use '../../styles/variables.scss' as commonVariables;
@use 'ngx-tethys/styles/variables.scss';

.shortcut-tip {
    position: fixed;
    right: commonVariables.$anchor-right + commonVariables.$anchor-padding;
    bottom: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    overflow: hidden;
    background-color: variables.$bg-default;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.wiki-edit-board {
    .wiki-board-cursor,
    .wiki-shared-caret,
    .wiki-shared-caret-line,
    .wiki-shared-cursor {
        position: unset;
    }
    .wiki-board-cursor {
        .wiki-shared-caret-line {
            margin-top: -4px;
        }
    }
    .is-connecting {
        position: absolute;
        top: 80px;
    }
}
