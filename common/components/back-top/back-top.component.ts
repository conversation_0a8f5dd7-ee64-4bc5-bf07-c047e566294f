import { Component, Input, Output, EventEmitter, HostBinding } from '@angular/core';

@Component({
    selector: 'wiki-common-back-top',
    template: `
        <thy-back-top
            class="wiki-back-top"
            (visibleChange)="visibleChange($event)"
            [thyContainer]="container"
            [thyTemplate]="backtopTemplate"
            [thyVisibilityHeight]="1530"
        >
            <ng-template #backtopTemplate>
                <div class="thy-back-top-content">
                    <thy-icon class="back-top-icon text-muted font-size-lg" thyIconName="arrow-up"></thy-icon>
                </div>
            </ng-template>
        </thy-back-top>
    `,
    standalone: false
})
export class CommonBackTopComponent {
    @HostBinding('class')
    hostClass = 'wiki-common-back-top';

    @Input() container: string | HTMLElement;

    @Output() public backTopVisible: EventEmitter<boolean> = new EventEmitter();

    visibleChange(val) {
        this.backTopVisible.emit(val);
    }
}
