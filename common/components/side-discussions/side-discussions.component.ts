import { ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { switchMap, timer } from 'rxjs';
import { Element } from 'slate';
import { NODE_TO_ELEMENT } from 'slate-dom';
import { Discussion } from '../../custom-types';

interface DiscussionMark extends Discussion {
    top: number;
    left: number;
}

@Component({
    selector: 'side-discussions',
    templateUrl: 'side-discussions.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class SideDiscussionsComponent implements OnInit, OnChanges {
    @Input() discussions: Discussion[];

    @Input() theValue: Element[];

    protected rootDiscussionMarks: DiscussionMark[] = [];

    private initialized = false;

    private rootDiscussions: Discussion[];

    constructor(
        private cdr: ChangeDetectorRef,
        private destroyRef: DestroyRef,
        private wikiPluginContext: WikiPluginContext
    ) {}

    ngOnInit() {
        timer(5000)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this.useDiscussionMarks();
                this.initialized = true;
            });
        this.subscribeMaxViewEvents();
    }

    ngOnChanges(simpleChanges: SimpleChanges) {
        if (simpleChanges['discussions']) {
            this.rootDiscussions = [];
            if (this.discussions) {
                this.discussions.forEach(discussion => {
                    const isExist = this.rootDiscussions.some(rootDiscussion => {
                        return rootDiscussion.node_key === discussion.node_key;
                    });
                    if (!isExist && discussion._id) {
                        this.rootDiscussions.push(discussion);
                    }
                });
            }
        }
        if (this.initialized) {
            timer(0).subscribe(() => {
                this.useDiscussionMarks();
            });
        }
    }

    trackBy(_, item: Discussion) {
        return item.node_key;
    }

    openDiscussionList(event: Event, mark: DiscussionMark) {
        const native = window.getSelection();
        if (native.type !== 'Range') {
            this.wikiPluginContext.openDiscussionList(event.currentTarget as HTMLElement, mark);
        }
    }

    private useDiscussionMarks() {
        this.rootDiscussionMarks = [];
        this.rootDiscussions.forEach(rootDiscussion => {
            const element = this.getNodeElement(rootDiscussion.node_key);
            if (element) {
                const parentElement = element.offsetParent as HTMLElement;
                const isBlockCard = parentElement.matches('.slate-block-card');
                const offsetWidth = isBlockCard ? parentElement.offsetWidth : element.offsetWidth;
                const offsetTop = isBlockCard ? parentElement.offsetTop : element.offsetTop;
                this.rootDiscussionMarks.push({
                    left: element.offsetLeft + offsetWidth + 9,
                    top: offsetTop,
                    ...rootDiscussion
                });
            }
        });
        this.cdr.markForCheck();
    }

    private getNodeElement(nodeKey: string) {
        const node =
            this.theValue?.find(block => {
                return block.key === nodeKey;
            }) || null;
        return NODE_TO_ELEMENT.get(node);
    }

    private subscribeMaxViewEvents() {
        this.wikiPluginContext.maxView$
            .pipe(
                switchMap(() => timer(100)),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => {
                this.useDiscussionMarks();
            });
    }
}
