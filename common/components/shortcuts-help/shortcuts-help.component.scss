@use 'ngx-tethys/styles/variables.scss';

.wiki-shortcuts-container {
    height: 100%;
}

.shortcut-wrapper {
    .thy-slide-header {
        height: 52px;
        z-index: 1;
        flex-shrink: 0;
        .thy-slide-header-title {
            font-size: 14px;
        }
    }

    .thy-slide-body {
        flex-shrink: 1;
        overflow-y: auto;
        .shortcuts-content {
            position: relative;
            &:not(:last-child) {
                &::after {
                    content: '';
                    position: absolute;
                    width: 310px;
                    height: 1px;
                    bottom: -18px;
                    background: variables.$gray-200;
                }
            }
            .operation-type {
                line-height: 20px;
                color: variables.$gray-600;
                padding: 17px 0;
                font-size: 14px;
            }
            .shortcut-detail {
                margin-bottom: 17px;
            }

            .shortcut-key {
                .keyboard {
                    height: 20px;
                    padding: 0 4px;
                    border-radius: 3px;
                    background: variables.$gray-100;
                    text-align: center;
                    min-width: 20px;
                }
            }
        }
    }
}
