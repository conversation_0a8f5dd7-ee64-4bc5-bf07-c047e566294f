import { Component, OnInit, HostBinding, Input, inject } from '@angular/core';
import { PageTypes } from '@wiki/app/constants/page';
import {
    getOperationShortcuts,
    ShortcutsType,
    getTextShortcuts,
    getMarkdownShortcuts,
    getEditShortcuts,
    getBoardShortcuts,
    getInsertionShortcuts,
    getMindShortcuts,
    getCanvasShortcuts,
    getTableShortcuts
} from './shortcuts-help.info';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-common-shortcuts',
    templateUrl: './shortcuts-help.component.html',
    standalone: false
})
export class CommonShortcutsHelpComponent implements OnInit {
    translate = inject(StyxTranslateService);

    @HostBinding(`class`) class = `wiki-shortcuts-container`;
    public isMac = false;

    @Input() pageType = PageTypes.document;

    public pageShortcutGroups: ShortcutsType[] = [
        {
            name: this.translate.instant<I18nSourceDefinitionType>('common.operation'),
            includes: getOperationShortcuts(this.translate)
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.textFormat'),
            includes: getTextShortcuts(this.translate)
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('common.table'),
            includes: getTableShortcuts(this.translate)
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.markdownSyntax'),
            includes: getMarkdownShortcuts(this.translate)
        }
    ];

    public boardShortcutGroups: ShortcutsType[] = [
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.plugin.insert'),
            includes: getInsertionShortcuts(this.translate)
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.canvas'),
            includes: getCanvasShortcuts(this.translate)
        },
        // {
        //     name: this.translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.fontFormat'),
        //     includes: TextShortcuts
        // },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('styx.mindMap'),
            includes: getMindShortcuts(this.translate)
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('common.edit'),
            includes: getEditShortcuts(this.translate)
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.plugin.board'),
            includes: getBoardShortcuts(this.translate)
        }
    ];

    shortcutTypes: ShortcutsType[];

    ngOnInit() {
        this.shortcutTypes = this.pageType === PageTypes.board ? this.boardShortcutGroups : this.pageShortcutGroups;
    }
}
