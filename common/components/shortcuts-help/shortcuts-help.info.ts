import { StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { CONTROL_KEY } from '@worktile/theia';

export interface ShortcutsType {
    name: string;
    includes: ShortcutsInfo[];
}
export interface ShortcutsInfo {
    name: string;
    keys: string[][];
}

export const getInsertionShortcuts = (translate: StyxTranslateService) => {
    return [
        {
            name: translate.instant<I18nSourceDefinitionType>('styx.mindMap'),
            keys: [['M']]
        }
    ];
};

export const getCanvasShortcuts = (translate: StyxTranslateService) => {
    return [
        {
            name: translate.instant<I18nSourceDefinitionType>('common.move'),
            keys: [['Space', translate.instant<I18nSourceDefinitionType>('styx.drag')]]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.zoom'),
            keys: [[CONTROL_KEY, translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.scroll')]]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('styx.zoomIn'),
            keys: [[CONTROL_KEY, '+']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('styx.zoomOut'),
            keys: [[CONTROL_KEY, '-']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.actualSize'),
            keys: [[CONTROL_KEY, '0']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('styx.fitCanvas'),
            keys: [[CONTROL_KEY, 'Shift', '=']]
        }
    ];
};

export const getMindShortcuts = (translate: StyxTranslateService) => {
    return [
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.expandCollapseNode'),
            keys: [[CONTROL_KEY, '/']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.insertSubset'),
            keys: [['Tab']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.insertSibling'),
            keys: [['Enter']]
        }
    ];
};

export const getEditShortcuts = (translate: StyxTranslateService) => {
    return [
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.undo'),
            keys: [[CONTROL_KEY, 'Z']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.redo'),
            keys: [[CONTROL_KEY, 'Shift', 'Z']]
        }
    ];
};

export const getOperationShortcuts = (translate: StyxTranslateService) => {
    return [
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.quickInsert'),
            keys: [['/']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.plugin.saveDraft'),
            keys: [[CONTROL_KEY, 'S']]
        },
        ...getEditShortcuts(translate),
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.softEnter'),
            keys: [['Shift', 'Enter']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.increaseIndent'),
            keys: [['Tab']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.decreaseIndent'),
            keys: [['Shift', 'Tab'], ['BackSpace']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.emoji'),
            keys: [['Space', ':']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.mention'),
            keys: [['Space', '@']]
        }
    ];
};

export const getTextShortcuts = (translate: StyxTranslateService) => {
    return [
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.bold'),
            keys: [[CONTROL_KEY, 'B']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.italic'),
            keys: [[CONTROL_KEY, 'I']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.underline'),
            keys: [[CONTROL_KEY, 'U']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.inlineCode'),
            keys: [[CONTROL_KEY, 'E']]
        }
    ];
};

export const getTableShortcuts = (translate: StyxTranslateService) => {
    return [
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.selectCell'),
            keys: [['Shift', '↑'], ['↓'], ['←'], ['→']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.selectCellRange'),
            keys: [['Shift']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.multiSelectCell'),
            keys: [[CONTROL_KEY]]
        }
    ];
};

export const getMarkdownShortcuts = (translate: StyxTranslateService) => {
    return [
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.markdown.heading1'),
            keys: [['#', 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.markdown.heading2'),
            keys: [['##', 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.markdown.heading3'),
            keys: [['###', 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.markdown.heading4'),
            keys: [['####', 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.markdown.heading5'),
            keys: [['#####', 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.markdown.heading6'),
            keys: [['######', 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.bold'),
            keys: [[translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.markdown.bold'), 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.italic'),
            keys: [[translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.markdown.italic'), 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.strikethrough'),
            keys: [[translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.markdown.strikethrough'), 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.inlineCode'),
            keys: [[translate.instant<I18nSourceDefinitionType>(`wiki.shortcuts.actions.inlineCode`), 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.orderedList'),
            keys: [['1.', 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.unorderedList'),
            keys: [['-', 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.todoList'),
            keys: [['[]', 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.codeBlock'),
            keys: [['```', 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('common.referred'),
            keys: [['>', 'Space']]
        },
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.divider'),
            keys: [['---', 'Space']]
        }
    ];
};

export const getBoardShortcuts = (translate: StyxTranslateService) => {
    return [
        {
            name: translate.instant<I18nSourceDefinitionType>('wiki.shortcuts.actions.editBoard'),
            keys: [['E']]
        }
    ];
};
