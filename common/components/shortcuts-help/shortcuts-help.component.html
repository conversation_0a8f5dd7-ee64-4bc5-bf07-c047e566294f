<thy-slide-layout class="shortcut-wrapper">
  <thy-slide-header styxI18nTracking [thyTitle]="'wiki.shortcuts.title' | translate"></thy-slide-header>
  <thy-slide-body>
    @for (shortcutType of shortcutTypes; track shortcutType) {
      <div class="shortcuts-content">
        <div class="operation-type">{{ shortcutType.name }}</div>
        @for (shortcut of shortcutType.includes; track $index) {
          <div class="shortcut-detail d-flex justify-content-between align-items-center">
            <span>{{ shortcut.name }}</span>
            <ng-template [ngTemplateOutlet]="shortcutKey" [ngTemplateOutletContext]="{ shortcut: shortcut }"> </ng-template>
          </div>
        }
      </div>
    }
  </thy-slide-body>
</thy-slide-layout>

<ng-template #shortcutKey let-shortcut="shortcut">
  <div class="shortcut-key d-flex">
    @for (KeyGroup of shortcut.keys; track $index; let index = $index) {
      <span class="d-flex align-items-center">
        @for (key of KeyGroup; track $index; let groupIndex = $index) {
          <span class="keyboard d-flex align-items-center justify-content-center"> {{ key }}</span>
          @if (groupIndex < KeyGroup.length - 1) {
            <span class="mx-1">+</span>
          }
        }
        @if (index < shortcut.keys.length - 1) {
          <span class="mx-1">/</span>
        }
      </span>
    }
  </div>
</ng-template>
