import { Overlay } from '@angular/cdk/overlay';
import { Component, HostListener, OnInit, TemplateRef, ViewChild, ViewContainerRef, inject } from '@angular/core';
import { Router } from '@angular/router';
import {
    AppRootContext,
    ApplicationType,
    Is,
    StyxBroadObjectDispatcher,
    StyxPivotEntity,
    StyxTranslateService,
    getObjectApplicationByType
} from '@atinc/ngx-styx';
import { RelationItemElement } from '@wiki/common/custom-types';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { RelationItemState } from '@wiki/common/interface/relation';
import { WikiCardEditor } from '@wiki/common/plugins/common/card-editor';
import { RelationItemEditor } from '@wiki/common/plugins/relation-item/relation-item.editor';
import { RelationApiService } from '@wiki/common/services/relation-api.service';
import { PageStore } from '@wiki/common/stores/page.store';
import { RelationItemsStore } from '@wiki/common/stores/relation-items.store';
import { getCommonRelationOption, RelationItemOption, RelationOpenType, WikiPluginTypes } from '@wiki/common/types';
import { ClientEventEmitter } from '@wiki/common/util/bridge-for-mobile';
import { getDomainUrl, verifyAvailableAppByBroadObjectType } from '@wiki/common/util/common';
import { isMobileMode, isSharedMode } from '@wiki/common/util/extension-mode';
import { TheBaseElement, TheEditor, TheModeType, TheQueries, getMode, topLeftPosition, updatePopoverPosition } from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { RelationItemInfo, RelationItemRoute } from '../../plugins/relation-item/type';

@Component({
    selector: 'common-relation-item, [commonRelationItem]',
    templateUrl: './relation-item.component.html',
    host: {
        class: 'cursor-pointer outside-cursor-default common-relation-item',
        '[class.text-mode]': 'element?.mode !== "card"',
        '[class.danger-mode]': 'isDanger',
        '[class.cursor-default]': '!isAvailableApp'
    },
    standalone: false
})
export class CommonRelationItemComponent extends TheBaseElement<RelationItemElement, TheEditor> implements OnInit {
    translate = inject(StyxTranslateService);

    application: ApplicationType;

    RelationItemState = RelationItemState;

    relationItemState: RelationItemState = RelationItemState.not_found;

    relationItem: RelationItemInfo;

    isDanger: boolean;

    relationItemRoute: RelationItemRoute;

    toolbarPopoverRef: ThyPopoverRef<any>;

    isAvailableApp = false;

    isAutofocus = false;

    relationItemOption: RelationItemOption;

    relationItemsStore: RelationItemsStore<string>;

    mode: TheModeType;

    get isToolbarOpen() {
        return this.toolbarPopoverRef && this.toolbarPopoverRef.getOverlayRef() && this.toolbarPopoverRef.getOverlayRef().hasAttached();
    }

    get principal_id() {
        return this.relationItemsStore.queryParams.principal_id;
    }

    @ViewChild('toolbar', { read: TemplateRef, static: true })
    toolbar: TemplateRef<any>;

    @HostListener('click')
    handleClick() {
        if (isMobileMode(this.mode as TheExtensionMode) && this.element._id) {
            ClientEventEmitter.open(this.element.type, this.element._id);
            return;
        }
        if (
            this.readonly &&
            this.relationItemState !== RelationItemState.not_found &&
            this.relationItem?._id &&
            this.relationItemRoute?.url
        ) {
            if (this.relationItemRoute.open_type === RelationOpenType.SELF) {
                this.styxBroadObjectDispatcher.dispatchOpenBroadObjectEvent(
                    {
                        _id: this.relationItem.short_id ?? this.relationItem._id,
                        type: this.relationItemOption?.broadObjectType,
                        application: this.application
                    },
                    {
                        versionId: this.relationItem.is_latest_version ? null : this.relationItem.version_id
                    }
                );
            } else {
                window.open(this.relationItemRoute?.url, '_blank');
            }
            return;
        }
    }

    isInPingCodePortal() {
        return !isMobileMode(this.mode) && !isSharedMode(this.mode as TheExtensionMode);
    }

    public router = inject(Router);
    public pageStore = inject(PageStore);
    public relationApiService = inject(RelationApiService);
    public viewContainerRef = inject(ViewContainerRef);
    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);
    private appRootContext = inject(AppRootContext);
    private styxBroadObjectDispatcher = inject(StyxBroadObjectDispatcher);

    onContextChange(): void {
        super.onContextChange();
        if (!this.mode) {
            this.mode = getMode(this.editor);
        }
        if (isMobileMode(this.mode)) {
            return;
        }
        if (this.isCollapsedAndNonReadonly) {
            this.openToolbar();
        } else {
            this.closeToolbar();
        }
    }

    ngOnInit() {
        super.ngOnInit();
        this.relationItemOption = getCommonRelationOption(this.translate)[this.element?.type];
        this.relationItemsStore = this.pageStore.getRelationItemStore(this.element?.type);
        this.application = getObjectApplicationByType(this.relationItemOption?.broadObjectType);
        this.isAutofocus = this.isCollapsedAndNonReadonly && TheQueries.isGlobalCollapsed(this.editor) && !this.element._id;
        if (this.isInPingCodePortal()) {
            this.isAvailableApp = verifyAvailableAppByBroadObjectType(this.editor, this.relationItemOption?.broadObjectType);
        }
        this.getRelationItem();
    }

    getRelationItem() {
        // 当前只会订阅一次，那在选择关联项后会删除当前关联项重新插入一个新的
        if (!this.element._id) {
            return;
        }
        this.relationItemState = RelationItemState.loading;
        this.relationItemsStore
            .getRelationItemById(this.relationItemOption.broadObjectType, this.element?._id)
            .subscribe((item: (StyxPivotEntity & { is_deleted: Is }) | null) => {
                if (item && !item.is_deleted) {
                    this.setRelationItem(item);
                } else {
                    this.relationItemState = RelationItemState.not_found;
                }
                this.cdr.markForCheck();
            });
    }

    openToolbar() {
        if (!TheQueries.isGlobalCollapsed(this.editor) || this.isToolbarOpen) {
            return;
        }

        const origin = this.elementRef.nativeElement as HTMLElement;
        this.toolbarPopoverRef = this.thyPopover.open(this.toolbar, {
            origin,
            viewContainerRef: this.viewContainerRef,
            minWidth: 0,
            panelClass: 'the-plugin-toolbar-popover',
            manualClosure: true,
            hasBackdrop: false,
            insideClosable: true,
            outsideClosable: false,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });

        const overlayRef = this.toolbarPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    closeToolbar() {
        if (this.isToolbarOpen) {
            this.toolbarPopoverRef.close();
        }
    }

    setRelationItem(relationItem: RelationItemInfo) {
        this.relationItemState = RelationItemState.success;
        this.relationItem = relationItem;
        if (this.isInPingCodePortal()) {
            const domain = getDomainUrl(this.appRootContext.globalInfo.config.baseUrlFormat, this.appRootContext.team.domain);
            const period_id = this.element.type === WikiPluginTypes.relationObjective ? `/${relationItem.period_id}` : '';
            this.relationItemRoute = {
                url: `${domain}/${this.relationItemOption.routePrefix}${period_id}/${this.relationItem.short_id ?? this.element?._id}`,
                open_type: RelationOpenType.SELF
            };
        }
    }

    wrapText(event: MouseEvent) {
        event.preventDefault();
        RelationItemEditor.wrapText(this.editor, this.element?.type, this.element);
    }

    switchCard(event: MouseEvent) {
        event.preventDefault();
        WikiCardEditor.switchCard(this.editor, this.element);
    }

    openRelationItem(event: KeyboardEvent) {
        event.preventDefault();
        window.open(this.relationItemRoute?.url, '_blank');
    }

    removeNode(event) {
        event.preventDefault();
        RelationItemEditor.removeNode(this.editor, this.element);
    }

    onEnterDelete() {
        this.isDanger = true;
    }

    onLeaveDelete() {
        this.isDanger = false;
    }
}
