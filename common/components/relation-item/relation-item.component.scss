@use 'ngx-tethys/styles/variables.scss';
@use '../../../common/styles/variables.scss' as commonVariables;
@forward './suggestion/suggestion.component.scss';

.common-relation-item {
    max-width: 100%;

    &:not(.text-mode) {
        position: relative;
        display: block;
    }
    &.text-mode {
        display: inline-flex;
        position: relative;
        border: 2px solid transparent;
    }
    &.disabled {
        .relation-item-card {
            cursor: inherit;
        }
        .name {
            color: variables.$gray-400;
        }
    }
    .card-pilot-name {
        max-width: 12rem;
        line-height: 22px;
    }
    .relation-item-card {
        .card-sub-content {
            width: 100%;
            font-size: 12px;
            // 进度条样式
            .thy-progress {
                min-width: 110px;
            }

            .styx-state {
                max-width: 110px;
            }
        }
    }
}
