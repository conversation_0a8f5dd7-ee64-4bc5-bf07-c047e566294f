import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, ElementRef, inject, Input, NgZone } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TheEditor, topLeftPosition, updatePopoverPosition } from '@worktile/theia';
import { ThyPopoverRef } from 'ngx-tethys/popover';
import { RelationItemInfo } from '../../../plugins/relation-item/type';

@Component({
    selector: 'common-relation-item-text, [commonRelationItemText]',
    template: `
        @if (relationItem) {
            <styx-business-object-brand
                [styxType]="broadObjectType"
                [styxEntity]="relationItem"
                [styxShowName]="true"
                [styxShowIdentifier]="true"
                [styxFlexible]="true"
            ></styx-business-object-brand>
        }
    `,
    host: {
        class: 'common-plugin-card-element relation-item-text common-inline-text-mode px-2'
    },
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class CommonRelationItemTextComponent implements AfterViewInit {
    @Input() relationItem: RelationItemInfo;

    @Input() broadObjectType: string;

    @Input() readonly: boolean;

    @Input() editor: TheEditor;

    @Input() isCollapsedAndNonReadonly: boolean;

    @Input() toolbarPopoverRef: ThyPopoverRef<any>;

    private elementRef = inject(ElementRef);

    private ngZone = inject(NgZone);

    private destroyRef = inject(DestroyRef);

    ngAfterViewInit(): void {
        if (this.isCollapsedAndNonReadonly) {
            this.ngZone.onStable.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
                const overlayRef = this.toolbarPopoverRef?.getOverlayRef();
                if (overlayRef && this.isCollapsedAndNonReadonly) {
                    const origin = this.elementRef?.nativeElement.parentElement;
                    updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
                }
            });
        }
    }
}
