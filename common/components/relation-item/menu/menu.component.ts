import { Component, inject, ViewChild } from '@angular/core';
import { helpers, StyxTranslateService } from '@atinc/ngx-styx';
import { relationIdeaMenuItem } from '@wiki/common/plugins/relation-idea/relation-idea.plugin';
import { relationObjectiveMenuItem } from '@wiki/common/plugins/relation-objective/relation-objective.plugin';
import { relationPageMenu } from '@wiki/common/plugins/relation-page/relation-page.plugin';
import { relationTestCaseMenuItem } from '@wiki/common/plugins/relation-test-case/relation-test-case.plugin';
import { relationTicketMenuItem } from '@wiki/common/plugins/relation-ticket/relation-ticket.plugin';
import { relationWorkItemMenuItem } from '@wiki/common/plugins/relation-work-item/relation-work-item.plugin';
import { TheEditor, ThePluginMenuComponent, ThePluginMenuItem } from '@worktile/theia';

@Component({
    selector: 'common-relation-menu, [commonRelationMenu]',
    templateUrl: './menu.component.html',
    host: {
        class: 'common-relation-menu d-block pt-3 pb-1'
    },
    standalone: false
})
export class CommonRelationMenuComponent {
    editor: TheEditor;

    keywords: string;

    translate = inject(StyxTranslateService);

    @ViewChild('pluginMenu', { read: ThePluginMenuComponent, static: false }) pluginMenu: ThePluginMenuComponent;

    menu = helpers.map(
        [
            relationIdeaMenuItem(this.translate),
            relationTicketMenuItem(this.translate),
            relationWorkItemMenuItem(this.translate),
            relationTestCaseMenuItem(this.translate),
            relationPageMenu(this.translate),
            relationObjectiveMenuItem(this.translate)
        ],
        (item: ThePluginMenuItem) => item.key
    );

    getSearchResult(keywords) {
        setTimeout(() => {
            this.pluginMenu?.updateKeywords(keywords);
        }, 0);
    }
}
