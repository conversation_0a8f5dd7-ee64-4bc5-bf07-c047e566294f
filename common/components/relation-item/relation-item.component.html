@if (relationItemState === RelationItemState.loading) {
  <span contenteditable="false" class="common-placeholder-loading text-mode-placeholder">
    <thy-icon [thyIconName]="relationItemOption.icon"></thy-icon>
  </span>
}
@if (relationItemState === RelationItemState.not_found) {
  <common-empty-content [iconName]="relationItemOption.emptyIcon" [text]="relationItemOption.notFoundText"></common-empty-content>
}

@if (relationItemState === RelationItemState.success) {
  @if (element?.mode === 'card') {
    <common-relation-item-card
      [broadObjectType]="relationItemOption?.broadObjectType"
      [relationItem]="relationItem"
    ></common-relation-item-card>
  } @else {
    <common-relation-item-text
      [editor]="editor"
      [toolbarPopoverRef]="toolbarPopoverRef"
      [isCollapsedAndNonReadonly]="isCollapsedAndNonReadonly"
      [broadObjectType]="relationItemOption?.broadObjectType"
      [relationItem]="relationItem"
    ></common-relation-item-text>
  }
}

<ng-template #toolbar>
  <thy-actions thySize="xxs">
    @if (relationItemState === RelationItemState.success) {
      @if (element?.mode === 'card') {
        <a
          href="javascript:;"
          thyAction
          thyActionIcon="inline"
          styxI18nTracking
          [thyTooltip]="'styx.text' | translate"
          (mousedown)="wrapText($event)"
        ></a>
      }
      @if (element?.mode === 'text') {
        <a
          href="javascript:;"
          thyAction
          styxI18nTracking
          [thyTooltip]="'styx.card' | translate"
          thyActionIcon="float-center"
          (mousedown)="switchCard($event)"
        ></a>
      }

      @if (isAvailableApp) {
        <a
          href="javascript:;"
          thyAction
          thyActionIcon="publish"
          styxI18nTracking
          [thyTooltip]="'styx.openNew' | translate"
          (mousedown)="openRelationItem($event)"
        ></a>
      }
      <thy-divider class="mr-2 ml-1 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
    }
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      (mousedown)="removeNode($event)"
      (mouseenter)="onEnterDelete()"
      (mouseleave)="onLeaveDelete()"
    ></a>
  </thy-actions>
</ng-template>
