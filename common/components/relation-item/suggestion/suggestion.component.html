<div class="relation-list">
  @if (loadingDone) {
    <div class="relation-list-body thy-select-dropdown-options" #relationListBody>
      @if (options.length) {
        <thy-selection-list
          [thyMultiple]="false"
          [disabled]="readonly || disabled"
          [thyBindKeyEventContainer]="editorContainer"
          [thyScrollContainer]="relationListBody"
          thyAutoActiveFirstItem="true"
          [thySpaceKeyEnabled]="false"
          (thySelectionChange)="selectMention($event)"
        >
          <span class="group-name text-truncate" translate="styx.recentBrowsing"></span>
          @for (item of options; track item._id) {
            <thy-list-option class="thy-option-item" [thyValue]="item._id">
              <div class="icon-text icon-text-sm text-truncate d-block">
                <styx-business-object-brand
                  [styxType]="relationItemOption.broadObjectType"
                  [styxEntity]="item"
                  [styxShowIdentifier]="true"
                  [styxFlexible]="true"
                ></styx-business-object-brand>
              </div>
            </thy-list-option>
          }
        </thy-selection-list>
      } @else {
        <thy-empty thyTopAuto styxI18nTracking [thyMessage]="'wiki.search.noResult' | translate" thySize="sm"></thy-empty>
      }
    </div>
  } @else {
    <thy-loading [thyDone]="loadingDone"> </thy-loading>
  }
  <div class="panel-footer" (click)="openSelectDialog()">
    <a thyAction class="flex-grow-1 pl-0" thyTheme="lite" thyIcon="list" href="javascript:;">
      {{ relationItemOption?.moreText }}
    </a>
  </div>
</div>
