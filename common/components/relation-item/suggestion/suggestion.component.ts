import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    ElementRef,
    HostBinding,
    inject,
    Input,
    OnInit
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
    AgileBroadObjectTypes,
    Is,
    ResponseData,
    ShipBroadObjectTypes,
    StyxPivotEntityStore,
    StyxRelationItemInfo,
    TeamsBroadObjectTypes,
    TesthubBroadObjectTypes,
    WikiBroadObjectTypes
} from '@atinc/ngx-styx';
import { RelationItemEditor } from '@wiki/common/plugins/relation-item/relation-item.editor';
import { CommonBroadObjectService } from '@wiki/common/services/broad-object.service';
import { RelationApiService } from '@wiki/common/services/relation-api.service';
import { WikiPluginTypes } from '@wiki/common/types';
import { RelationItemOption } from '@wiki/common/types/relation-types';
import { MentionEditor, THE_EDITOR_POPOVER_REF, TheBaseSuggestion, TheEditor } from '@worktile/theia';
import { BehaviorSubject, of } from 'rxjs';
import { debounceTime, finalize, switchMap, take } from 'rxjs/operators';
import { Editor } from 'slate';

@Component({
    selector: 'common-relation-item-suggestion',
    templateUrl: 'suggestion.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [StyxPivotEntityStore],
    standalone: false
})
export class CommonRelationItemSuggestionComponent extends TheBaseSuggestion implements OnInit {
    @HostBinding('class') className = `relation-item-suggestion`;

    @Input() editor: Editor;

    @Input() principal_id: string;

    @Input() relationItemOption: RelationItemOption;

    @Input() readonly = false;

    @Input() disabled = false;

    @Input() pageId: string;

    value = '';

    options = [];

    keywords$: BehaviorSubject<string> = new BehaviorSubject('');

    loadingDone = false;

    WikiBroadObjectTypes = WikiBroadObjectTypes;

    editorContainer: HTMLElement | ElementRef | string;

    pivotStore = inject(StyxPivotEntityStore);

    constructor(
        public elementRef: ElementRef,
        private cdr: ChangeDetectorRef,
        private relationApiService: RelationApiService,
        private broadObjectService: CommonBroadObjectService,
        private destroyRef: DestroyRef
    ) {
        super();
    }

    ngOnInit() {
        this.editorContainer = TheEditor.toDOMNode(this.editor, this.editor);
        this.subscribeFn();
    }

    subscribeFn() {
        if (!this.readonly) {
            this.keywords$
                .pipe(
                    debounceTime(500),
                    switchMap(keywords => {
                        return this.fetchSuggestionList(keywords).pipe(
                            finalize(() => {
                                this.loadingDone = true;
                                this.cdr.detectChanges();
                            })
                        );
                    }),
                    takeUntilDestroyed(this.destroyRef)
                )
                .subscribe((responseData: ResponseData<StyxRelationItemInfo[], any>) => {
                    this.pivotStore.initializeWithReferences(responseData.value, responseData.references);
                });
        }
        this.pivotStore.entities$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(entities => {
            this.options = entities;
            this.cdr.detectChanges();
        });
    }

    // 插入 # 时调起的搜索
    getSearchResult(keywords: string) {
        this.keywords$.next(keywords);
    }

    fetchSuggestionList(keywords: string) {
        const view =
            keywords === '' ? { category: 'recent_browsed' } : { category: 'all', search: { keywords, scopes: ['identifier', 'title'] } };
        return this.relationApiService.fetchSelectableItems({
            principal_name: 'page',
            principal_id: this.pageId,
            target_name: this.relationItemOption.broadObjectType,
            include_related_items: Is.yes,
            view
        });
    }

    openSelectDialog() {
        const selection = this.editor.selection;
        const commonResolve = (relationItems: StyxRelationItemInfo[]) => {
            this.editor.selection = selection;
            this.selectedHandle(relationItems);
            return of(true);
        };

        switch (this.relationItemOption.broadObjectType) {
            case WikiBroadObjectTypes.page:
                this.broadObjectService.openPageSelection((relationItems: StyxRelationItemInfo[]) => {
                    return commonResolve(relationItems);
                });
                break;
            case AgileBroadObjectTypes.workItem:
                this.broadObjectService.openWorkItemSelection([], (relationItems: StyxRelationItemInfo[]) => {
                    return commonResolve(relationItems);
                });
                break;
            case TesthubBroadObjectTypes.testCase:
                this.broadObjectService.openTestCaseSelection(
                    [],
                    (relationItems: StyxRelationItemInfo[]) => {
                        return commonResolve(relationItems);
                    },
                    { principalId: this.principal_id }
                );
                break;
            case ShipBroadObjectTypes.idea:
            case ShipBroadObjectTypes.ticket:
                this.broadObjectService.openShipSelection(
                    this.relationItemOption.broadObjectType,
                    [],
                    (relationItems: StyxRelationItemInfo[]) => {
                        return commonResolve(relationItems);
                    },
                    { principalId: this.principal_id }
                );
                break;
            case TeamsBroadObjectTypes.objective:
                this.broadObjectService.openObjectiveSelection(
                    [],
                    (relationItems: StyxRelationItemInfo[]) => {
                        return commonResolve(relationItems);
                    },
                    { principalId: this.principal_id }
                );
                break;
        }
    }

    selectMention(selected: any) {
        this.selectedHandle([{ _id: selected.value }]);
    }

    selectedHandle(selected: any[]) {
        const popover = THE_EDITOR_POPOVER_REF.get(this.editor);
        if (!popover?.componentInstance) {
            popover.componentInstance = this.elementRef;
        }
        MentionEditor.deleteTriggerText(this.editor);
        switch (this.relationItemOption.broadObjectType) {
            case WikiBroadObjectTypes.page:
                RelationItemEditor.insertText(this.editor, WikiPluginTypes.relationPage, [selected[0]]);
                break;
            case ShipBroadObjectTypes.idea:
                RelationItemEditor.insertText(this.editor, WikiPluginTypes.relationIdea, selected);
                break;
            case AgileBroadObjectTypes.workItem:
                RelationItemEditor.insertText(this.editor, WikiPluginTypes.relationWorkItem, selected);
                break;
            case TesthubBroadObjectTypes.testCase:
                RelationItemEditor.insertText(this.editor, WikiPluginTypes.relationTestCase, selected);
                break;
            case ShipBroadObjectTypes.ticket:
                RelationItemEditor.insertText(this.editor, WikiPluginTypes.relationTicket, selected);
                break;
            case TeamsBroadObjectTypes.objective:
                RelationItemEditor.insertText(this.editor, WikiPluginTypes.relationObjective, selected);
                break;
        }
        MentionEditor.close(this.editor);
        THE_EDITOR_POPOVER_REF.set(this.editor, null);
    }
}
