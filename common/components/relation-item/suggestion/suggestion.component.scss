@use 'ngx-tethys/styles/variables.scss';
@use '../../../styles/variables.scss' as commonVariables;

.thy-autocomplete-container {
    .thy-select-dropdown-options {
        padding-bottom: 42px;

        .thy-empty-state {
            margin-top: 10px;
            margin-bottom: 20px;
        }
    }

    .panel-footer {
        position: absolute;
        bottom: 1px;
        display: flex;
        width: calc(100% - 2px);
        height: 41px;
        padding: 0 20px;
        border-top: commonVariables.$wiki-border-default;
        border-bottom-left-radius: variables.$border-radius;
        border-bottom-right-radius: variables.$border-radius;
        color: variables.$primary;
        background-color: variables.$bg-panel;
        cursor: pointer;

        &:hover {
            background-color: variables.$gray-80;
        }
    }
}

.relation-item-suggestion {
    .relation-list {
        display: block;
        width: 440px;
        padding-bottom: 42px;
    }
    .relation-list-body {
        max-height: 260px;
        min-height: 60px;
        padding-bottom: 0;
        .thy-selection-list {
            padding: 0;
        }
    }
    .group-name {
        width: 100%;
        height: 30px;
        display: block;
        line-height: 30px;
        color: variables.$gray-600;
        background-color: variables.$gray-80;
        padding-left: 20px;
        padding-right: 20p;
    }
    .panel-footer {
        position: absolute;
        bottom: 1px;
        display: flex;
        width: calc(100% - 2px);
        height: 41px;
        padding: 0 20px;
        border-top: commonVariables.$wiki-border-default;
        border-bottom-left-radius: variables.$border-radius;
        border-bottom-right-radius: variables.$border-radius;
        color: variables.$primary;
        background-color: variables.$bg-panel;
        cursor: pointer;

        &:hover {
            background-color: variables.$gray-80;
        }
    }
}
