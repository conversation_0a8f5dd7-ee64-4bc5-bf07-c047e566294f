@if (relationItem) {
  <styx-business-object-brand
    [styxType]="broadObjectType"
    [styxEntity]="relationItem"
    [styxShowName]="true"
    [styxShowIdentifier]="true"
    [styxFlexible]="true"
  ></styx-business-object-brand>
}
@if (relationItem?.refs) {
  <div class="card-sub-content mt-2 pl-6 d-flex justify-content-between align-self-center">
    <span class="text-truncate d-inline-flex">
      <!-- 状态 -->
      <styx-state [ngClass]="{ 'ml-5': isObjective }" [styxData]="relationItem?.refs?.state_id || relationItem?.refs?.state"></styx-state>

      <!-- 名称：项目、测试库、需求、所属产品、周期 -->
      <thy-tag class="card-pilot-name ml-2 text-truncate d-block" thyShape="pill" thyTheme="fill">
        {{ relationItem?.refs[resource.titleKey]?.name }}
      </thy-tag>

      <!-- 测试用例-用例类型 工单-解决方案 -->
      @if (relationItem?.refs[resource.type]) {
        <thy-tag
          class="card-pilot-name ml-2 text-truncate d-block"
          thyShape="pill"
          thyTheme="fill"
          [thyColor]="resource.hasBgColor ? relationItem?.refs[resource.type].bg_color : 'default'"
        >
          {{ relationItem?.refs[resource.type].text }}
        </thy-tag>
      }

      <!-- 工作项-截止时间、产品需求-计划时间 -->
      @if (resource.timeKey && (relationItem?.due?.date || relationItem?.plan_at)) {
        <thy-tag
          class="ml-2"
          thyShape="pill"
          [thyTheme]="hasDelayDue ? 'weak-fill' : 'fill'"
          [thyColor]="hasDelayDue ? 'danger' : 'default'"
        >
          <thy-icon [class.text-muted]="!hasDelayDue" thyIconName="calendar-check"></thy-icon>
          @if (resource.timeKey === 'due') {
            <span>{{ relationItem?.due?.date | dateAutoFormat }}</span>
          }
          @if (resource.timeKey === 'plan_at') {
            <span>{{ relationItem?.plan_at | thyDatePickerFormat }}</span>
          }
        </thy-tag>
      }

      <!-- 目标-进度 -->
      @if (resource.showRate) {
        <div class="ml-2 d-flex align-items-center">
          <thy-progress [thyMax]="1" thyType="success" [thyValue]="relationItem.rate"></thy-progress>
          <span class="ml-2" styxI18nTracking [thyTooltip]="'styx.completed' | translate">{{ relationItem.rate | percent }} </span>
        </div>
      }
    </span>
    <span class="text-truncate">
      @if (relationItem?.refs?.assignee || relationItem?.refs?.maintenance_uid) {
        <thy-avatar
          thySize="xs"
          class="align-middle"
          [thySrc]="relationItem?.refs?.assignee?.avatar || relationItem?.refs.maintenance_uid?.avatar"
          [thyName]="relationItem?.refs?.assignee?.display_name || relationItem?.refs.maintenance_uid?.display_name"
        ></thy-avatar>
      }
    </span>
  </div>
}
