import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import {
    AgileBroadObjectTypes,
    PropertyDateExpiredPipe,
    ShipBroadObjectTypes,
    TeamsBroadObjectTypes,
    TesthubBroadObjectTypes
} from '@atinc/ngx-styx';
import { RelationItemInfo } from '../../../plugins/relation-item/type';

type ResourceType = {
    titleKey: string;
    timeKey?: string;
    type?: string;
    hasBgColor?: boolean;
    showRate?: boolean;
};

@Component({
    selector: 'common-relation-item-card, [commonRelationWorkItemCard]',
    templateUrl: 'card.component.html',
    host: {
        class: 'common-plugin-card-element d-flex flex-column px-4 py-3 relation-item-card'
    },
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [PropertyDateExpiredPipe],
    standalone: false
})
export class CommonRelationItemCardComponent implements OnInit {
    @Input() relationItem: RelationItemInfo;

    @Input() broadObjectType: string;

    hasDelayDue = false;

    protected resource: ResourceType;

    protected isObjective: boolean;

    protected relationResources: { [key: string]: ResourceType } = {
        [AgileBroadObjectTypes.workItem]: { titleKey: 'project_id', timeKey: 'due' },
        [TesthubBroadObjectTypes.testCase]: { titleKey: 'test_library_id', type: 'type' },
        [ShipBroadObjectTypes.idea]: { titleKey: 'product_id', timeKey: 'plan_at' },
        [ShipBroadObjectTypes.ticket]: { titleKey: 'product_id', type: 'solution', hasBgColor: true },
        [TeamsBroadObjectTypes.objective]: { titleKey: 'period_id', showRate: true }
    };

    constructor(public propertyDateExpiredPipe: PropertyDateExpiredPipe) {}

    ngOnInit() {
        this.isObjective = this.broadObjectType === TeamsBroadObjectTypes.objective;
        this.resource = this.relationResources[this.broadObjectType];
        this.hasDelayDue = this.getHasOverDue();
    }

    getHasOverDue() {
        const data = this.relationItem?.due?.date || this.relationItem?.plan_at?.end;
        return this.resource.timeKey && this.propertyDateExpiredPipe.transform(data as number, this.relationItem?.state_type);
    }
}
