@use '@worktile/theia/styles/variables.scss' as theia;
@use '@worktile/theia/styles/mixins.scss' as mixins;
@use '../../styles/variables.scss' as commonVariables;

.wiki-group-toolbar-item {
    margin-right: 0;

    &.the-toolbar-dropdown-container {
        padding: 0;
    }
    .dropdown-plus-circle-fill {
        color: commonVariables.$wiki-primary;
    }
    .thy-action .thy-icon {
        font-size: 18px;
    }
}
.the-toolbar-dropdown-popover {
    &.group {
        .dropdown {
            max-height: 470px;
            overflow-y: initial;
        }
        .thy-dropdown-menu-item {
            &.disabled {
                pointer-events: none;
            }

            @include mixins.the-toolbar-disabled();
        }
    }
}
