import { ConnectedPosition, Overlay } from '@angular/cdk/overlay';
import { Component, ElementRef, inject, OnDestroy, OnInit } from '@angular/core';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { WIKI_FULLSCREEN } from '@wiki/common/constants/page';
import { FULLSCREEN_TOOLBAR_HIDDEN_TYPES, WikiCommonMenuItemKey, WikiGroupToolbarPluginMenu, WikiPluginTypes } from '@wiki/common/types';
import { verifyAvailableAppByPluginType } from '@wiki/common/util/common';
import {
    DefaultToolbarItem,
    TheBaseToolbarItem,
    TheEditor,
    ThePluginMenuComponent,
    ThePluginMenuItemKey,
    TheQueries,
    ToolbarItem,
    bottomLeftPosition,
    updatePopoverPosition
} from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';

@Component({
    selector: 'wiki-common-group-toolbar-item',
    templateUrl: './group-toolbar.component.html',
    host: {
        class: 'wiki-group-toolbar-item the-toolbar-dropdown-container'
    },
    standalone: false
})
export class WikiCommonGroupToolbarItemComponent extends TheBaseToolbarItem implements OnInit, OnDestroy {
    groupPopoverRef: ThyPopoverRef<any>;

    activeMenu: DefaultToolbarItem | null;

    disableGroup = {};

    fullscreenHiddenTypes = FULLSCREEN_TOOLBAR_HIDDEN_TYPES;

    translate = inject(StyxTranslateService);

    get isGroupPopoverOpen() {
        return this.groupPopoverRef && this.groupPopoverRef.componentInstance;
    }

    constructor(
        private thyPopover: ThyPopover,
        private overlay: Overlay,
        private elementRef: ElementRef
    ) {
        super();
    }

    ngOnInit() {}

    selectionChange(editor: TheEditor): void {
        super.selectionChange(editor);

        if (editor.selection) {
            this.toolbarItem.includes.forEach((menu: ToolbarItem) => {
                this.addDisableToolbars(editor, menu);
                if (menu.includes?.length) {
                    menu.includes.forEach((subMenu: ToolbarItem) => {
                        this.addDisableToolbars(editor, subMenu);
                    });
                }
            });
        }
    }

    addDisableToolbars(editor: TheEditor, item: ToolbarItem) {
        let disabled;
        if (item?.disable) {
            disabled = item?.disable ? item?.disable(editor) : false;
        } else {
            const thePlugin = TheQueries.getPluginByToolbarItem(editor, item);
            disabled = thePlugin && TheQueries.getToolbarItemDisabled(editor, thePlugin.key);
        }
        this.disableGroup[item.key] = disabled ? true : false;
    }

    buildGroupToolbar() {
        const menuItmKeys = {
            [WikiCommonMenuItemKey.collaborativeSpace]: [WikiPluginTypes.relationObjective],
            [WikiCommonMenuItemKey.productManage]: [WikiPluginTypes.relationIdea, WikiPluginTypes.relationTicket],
            [WikiCommonMenuItemKey.projectManage]: [WikiPluginTypes.relationWorkItem, WikiPluginTypes.relationWorkItemList],
            [WikiCommonMenuItemKey.testManage]: [WikiPluginTypes.relationTestCase, WikiPluginTypes.relationTestCaseList],
            [WikiCommonMenuItemKey.insight]: [WikiPluginTypes.relationReport]
        };
        return WikiGroupToolbarPluginMenu(this.translate).filter(menu => {
            const types = menuItmKeys[menu as ThePluginMenuItemKey];
            if (types && !types.every(type => verifyAvailableAppByPluginType(this.translate, this.editor, type))) {
                return false;
            }
            return true;
        });
    }

    openDropdownPopover() {
        const isFullscreenMode = !!(this.elementRef.nativeElement as HTMLElement).closest(`.${WIKI_FULLSCREEN}`);
        let groupToolbarPluginMenu = this.buildGroupToolbar();
        this.groupPopoverRef = this.thyPopover.open(ThePluginMenuComponent, {
            origin: this.elementRef.nativeElement,
            panelClass: [this.toolbarItem?.key, isFullscreenMode ? `${WIKI_FULLSCREEN}-toolbar` : ''],
            placement: 'bottomLeft',
            hasBackdrop: false,
            manualClosure: true,
            insideClosable: false,
            outsideClosable: true,
            offset: 10,
            minWidth: 0,
            initialState: {
                theDisplaySearch: true,
                editor: this.editor,
                thePluginMenu: groupToolbarPluginMenu
            },
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });
        this.createPositionStrategy();
    }

    createPositionStrategy() {
        const bottomPosition: ConnectedPosition = {
            ...bottomLeftPosition,
            offsetY: 8
        };
        const origin = this.elementRef.nativeElement;
        const overlayRef = this.groupPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [bottomPosition]);
    }

    ngOnDestroy() {}
}
