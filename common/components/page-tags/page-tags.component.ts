import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, inject } from '@angular/core';
import { StyxTagApiService, SubAppRootContext, TagInfo, UtilService, WikiBroadObjectTypes } from '@atinc/ngx-styx';
import { API_PREFIX } from '@wiki/app/constants';
import { PageInfo } from '@wiki/app/entities/page-info';
import { SpaceStore } from '@wiki/app/stores/space.store';
import { PageEventBus } from '@wiki/common/services/page-event-bus';
import { PageStore } from '@wiki/common/stores/page.store';

@Component({
    selector: 'wiki-page-tags',
    templateUrl: './page-tags.component.html',
    host: {
        class: 'wiki-page-tags'
    },
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiPageTagsComponent {
    @Input() page: PageInfo;

    @Input() maxTagCount = 5;

    @Input() addable = true;

    @Input() removable = true;

    @Input() clickSelectable = true;

    @Output() tagsChange = new EventEmitter<TagInfo[]>();

    apiPrefix = API_PREFIX;

    broadObjectType = WikiBroadObjectTypes.page;

    subAppRootContext = inject(SubAppRootContext, { optional: true });

    private pageEventBus = inject(PageEventBus);

    private util = inject(UtilService);

    private styxTagApiService = inject(StyxTagApiService);

    private pageStore = inject(PageStore);

    private spaceStore = inject(SpaceStore);

    addTagAction = (tag: TagInfo) => {
        return this.styxTagApiService.addTag(this.apiPrefix, this.broadObjectType, this.page?._id, [tag._id]);
    };

    removeTagAction = (tags: TagInfo[]) => {
        return this.styxTagApiService.removeTags(this.apiPrefix, this.broadObjectType, this.page?._id, tags);
    };

    tagsDataChange(tags: TagInfo[]) {
        this.pageEventBus.emitPageInfo({
            ...this.page,
            tag_ids: tags.map(tag => tag._id),
            refs: {
                ...this.page.refs,
                tags
            }
        });
        this.tagsChange.emit(tags);
    }

    tagClick(tag: TagInfo) {
        if (this.clickSelectable) {
            const identifier = this.page.refs?.pilot?.identifier ?? this.spaceStore.snapshot?.pilot?.identifier;
            const url = this.util.generateTeamUrl(`/wiki/spaces/${identifier}/tags?tags=${tag._id}`);
            window.open(url, '_blank');
        }
    }
}
