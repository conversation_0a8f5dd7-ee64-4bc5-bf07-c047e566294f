@use 'ngx-tethys/styles/bootstrap/variables.scss' as variables;
@use '../../styles/variables.scss' as commonVariables;

.wiki-page-tags {
    display: block;
    padding: 0 commonVariables.$page-content-base-padding 20px;

    .styx-tags-container:not(.styx-tags-container--in-card) {
        margin-bottom: 0;
    }
    .styx-tag-list-container .styx-tag-item,
    .styx-add-button {
        margin-bottom: 0;
    }
}
