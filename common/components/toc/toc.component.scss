@use 'ngx-tethys/styles/variables.scss';
@use '../../styles/variables.scss' as commonVariables;

.wiki-common-toc {
    display: block;
    position: absolute;
    width: commonVariables.$page-content-toc-width;
    right: commonVariables.$anchor-right;
    scrollbar-width: none;
    &::-webkit-scrollbar {
        display: none;
        /* Chrome Safari */
    }
    /* Firefox */
    overflow-y: auto;
    .thy-anchor {
        .thy-anchor-link {
            .thy-anchor-link-title {
                padding: 8px 0;
            }
        }
    }
    &.toc-for-edit {
        &.collpased {
            display: none;
        }

        .thy-anchor {
            .thy-anchor-ink {
                display: none;
            }
        }

        .toc-header-edit {
            display: flex;
            justify-content: space-between;
            border-bottom: commonVariables.$wiki-border-default;

            .toc-icon {
                cursor: pointer;

                &:hover {
                    color: variables.$primary !important;
                }
            }
            .title {
                line-height: 14px;
            }
        }

        .thy-anchor {
            padding-top: 8px;
            .thy-anchor-ink {
                display: none;
            }

            .thy-anchor-link {
                padding-left: 0px;

                &[level='1'] {
                    padding-left: commonVariables.$anchor-link-level-padding;
                }

                &[level='2'] {
                    padding-left: commonVariables.$anchor-link-level-padding * 2;
                }

                &[level='3'] {
                    padding-left: commonVariables.$anchor-link-level-padding * 3;
                }
            }
        }
    }
    &.toc-for-read {
        transition: all 0.2s;
        &.collpased {
            width: commonVariables.$anchor-collapsed-width;
            right: commonVariables.$anchor-right;
            overflow: hidden;

            .thy-anchor-wrapper {
                overflow: initial;
            }

            .title {
                display: none;
            }

            .thy-anchor {
                margin-left: 0.5rem;
            }

            .thy-anchor-link {
                padding-left: 25px !important;

                .thy-anchor-link-title {
                    transition: none;
                    visibility: hidden;
                }
            }
        }

        .toc-header-read {
            margin-bottom: 10px;

            .toc-icon {
                margin-right: 9px;
                cursor: pointer;

                &:hover {
                    color: variables.$primary !important;
                }
            }
        }

        .thy-anchor {
            .thy-anchor-link {
                padding-left: commonVariables.$anchor-link-base-padding;
                &[level='1'] {
                    padding-left: commonVariables.$anchor-link-base-padding + commonVariables.$anchor-link-level-padding;
                }

                &[level='2'] {
                    padding-left: commonVariables.$anchor-link-base-padding + commonVariables.$anchor-link-level-padding * 2;
                }

                &[level='3'] {
                    padding-left: commonVariables.$anchor-link-base-padding + commonVariables.$anchor-link-level-padding * 3;
                }
            }

            .thy-anchor-ink::before {
                background-color: variables.$gray-200;
            }
        }
    }
}
