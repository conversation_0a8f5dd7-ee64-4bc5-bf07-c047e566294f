@if (type === 'read' && headers()?.length > 0) {
  <div class="toc-header-read text-truncate">
    <thy-icon
      styxI18nTracking
      [thyTooltip]="isCollapsed() ? ('styx.expand' | translate) : ('styx.collapse' | translate)"
      thyTooltipPlacement="left"
      (click)="toggleCollapse()"
      class="toc-icon font-size-base text-desc"
      thyIconName="toc"
    ></thy-icon>
    <span class="title">{{ title }}</span>
  </div>
}

@if (type === 'edit') {
  <div class="toc-header-edit text-truncate pb-4">
    <span class="title" translate="wiki.plugin.outline"></span>
    <thy-icon (click)="toggleCollapse()" class="toc-icon font-size-base text-muted" thyIconName="close"></thy-icon>
  </div>
}

<thy-anchor #anchor [thyAffix]="false" [thyContainer]="container" (thyClick)="updateUrl($event)">
  @for (header of headers(); track trackByFn($index, header)) {
    <div>
      @if (!header.disable) {
        <thy-anchor-link
          #anchorLink
          thyHref="{{ '#' + header.key }}"
          [thyTitle]="header.text"
          [attr.level]="header.level"
        ></thy-anchor-link>
      }
    </div>
  }
</thy-anchor>
