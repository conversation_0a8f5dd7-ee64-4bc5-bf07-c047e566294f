import {
    Component,
    OnInit,
    Input,
    ElementRef,
    DestroyRef,
    AfterViewInit,
    ViewChild,
    ViewChildren,
    QueryList,
    NgZone,
    input,
    computed,
    output,
    effect,
    model
} from '@angular/core';
import { UpdateHostClassService } from 'ngx-tethys/core';
import { Editor, Element, Node } from 'slate';
import { TheDocument } from '@wiki/common/custom-types';
import { extractHeading, HeadingType } from '../../util/extract-heading';
import { ThyAnchor, ThyAnchorLink } from 'ngx-tethys/anchor';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { fromEvent, take } from 'rxjs';

@Component({
    selector: 'wiki-common-toc',
    templateUrl: './toc.component.html',
    providers: [UpdateHostClassService],
    standalone: false
})
export class CommonTocComponent implements AfterViewInit {
    hasHeadingAnchor = input(false);

    isCollapsed = model<boolean>();

    value = input<TheDocument>();

    editor = input<Editor>();

    headers = computed(() => {
        if (this.value() && this.editor()) {
            let _headers = extractHeading(this.editor(), this.value());
            const minLevel = Math.min(..._headers.map(heading => heading.level));
            _headers = _headers.map(header => {
                return { ...header, level: header.level - minLevel };
            });
            return _headers;
        }
        return [];
    });

    @Input()
    container: string | HTMLElement;

    @Input()
    title: string;

    @Input()
    type: 'edit' | 'read';

    collapsedChange = output<boolean>();

    headersChange = output<HeadingType[]>();

    @ViewChild('anchor', { static: true }) anchor: ThyAnchor;

    @ViewChildren('anchorLink') anchorLinks: QueryList<ThyAnchorLink>;

    constructor(
        private elementRef: ElementRef<HTMLElement>,
        private updateHostClassService: UpdateHostClassService,
        private destroyRef: DestroyRef,
        private route: ActivatedRoute,
        public ngZone: NgZone
    ) {
        this.updateHostClassService.initializeElement(this.elementRef.nativeElement);
        effect(() => {
            this.headersChange.emit(this.headers());
        });
        effect(() => {
            this.updateHostClass();
        });
    }

    ngAfterViewInit(): void {
        if (this.hasHeadingAnchor()) {
            this.route.fragment.pipe(take(1)).subscribe(fragment => {
                if (fragment) {
                    setTimeout(() => {
                        const anchorLink = this.anchorLinks.find(link => link.thyHref === `#${fragment}`);
                        this.anchor.handleScrollTo(anchorLink);
                    }, 1000);
                }
            });
            fromEvent(document.body, 'scroll')
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => {
                    console.warn(`prevent default scroll by #hash`);
                    window.document.body.scrollTo(0, 0);
                });
        }
    }

    updateUrl(anchorLink: ThyAnchorLink) {
        if (this.hasHeadingAnchor()) {
            // 目前这段代码在 dialog 弹框下会有问题，导致弹框异常关闭
            // 暂时方案是弹框下 hasHeadingAnchor() 的值是 false
            const href = anchorLink.thyHref;
            window.location.hash = href.split('#')[1];
        }
    }

    extractNodeText(node: Element) {
        return Node.string(node);
    }

    toggleCollapse() {
        this.isCollapsed.set(!this.isCollapsed());
        this.collapsedChange.emit(this.isCollapsed());
    }

    updateHostClass() {
        const classMap = {
            'wiki-common-toc': true,
            [`toc-for-${this.type}`]: true,
            collpased: this.isCollapsed(),
            'common-editing-sidebar': this.type === 'edit' ? true : false
        };
        this.updateHostClassService.updateClassByMap(classMap);
    }

    trackByFn(index: number, item: { level: number; text: string; key: string }) {
        return item.key;
    }
}
