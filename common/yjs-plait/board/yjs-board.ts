import { PlaitBoard, PlaitOperation } from '@plait/core';
import * as Y from 'yjs';
import translateSetThemeEvent from '../utils/translate-set-theme-event';
import invariant from 'tiny-invariant';
import { SharedTheme } from '../yjs-plait.type';
import { YjsEditor } from '@worktile/y-slate';

const IS_REMOTE: WeakSet<PlaitBoard> = new WeakSet();
const IS_LOCAL: WeakSet<PlaitBoard> = new WeakSet();

export type YjsBoard = YjsEditor & PlaitBoard & { sharedTheme: SharedTheme };

export const YjsBoard = {
    applyPlaitOperations: (board: YjsBoard, operations: PlaitOperation[]): void => {
        YjsBoard.asLocal(board, () => {
            try {
                board.sharedTheme.doc.transact(() => {
                    operations.map(operation => {
                        if (operation.type === 'set_theme') {
                            board.sharedTheme.set('theme', operation.newProperties);
                        }
                    });
                }, board);
            } catch (error) {
                invariant('applyPlaitOperations', error);
            }
        });
    },

    isRemote: (editor: YjsBoard): boolean => {
        return IS_REMOTE.has(editor);
    },

    asRemote: (board: YjsBoard, fn: () => void): void => {
        const wasRemote = YjsBoard.isRemote(board);
        IS_REMOTE.add(board);

        fn();

        if (!wasRemote) {
            Promise.resolve().then(() => IS_REMOTE.delete(board));
        }
    },

    applyThemeEvents: (board: YjsBoard, events: Y.YEvent<any>[]): void => {
        try {
            events.forEach(event => {
                if (event instanceof Y.YMapEvent) {
                    board.apply(translateSetThemeEvent(board, event));
                }
            });
        } catch (error) {
            invariant('applyYjsEvents', error);
        }
    },

    asLocal: (board: YjsBoard, fn: () => void): void => {
        const wasLocal = YjsBoard.isLocal(board);
        IS_LOCAL.add(board);

        fn();

        if (!wasLocal) {
            IS_LOCAL.delete(board);
        }
    },

    isLocal: (board: YjsBoard): boolean => {
        return IS_LOCAL.has(board);
    }
};
