import invariant from 'tiny-invariant';
import { Awareness } from 'y-protocols/awareness';
import { PlaitBoard, getRectangleByElements, getSelectedElements } from '@plait/core';
import { YjsBoard } from './yjs-board';
import { WebsocketProvider } from '@wiki/common/util/shared/y-websocket';
import { SyncElement } from '@worktile/y-slate';
import * as Y from 'yjs';
import { Injector } from '@angular/core';

export interface CursorBoard extends YjsBoard {
    injector: Injector;
    provider: WebsocketProvider;
    awareness: Awareness;
    sharedType: Y.Array<SyncElement>;
}

export const AWARENESS: WeakMap<CursorBoard, Awareness> = new WeakMap();

export const CursorBoard = {
    awareness(board: CursorBoard): Awareness {
        const awareness = AWARENESS.get(board);
        invariant(awareness, 'CursorBoard without attaches awareness');
        return awareness;
    },

    updateCursor: (board: CursorBoard): void => {
        const selectedElements = getSelectedElements(board);
        const nodes = selectedElements.map(item => {
            const nodeRectangle = getRectangleByElements(board, [item], true);
            return {
                path: PlaitBoard.findPath(board, item),
                nodeRectangle
            };
        });
        try {
            const awareness = CursorBoard.awareness(board);
            awareness.setLocalState({ ...awareness.getLocalState(), nodes });
        } catch (error) {}
    }
};
