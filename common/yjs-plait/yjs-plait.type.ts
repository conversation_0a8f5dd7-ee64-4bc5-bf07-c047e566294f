import { MeInfo } from '@atinc/ngx-styx';
import { Path, PlaitTheme } from '@plait/core';
import { Point } from 'slate';
import * as Y from 'yjs';

export interface AwarenessInfo {
    alphaColor: string;
    color: string;
    user: MeInfo;
}

export interface WikiBoardAwarenessInfo extends AwarenessInfo {
    nodes: {
        points: Point;
        path: Path;
    }[];
}

export type SharedTheme = Y.Map<Partial<PlaitTheme>>;
