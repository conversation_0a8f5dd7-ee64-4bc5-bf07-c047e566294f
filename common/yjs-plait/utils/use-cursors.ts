import { Subject } from 'rxjs';
import { WikiBoardAwarenessInfo } from '../yjs-plait.type';
import { CursorBoard } from '../board/cursor-board';

export const useCursors = (
    board: CursorBoard
): Subject<{
    data: WikiBoardAwarenessInfo[];
}> => {
    let cursor$ = new Subject<{ data: WikiBoardAwarenessInfo[] }>();
    let data: WikiBoardAwarenessInfo[] = [];

    board.awareness.on('update', () => {
        data = Array.from(board.awareness.getStates())
            .filter(([clientId]) => clientId !== board.sharedType.doc?.clientID)
            .map(([, awareness]) => {
                return awareness;
            }) as WikiBoardAwarenessInfo[];
        cursor$.next({
            data
        });
    });
    return cursor$;
};

export default useCursors;
