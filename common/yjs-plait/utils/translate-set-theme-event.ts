import * as Y from 'yjs';
import { PlaitTheme, SetThemeOperation } from '@plait/core';
import { YjsBoard } from '../board/yjs-board';
import { SharedTheme } from '../yjs-plait.type';

/**
 *
 * @param event
 */
export default function translateSetThemeEvent(board: YjsBoard, event: Y.YMapEvent<unknown>): SetThemeOperation {
    const targetSyncElement = event.target as SharedTheme;
    const keyChanges = Array.from(event.changes.keys.entries());

    const newProperties = keyChanges.reduce((result, [key, info]) => {
        result[key] = info.action === 'delete' ? null : targetSyncElement.get(key);
        return result;
    }, {}) as { theme: PlaitTheme };

    return {
        type: 'set_theme',
        newProperties: newProperties.theme,
        properties: board.theme
    };
}
