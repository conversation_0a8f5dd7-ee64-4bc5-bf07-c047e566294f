import { Injectable } from '@angular/core';
import { getPluginOptions, TheEditor } from '@worktile/theia';
import { ThyFullscreen, ThyFullscreenMode } from 'ngx-tethys/fullscreen';
import { Subject } from 'rxjs';
import { filter } from 'rxjs/operators';
import { Editor, Element as SlateElement } from 'slate';
import { WIKI_FULLSCREEN, WIKI_FULLSCREEN_CONTAINER, WIKI_FULLSCREEN_NODE, WIKI_FULLSCREEN_TOOLBAR_HIDDEN } from '../constants/page';
import { FULLSCREEN_EDIT_TYPES } from '../types/editor.types';
import { ActivatedRoute, Router } from '@angular/router';

export enum FullscreenState {
    on = 'on',
    off = 'off'
}

export type FullscreenElement = SlateElement | undefined;

@Injectable({ providedIn: 'root' })
export class FullscreenService {
    public fullscreenChange$ = new Subject<{ editor: TheEditor; element: FullscreenElement; state: FullscreenState }>();

    private sendNotification(content: { editor: TheEditor; element: FullscreenElement; state: FullscreenState }) {
        this.fullscreenChange$.next(content);
    }

    public subscribe(
        channel: FullscreenElement,
        handle: (value: { editor: TheEditor; element: FullscreenElement; state: FullscreenState }, element?: HTMLElement) => void
    ) {
        return this.fullscreenChange$
            .pipe(
                filter((content: { editor: TheEditor; element: FullscreenElement; state: FullscreenState }) => {
                    if (!channel) {
                        return true;
                    }
                    const { editor, element } = content;
                    const channelElement = TheEditor.toDOMNode(editor, channel);
                    const contentElement = TheEditor.toDOMNode(editor, element);
                    return channelElement === contentElement;
                })
            )
            .subscribe(content => handle(content));
    }

    setFullscreen(editor: Editor, event: MouseEvent, element: SlateElement, editorToolbarHidden?: boolean) {
        event?.preventDefault();
        const option = getPluginOptions(editor, element.type);
        option.showFullscreen = false;

        const fullscreenDomNode = TheEditor.toDOMNode(editor, element);
        fullscreenDomNode.classList.add(WIKI_FULLSCREEN_NODE);

        const fullscreenContainer = fullscreenDomNode.closest(`.${WIKI_FULLSCREEN_CONTAINER}`) as HTMLElement;
        fullscreenContainer.classList.add(WIKI_FULLSCREEN);
        editorToolbarHidden && fullscreenContainer.classList.add(WIKI_FULLSCREEN_TOOLBAR_HIDDEN);

        this.setContentEditable(fullscreenContainer as HTMLElement, true);

        const thyFullscreen = editor.injector.get(ThyFullscreen);
        const ref = thyFullscreen.launch({
            target: fullscreenContainer,
            mode: ThyFullscreenMode.emulated
        });
        ref.afterExited().subscribe(() => {
            this.exitFullscreen(editor, fullscreenContainer);
        });
        this.sendNotification({ editor, element, state: FullscreenState.on });
    }

    exitFullscreen(editor: Editor, fullscreenContainer: HTMLElement, afterExited = false) {
        const router = editor.injector.get(Router);
        const route = editor.injector.get(ActivatedRoute);
        const elementId = route.snapshot.queryParamMap.get('inner');
        if (elementId) {
            router.navigate([]);
        }
        fullscreenContainer?.classList.remove(WIKI_FULLSCREEN);
        this.setContentEditable(fullscreenContainer as HTMLElement, false);
        const fullscreenDomNode = fullscreenContainer.querySelector(`.${WIKI_FULLSCREEN_NODE}`);
        fullscreenDomNode?.classList.remove(WIKI_FULLSCREEN_NODE);
        fullscreenContainer?.classList.remove(WIKI_FULLSCREEN_TOOLBAR_HIDDEN);
        let fullscreenNode: FullscreenElement;
        if (fullscreenDomNode) {
            fullscreenNode = TheEditor.toSlateNode(editor, fullscreenDomNode) as SlateElement;
            const option = getPluginOptions(editor, fullscreenNode.type);
            option.showFullscreen = true;
        } else {
            // 处理协同编辑下全屏元素被删除后，无法正确设置 option 的情况
            const fullscreenEditType = FULLSCREEN_EDIT_TYPES;
            fullscreenEditType.forEach(item => {
                const option = getPluginOptions(editor, item);
                option.showFullscreen = true;
            });
        }
        if (!afterExited) {
            const thyFullscreen = editor.injector.get(ThyFullscreen);
            if (thyFullscreen) {
                thyFullscreen.exit();
            }
        }
        this.sendNotification({ editor, element: fullscreenNode, state: FullscreenState.off });
    }

    setContentEditable(fullscreenContainer: HTMLElement, addContentEditableFalse: boolean) {
        if (addContentEditableFalse) {
            const leftCards = fullscreenContainer.querySelectorAll('.card-left');
            const rightCards = fullscreenContainer.querySelectorAll('.card-right');
            Array.from([...leftCards, ...rightCards]).forEach(item => {
                item.setAttribute('contentEditable', 'false');
            });
        } else {
            const leftCards = fullscreenContainer.querySelectorAll('.card-left');
            const rightCards = fullscreenContainer.querySelectorAll('.card-right');
            Array.from([...leftCards, ...rightCards]).forEach(item => {
                item.removeAttribute('contentEditable');
            });
        }
    }
}
