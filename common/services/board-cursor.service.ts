import { ComponentRef, Injectable, Renderer2 } from '@angular/core';
import { Path, PlaitBoard, PlaitElement, PlaitNode, createForeignObject, createG } from '@plait/core';
import { getForeignRectangleByNode } from '../util/shared/shared';
import { WikiCommonSharedCaretComponent } from '../components/caret/caret.component';
import { WikiBoardAwarenessInfo } from '../yjs-plait/yjs-plait.type';
import { AngularBoard } from '@plait/angular-board';

export const WIKI_BOARD_CURSOR = 'wiki-board-cursor';

@Injectable()
export class BoardCursorService {
    componentRefs: ComponentRef<WikiCommonSharedCaretComponent>[] = [];

    constructor(private render2: Renderer2) {}

    render(board: PlaitBoard, path: Path, value: WikiBoardAwarenessInfo) {
        try {
            const element = PlaitNode.get(board, path);
            if (element) {
                const foreignRectangle = getForeignRectangleByNode(board, element);
                const foreignObject = createForeignObject(
                    foreignRectangle.x,
                    foreignRectangle.y,
                    foreignRectangle.width,
                    foreignRectangle.height
                );
                const g = createG();
                g.append(foreignObject);
                this.render2.addClass(g, WIKI_BOARD_CURSOR);
                const { componentRef } = (board as unknown as AngularBoard).renderComponent(WikiCommonSharedCaretComponent, foreignObject, {
                    decorate: value
                });
                this.componentRefs.push(componentRef);
                PlaitElement.getElementG(element).append(g);
            }
        } catch (error) {}
    }

    removeCursors() {
        const cursors = document.querySelectorAll(`.${WIKI_BOARD_CURSOR}`);
        cursors.forEach(item => {
            item.remove();
        });
        this.componentRefs.map(componentRef => {
            componentRef.destroy();
        });
        this.componentRefs = [];
    }
}
