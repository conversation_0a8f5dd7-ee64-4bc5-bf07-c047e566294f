import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { API_PREFIX } from '@wiki/app/constants';
import { Id } from 'ngx-tethys/types';

@Injectable({
    providedIn: 'root'
})
export class FileApiService {
    constructor(private http: HttpClient) {}

    /**
     * 添加下载记录
     * @param pageId 页面 id
     * @param attachmentId 附件 id
     * @param version_id 版本 id
     * @returns
     */
    addAttachmentDownloadLog(pageId: Id, attachmentId: Id, version_id: string) {
        return this.http.post(`${API_PREFIX}/pages/${pageId}/attachments/${attachmentId}/download-log`, { version_id });
    }
}
