import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Is, StyxRelationItemInfo, StyxRelationParamsInfo, WikiBroadObjectTypes } from '@atinc/ngx-styx';
import { ResponseData } from '@atinc/ngx-styx/core';
import { API_LADON_PREFIX, API_PREFIX } from '@wiki/app/constants';
import { Id } from 'ngx-tethys/types';
import { map } from 'rxjs/operators';
import { RelationParamsInfo } from '../interface/relation';

@Injectable({
    providedIn: 'root'
})
export class RelationApiService {
    constructor(private http: HttpClient) {}

    // include_related_items: Is; 临时加上，后期修改业务组件库
    fetchSelectableItems(param: StyxRelationParamsInfo & { include_related_items?: Is; pi?: number; ps?: number }) {
        return this.http
            .post<ResponseData<StyxRelationItemInfo[]>>(`${API_LADON_PREFIX}/relation/addable-items`, {
                principal_id: param.principal_id,
                principal_name: param.principal_name,
                target_name: param.target_name,
                mode: param.mode,
                view: { ...param.view, pi: param.pi, ps: param.ps },
                include_related_items: param.include_related_items
            });
    }

    fetchRelatedItems(fetchParams: RelationParamsInfo<string>) {
        let params = `principal_name=${fetchParams.principal_name}&principal_id=${fetchParams.principal_id}&target_name=${
            fetchParams.target_name || WikiBroadObjectTypes.page
        }`;
        if (fetchParams?.team_id) {
            params = params + `&team_id=${fetchParams.team_id}`;
        }
        if (fetchParams?.version_id) {
            params = params + `&version_id=${fetchParams.version_id}`;
        }
        return this.http.get(`${API_LADON_PREFIX}/relation/related-items?${params}`);
    }

    /**
     *
     * @param 根据 broadObjectType 获取相应的数据，参数为以 ',' 分割的 ids
     * @returns
     */
    fetchRelationItem(broadObjectType: string, itemIds: string, teamId?: Id) {
        let params = `item_ids=${itemIds}`;
        if (teamId) {
            params = params + `&team_id=${teamId}`;
        }
        return this.http.get(`${API_PREFIX}/relation/${broadObjectType}?${params}`);
    }

    /**
     * 添加关联
     * @param principalObjectId
     * @param targetId
     * @param principalObjectType
     * @param targetType
     * @returns
     */
    addRelation(principalObjectId: Id, targetId: Id, principalObjectType: string, targetType: string = WikiBroadObjectTypes.page) {
        return this.http.post(`${API_LADON_PREFIX}/relation`, {
            target_ids: [targetId],
            target_name: targetType,
            principal_id: principalObjectId,
            principal_name: principalObjectType
        });
    }

    /**
     * 解除关联
     * @param principalObjectId
     * @param targetId
     * @param principalObjectType
     * @param targetType
     * @returns
     */
    delRelationPage(principalObjectId: Id, targetId: Id, principalObjectType: string, targetType: string = WikiBroadObjectTypes.page) {
        return this.http.delete(`${API_LADON_PREFIX}/relation`, {
            body: {
                principal_id: principalObjectId,
                principal_name: principalObjectType,
                target_id: targetId,
                target_name: targetType
            }
        });
    }
}
