import { Observable } from 'rxjs';
import { InjectionToken } from '@angular/core';
import { RelationWorkItemInfo, RelationWorkItems } from '../../plugins/relation-work-item/type';
export const WIKI_RELATION_WORK_ITEM_CONTEXT = new InjectionToken<any>('WIKI_WORK_ITEM_CONTEXT');

export interface RelationWorkItemContext {
    fetchRecentWorkItems(): Observable<any[]>;

    searchWorkItems(keywords: string): Observable<RelationWorkItemInfo[]>;

    getWorkItemRoute(workItemId: string);

    getWorkItemById(id: string);

    getWorkItemReferences(): RelationWorkItems;

    addWorkItemReference(workItems: RelationWorkItemInfo[]);
}
