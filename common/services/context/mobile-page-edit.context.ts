import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { AppRootContext, AttachmentEntity, GlobalUsersStore, Id, ResponseData } from '@atinc/ngx-styx';
import { PageExtensionData, PageExtensionType } from '@wiki/app/entities/page-extendsion';
import { PageApiService } from '@wiki/app/services';
import { ThyPopover } from 'ngx-tethys/popover';
import { Observable, map, of } from 'rxjs';
import { AttachmentScope, PageAttachmentEntity } from '../../interface/base-file';
import { TheExtensionMode } from '../../interface/extension-mode';
import { PageStore } from '../../stores/page.store';
import { AttachmentApiService } from '../attachment-api.service';
import { AttachmentContext } from './attachment-context';

@Injectable()
export class MobilePageEditContext implements AttachmentContext {
    readonly: boolean;

    constructor(
        public pageStore: PageStore,
        public router: Router,
        public globalUsersStore: GlobalUsersStore,
        public thyPopover: ThyPopover,
        public appRootContext: AppRootContext,
        public attachmentApiService: AttachmentApiService,
        private pageApiService: PageApiService
    ) {}

    attachmentOriginId() {
        return this.pageStore.snapshot.page._id;
    }

    getPageAttachments() {
        const pageId = this.pageStore.snapshot.page._id;
        return this.attachmentApiService.fetchAttachments(pageId).pipe(
            map((data: ResponseData<any[]>) => {
                return data.value;
            })
        );
    }

    getAttachments(): AttachmentEntity[] {
        return this.pageStore.snapshot.page.attachments;
    }

    getAttachmentById(_id: Id): AttachmentEntity {
        return this.pageStore.snapshot.page.attachments.find(attachment => attachment._id === _id);
    }

    addAttachment(attachment: AttachmentEntity, isPure = false, attachmentScope?: AttachmentScope): Observable<AttachmentEntity> {
        if (isPure) {
            this.pageStore.pureAddAttachment(attachment);
            return of(null);
        }
        return this.pageStore.addAttachment(this.pageStore.snapshot.page._id, attachment, attachmentScope);
    }

    modifyAttachmentName(id: Id, title: string): Observable<any> {
        return this.pageStore.modifyAttachmentName(this.pageStore.snapshot.page._id, id, title);
    }

    attachmentChange(attachmentId: Id): Observable<PageAttachmentEntity> {
        return of(null);
    }

    savePageExtension(
        pageId: string,
        value: { key: string; data: PageExtensionData; type: PageExtensionType },
        mode?: TheExtensionMode
    ): Observable<any> {
        return this.pageApiService.savePageExtension(pageId, value, mode);
    }

    getMentionReferenceData() {
        return this.pageStore.snapshot.members;
    }

    getMentionData() {
        return this.globalUsersStore.snapshot.teamUsers;
    }

    getPagePermissions() {
        return this.pageStore.snapshot.page.permissions;
    }
}
