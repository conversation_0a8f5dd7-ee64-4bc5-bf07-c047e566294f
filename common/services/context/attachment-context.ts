import { AttachmentEntity, Id } from '@atinc/ngx-styx';
import { Observable } from 'rxjs';
import { AttachmentScope, PageAttachmentEntity } from '../../interface/base-file';

export interface AttachmentContext {
    readonly: boolean;

    getPageAttachments(): Observable<AttachmentEntity[]>;

    getAttachmentById(id: Id): AttachmentEntity;

    getAttachments(): AttachmentEntity[];

    addAttachment(attachment: AttachmentEntity, isPure?: boolean, attachmentScope?: AttachmentScope): Observable<AttachmentEntity>;

    modifyAttachmentName(id: Id, title: string): Observable<AttachmentEntity>;

    attachmentOriginId(): Id;

    getPagePermissions(): string;

    attachmentChange(attachmentId: Id): Observable<PageAttachmentEntity>;
}
