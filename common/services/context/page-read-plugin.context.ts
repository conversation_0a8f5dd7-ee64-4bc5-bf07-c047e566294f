import { ComponentType } from '@angular/cdk/portal';
import { Injectable, Optional } from '@angular/core';
import { Router } from '@angular/router';
import { AppRootContext, AttachmentEntity, GlobalUsersStore, Id, ResponseData } from '@atinc/ngx-styx';
import { PageInfo, SpaceInfo } from '@wiki/app/entities';
import { PageExtensionData, PageExtensionInfo, PageExtensionType } from '@wiki/app/entities/page-extendsion';
import { PageApiService } from '@wiki/app/services';
import { getDomainUrl } from '@wiki/app/util/common';
import { ThyPopover } from 'ngx-tethys/popover';
import { ShareSpaceStore } from 'outside/src/app/stores/share-space.store';
import { Observable, of } from 'rxjs';
import { Discussion } from '../../custom-types';
import { AttachmentInfo, PageAttachmentEntity, TheExtensionMode } from '../../interface';
import { WikiCommonEditorAttachmentComponent } from '../../plugins/attachment';
import { RelationPageInfo, RelationPageRoute } from '../../plugins/relation-page';
import { RelationWorkItemInfo, RelationWorkItems } from '../../plugins/relation-work-item';
import { PageStore } from '../../stores/page.store';
import { RelationOpenType } from '../../types';
import { AttachmentContext } from './attachment-context';
import { WikiPluginContext } from './plugin.context';

@Injectable()
export class PageReadThePluginContext extends WikiPluginContext implements AttachmentContext {
    workItemReferences: RelationWorkItems;

    constructor(
        public pageStore: PageStore,
        @Optional() public shareSpaceStore: ShareSpaceStore,
        public router: Router,
        public globalUsersStore: GlobalUsersStore,
        public thyPopover: ThyPopover,
        public appRootContext: AppRootContext,
        private pageApiService: PageApiService
    ) {
        super();
    }

    getPageId(): string {
        return this.pageStore.snapshot.page._id;
    }

    getSpaceId(): string {
        return this.pageStore.snapshot.page.space_id;
    }

    getPageAttachments(): Observable<AttachmentEntity[]> {
        return of(undefined);
    }

    setAttachments(attachmentInfos: AttachmentInfo[] | []): void {
        throw new Error('Method not implemented.');
    }

    attachmentOriginId() {
        return this.pageStore.snapshot.page._id;
    }

    getAttachments(): AttachmentEntity[] {
        return this.pageStore.snapshot.page.attachments;
    }

    addAttachment(attachment: AttachmentEntity, isPure = false): Observable<AttachmentEntity> {
        throw new Error('Method not implemented.');
    }

    modifyAttachmentName(id: Id, title: string): Observable<any> {
        throw new Error('Method not implemented.');
    }

    attachmentChange(attachmentId: Id): Observable<PageAttachmentEntity> {
        return of(null);
    }

    selectRelationPage(): Observable<RelationPageInfo> {
        throw new Error('Method not implemented.');
    }

    getRelationPageRoute(spaceId: string, pageId: string): RelationPageRoute {
        if (this.shareSpaceStore) {
            const currentSpace = this.shareSpaceStore.snapshot.spaceInfo;
            if (currentSpace && currentSpace._id === spaceId) {
                return {
                    url: `/spaces/${currentSpace.shortId}/pages/${pageId}`,
                    open_type: RelationOpenType.SELF
                };
            }
        }
        const isExport = this.router.url.includes('export');
        if (isExport) {
            return {
                url: `${getDomainUrl(
                    this.appRootContext.globalInfo.config.baseUrlFormat,
                    this.appRootContext.team.domain
                )}/wiki/spaces/${spaceId}/pages/${pageId}`
            };
        }
        return {
            url: ''
        };
    }

    getAttachmentComponent(): ComponentType<any> {
        return WikiCommonEditorAttachmentComponent;
    }

    getAttachmentById(_id: Id): AttachmentEntity {
        return this.pageStore.snapshot.page.attachments.find(attachment => attachment._id === _id);
    }

    openAttachmentList(editor: any): void {
        throw new Error('Method not implemented.');
    }

    getDiscussions() {
        return of([]);
    }

    openDiscussionList(target: HTMLElement, discussionData: Discussion) {
        throw new Error('Method not implemented.');
    }

    getPages(): PageInfo[] {
        throw new Error('Method not implemented.');
    }

    fetchPageExtension(
        pageId: string,
        extensionId: string,
        mode?: TheExtensionMode
    ): Observable<
        ResponseData<
            PageExtensionInfo,
            {
                pages?: PageInfo[];
                space?: SpaceInfo;
            }
        >
    > {
        return this.pageApiService.fetchPageExtension(pageId, extensionId, mode);
    }

    savePageExtension(
        pageId: string,
        data: { key: string; data: PageExtensionData; type: PageExtensionType },
        mode?: TheExtensionMode
    ): Observable<any> {
        throw new Error('Method not implemented.');
    }

    getMentionReferenceData() {
        return this.pageStore.snapshot.members;
    }

    getMentionData() {
        return this.globalUsersStore.snapshot.teamUsers;
    }

    getSearchedData(value: string) {
        return null;
    }

    // workItem
    fetchRecentWorkItems(): Observable<any[]> {
        throw new Error('Method not implemented.');
    }

    searchWorkItems(keywords: string): Observable<RelationWorkItemInfo[]> {
        throw new Error('Method not implemented.');
    }

    getWorkItemRoute(workItemId: string) {
        const isExport = this.router.url.includes('export');
        if (isExport) {
            const domain = getDomainUrl(this.appRootContext.globalInfo.config.baseUrlFormat, this.appRootContext.team.domain);
            return {
                url: `${domain}/agile/items/${workItemId}`
            };
        }
        return {
            url: ''
        };
    }

    getPagePermissions() {
        return this.pageStore.snapshot.page.permissions;
    }
}
