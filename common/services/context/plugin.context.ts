import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ResponseData } from '@atinc/ngx-styx';
import {
    PageExtensionData,
    PageExtensionInfo,
    PageExtensionType,
    PageInfo,
    PageSelectionOption,
    PageViewPort,
    SpaceInfo
} from '@wiki/app/entities';
import { Observable, Subject } from 'rxjs';
import { Editor } from 'slate';
import { Discussion } from '../../custom-types';
import { TheExtensionMode } from '../../interface';
import { RelationPageRoute } from '../../plugins/relation-page';

@Injectable()
export abstract class WikiPluginContext implements OnDestroy {
    maxView$ = new Subject<PageViewPort>();

    readonly: boolean;

    abstract getSpaceId(): string;

    abstract getPageId(): string;

    abstract getPages(): PageInfo[];

    abstract fetchPageExtension(
        pageId: string,
        extensionId: string,
        mode?: TheExtensionMode
    ): Observable<
        ResponseData<
            PageExtensionInfo,
            {
                pages?: PageInfo[];
                space?: SpaceInfo;
            }
        >
    >;

    abstract savePageExtension(
        pageId: string,
        data: { key: string; data: PageExtensionData; type: PageExtensionType },
        mode?: TheExtensionMode
    ): Observable<any>;

    abstract openAttachmentList(editor: Editor): void;

    abstract selectRelationPage(option: PageSelectionOption);

    abstract getRelationPageRoute(spaceId: string, pageId: string): RelationPageRoute;

    abstract openDiscussionList(target: HTMLElement, leaf: Discussion);

    abstract getDiscussions(): Observable<Discussion[]>;

    setReadonly(value: boolean) {
        this.readonly = value;
    }

    setMaxView(port: PageViewPort) {
        this.maxView$.next(port);
    }

    ngOnDestroy() {
        this.maxView$?.complete();
    }
}
