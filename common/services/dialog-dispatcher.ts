import { Injectable } from '@angular/core';
import { AttachmentEntity, StyxAttachmentUpdatable, Id } from '@atinc/ngx-styx';

@Injectable()
export class DialogDispatcher implements StyxAttachmentUpdatable {
    constructor() {}

    updateDialogAttachments: (attachments: AttachmentEntity[]) => void = () => {};

    pureUpdateAttachments(id: Id, attachments: AttachmentEntity[]) {
        this.updateDialogAttachments(attachments);
    }
}
