import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { API_PREFIX } from '@wiki/app/constants';
import { AttachmentScope } from '../interface/base-file';

@Injectable({
    providedIn: 'root'
})
export class AttachmentApiService {
    constructor(private http: HttpClient) {}

    fetchAttachments(pageId: string) {
        return this.http.get(`${API_PREFIX}/pages/${pageId}/attachments`);
    }

    addAttachment(pageId: string, attachmentId: string, attachmentScope?: AttachmentScope) {
        return this.http.post(`${API_PREFIX}/pages/${pageId}/attachment`, {
            attachment_id: attachmentId,
            attachment_scope: attachmentScope
        });
    }

    modifyAttachmentName(pageId: string, attachmentId: string, title: string) {
        return this.http.put(`${API_PREFIX}/pages/${pageId}/attachments/${attachmentId}/title`, { title: title });
    }

    deleteAttachments(pageId: string, attachmentIds: string[]) {
        return this.http.delete(`${API_PREFIX}/pages/${pageId}/attachments/${attachmentIds.toString()}`);
    }
}
