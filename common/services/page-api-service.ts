import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { API_PREFIX } from '@wiki/app/constants';
import { DraftInfo, PageViewPort } from '@wiki/app/entities/page-info';

@Injectable({
    providedIn: 'root'
})
export class CommonPageApiService {
    constructor(private http: HttpClient) {}

    addPage(spaceId: string, page: DraftInfo) {
        return this.http.post(`${API_PREFIX}/spaces/${spaceId}/pages`, page);
    }

    fetchPageRelations(spaceId: string, pageId: string) {
        return this.http.get(`${API_PREFIX}/pages/${pageId}/relations`);
    }

    fetchCollaborationConnectInfo(pageId: string) {
        return this.http.get(`${API_PREFIX}/secret/pages/${pageId}/collaboration/connect-info`);
    }

    updatePageViewPort(pageId: string, viewport: PageViewPort) {
        return this.http.put(`${API_PREFIX}/pages/${pageId}/viewport`, { viewport });
    }

    updateStencilViewPort(stencilId: string, viewport: PageViewPort) {
        return this.http.put(`${API_PREFIX}/stencils/${stencilId}/viewport`, { viewport });
    }

    updatePage(
        pageId: string,
        page: {
            name?: string;
            emoji_icon?: string;
        }
    ) {
        return this.http.put(`${API_PREFIX}/pages/${pageId}`, page);
    }
}
