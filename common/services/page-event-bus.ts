import { Injectable } from '@angular/core';
import { Id, helpers } from '@atinc/ngx-styx';
import { Action, Store } from '@tethys/store';
import { DraftInfo, PageInfo } from '@wiki/app/entities';
import { filter, map, skip } from 'rxjs/operators';
import { PageRelationReference, WikiPageContent } from '../interface';

export interface PageBusData {
    source: 'page-info' | 'page-document';
    data: PageInfo | PageDocumentData;
}

@Injectable({
    providedIn: 'root'
})
export class PageEventBus extends Store<PageBusData> {
    constructor() {
        super({});
    }

    @Action()
    emitPageInfo(page: Partial<DraftInfo>) {
        this.update({ source: 'page-info', data: page });
    }

    @Action()
    emitPageDocument(data: PageDocumentData) {
        this.update({
            source: 'page-document',
            data
        });
    }

    onPageInfoChange$() {
        return this.select$(state => state.data).pipe(
            skip(1),
            filter(state => this.snapshot.source === 'page-info')
        );
    }

    onPageDocumentChange$() {
        return this.select$(state => state.data).pipe(
            skip(1),
            filter(state => this.snapshot.source === 'page-document'),
            map((data: PageDocumentData) => {
                return {
                    ...data,
                    document: helpers.cloneDeep(data.document)
                };
            })
        );
    }
}

export interface PageDocumentData extends PageRelationReference {
    document: WikiPageContent;
    pageId: Id;
    shortId?: Id;
    isPublish?: boolean;
}
