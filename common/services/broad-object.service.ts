import { Injectable } from '@angular/core';
import {
    AgileBroadObjectTypes,
    ApplicationType,
    InsightBroadObjectTypes,
    Is,
    ShipBroadObjectTypes,
    StyxBroadObjectDispatcher,
    StyxRelationItemInfo,
    TeamsBroadObjectTypes,
    TesthubBroadObjectTypes,
    WikiBroadObjectTypes
} from '@atinc/ngx-styx';
import { Observable } from 'rxjs';
import { RelationObjectiveInfo } from '../plugins/relation-objective/type';
import { RelationTestCaseInfo } from '../plugins/relation-test-case/type';
import { RelationTicketInfo } from '../plugins/relation-ticket/type';
import { WorkItemInfo } from '../types/relation-types';
import { RelationPageInfo } from '../plugins/relation-page';

@Injectable({
    providedIn: 'root'
})
export class CommonBroadObjectService {
    constructor(private styxBroadObjectDispatcher: StyxBroadObjectDispatcher) {}

    openWorkItemSelection(selectedItems: WorkItemInfo[] = [], saveHandle: (selected: StyxRelationItemInfo[]) => Observable<boolean>) {
        this.styxBroadObjectDispatcher.dispatchOpenBroadObjectRelationSelectionEvent(
            {
                type: AgileBroadObjectTypes.workItem,
                application: ApplicationType.agile
            },
            {
                save$: (result: { workItems: StyxRelationItemInfo[] }) => {
                    return saveHandle(result.workItems);
                },
                selectedWorkItems: selectedItems,
                multiple: true,
                projectSelectable: true,
                stateSelectable: true,
                workItemTypeSelectable: true,
                hasRecentBrowsed: true,
                restoreFocus: false
            }
        );
    }

    opeReportSelection(saveHandle: (selected: StyxRelationItemInfo[]) => Observable<boolean>) {
        this.styxBroadObjectDispatcher.dispatchOpenBroadObjectRelationSelectionEvent(
            {
                type: InsightBroadObjectTypes.insightReport,
                application: ApplicationType.insight
            },
            {
                save$: (result: { reports: StyxRelationItemInfo[] }) => {
                    return saveHandle(result.reports);
                }
            }
        );
    }

    openPageSelection(
        saveHandle: (selected: StyxRelationItemInfo[]) => Observable<boolean>,
        selectMultiple: boolean = false,
        selectedItems: RelationPageInfo[] = []
    ) {
        this.styxBroadObjectDispatcher.dispatchOpenBroadObjectRelationSelectionEvent(
            {
                type: WikiBroadObjectTypes.page,
                application: ApplicationType.wiki
            },
            {
                selectedItems,
                confirmHandle: (selectedPages: StyxRelationItemInfo[]) => {
                    return saveHandle(selectedPages);
                },
                selectMultiple
            }
        );
    }

    openTestCaseSelection(
        selectedItems: RelationTestCaseInfo[] = [],
        saveHandle: (selected: StyxRelationItemInfo[]) => Observable<boolean>,
        options?: { principalId: string }
    ) {
        this.styxBroadObjectDispatcher.dispatchOpenBroadObjectRelationSelectionEvent(
            {
                type: TesthubBroadObjectTypes.testCase,
                application: ApplicationType.testhub
            },
            {
                principalId: options?.principalId,
                principalName: WikiBroadObjectTypes.page,
                selectedItems: selectedItems,
                includeRelatedItems: Is.yes,
                save$: (event: { testCases: StyxRelationItemInfo[] }) => {
                    return saveHandle(event.testCases);
                }
            }
        );
    }

    openShipSelection(
        type: ShipBroadObjectTypes,
        selectedItems: RelationTicketInfo[] = [],
        saveHandle: (selected: StyxRelationItemInfo[]) => Observable<boolean>,
        options?: { principalId: string }
    ) {
        this.styxBroadObjectDispatcher.dispatchOpenBroadObjectRelationSelectionEvent(
            {
                type,
                application: ApplicationType.ship
            },
            {
                selectedItems: selectedItems,
                principalId: options?.principalId,
                principalName: WikiBroadObjectTypes.page,
                includeRelatedItems: Is.yes,
                restoreFocus: false,
                selectionConfirm: (ticket: StyxRelationItemInfo[]) => {
                    return saveHandle(ticket);
                }
            }
        );
    }

    openObjectiveSelection(
        selectedItems: RelationObjectiveInfo[] = [],
        saveHandle: (selected: StyxRelationItemInfo[]) => Observable<boolean>,
        options?: { principalId: string }
    ) {
        this.styxBroadObjectDispatcher.dispatchOpenBroadObjectRelationSelectionEvent(
            {
                type: TeamsBroadObjectTypes.objective,
                application: ApplicationType.teams
            },
            {
                principalId: options?.principalId,
                principalName: WikiBroadObjectTypes.page,
                selectedItems: selectedItems,
                includeRelatedItems: Is.yes,
                restoreFocus: false,
                save$: (event: { objectives: StyxRelationItemInfo[] }) => {
                    return saveHandle(event.objectives);
                }
            }
        );
    }
}
