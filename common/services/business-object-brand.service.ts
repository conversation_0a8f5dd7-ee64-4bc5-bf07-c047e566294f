import { Injectable, ComponentRef, createComponent, ApplicationRef, Injector, Type, signal, Signal, inject } from '@angular/core';
import { StyxBusinessBrandComponent } from '@atinc/ngx-styx';
import { RelationItemInfo } from '../plugins/relation-item/type';
import { ThyIconRegistry } from 'ngx-tethys/icon';
import { take } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class SvgService {
    private iconRegistry = inject(ThyIconRegistry);

    constructor(
        private appRef: ApplicationRef,
        private injector: Injector
    ) {}

    /**
     * 获取 business-object-brand 组件的 SVG 数据
     * @param broadObjectType 对象类型
     * @param entity 实体数据
     * @returns Promise<{
     *  icon: string | null;
     *  styxIconType: string | null;
     *  iconColor: string | null;
     *  svgString: string | null;
     * }> SVG 字符串的对象
     */
    async getSvgDataByRelation(
        broadObjectType: string,
        entity: RelationItemInfo
    ): Promise<{
        icon: string | null;
        styxIconType: string | null;
        iconColor: string | null;
        svgString: string | null;
    }> {
        let componentRef: ComponentRef<StyxBusinessBrandComponent>;
        let result = {
            icon: null,
            styxIconType: null,
            iconColor: null,
            svgString: null
        };

        // 创建一个临时的 div 元素
        const div = document.createElement('div');
        div.style.display = 'none';
        document.body.appendChild(div);

        try {
            // 创建组件实例
            componentRef = createComponent(StyxBusinessBrandComponent, {
                environmentInjector: this.appRef.injector,
                elementInjector: this.injector
            }) as ComponentRef<StyxBusinessBrandComponent>;

            // 设置组件输入属性
            componentRef.instance.styxType = broadObjectType;
            componentRef.instance.styxEntity = entity;
            componentRef.instance.styxShowName = false;
            componentRef.instance.styxShowIdentifier = false;
            componentRef.instance.styxFlexible = false;

            // 将组件添加到 DOM
            div.appendChild(componentRef.location.nativeElement);
            this.appRef.attachView(componentRef.hostView);

            // 等待组件渲染完成
            await new Promise<void>((resolve, reject) => {
                requestAnimationFrame(async () => {
                    // 获取 SVG 元素
                    let svgElement = div.querySelector('svg');
                    // 刷新页面时，emoji 还未加载就获取会获取失败
                    if (!svgElement) {
                        await new Promise<void>(tmpResolve => {
                            setTimeout(() => {
                                tmpResolve();
                            }, 100);
                        });
                        svgElement = div.querySelector('svg');
                        if (!svgElement) {
                            console.error('SVG element not found');
                            reject(new Error('SVG element not found'));
                            return;
                        }
                    }

                    // 获取 SVG 字符串并更新结果
                    result = {
                        icon: componentRef.instance.icon,
                        styxIconType: componentRef.instance.styxIconType,
                        iconColor: componentRef.instance.iconColor,
                        svgString: svgElement.outerHTML
                    };
                    resolve();
                });
            });

            return result;
        } finally {
            // 清理
            this.appRef.detachView(componentRef.hostView);
            componentRef.destroy();
            document.body.removeChild(div);
        }
    }

    getSvgByName(iconName: string) {
        return this.iconRegistry.getSvgIcon(iconName).pipe(take(1));
    }
}
