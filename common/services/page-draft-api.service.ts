import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Is } from '@atinc/ngx-styx';
import { BoardOptions, WikiPageContent } from '@wiki/common/interface/page';

@Injectable({
    providedIn: 'root'
})
export class PageDraftApiService {
    constructor(private http: HttpClient) {}

    fetchDrafts(spaceId: string) {
        return this.http.get(`/api/wiki/spaces/${spaceId}/draft-pages`);
    }

    fetchDraft(pageId: string, only_draft = false) {
        return this.http.get(`/api/wiki/draft-pages/${pageId}?only_draft=${only_draft}`);
    }

    saveAndPublish(
        pageId: string,
        options: {
            page_name: string;
            emoji_icon: string;
            content: WikiPageContent;
            send_notice: Is;
            board_options?: BoardOptions;
            stencil_id?: string;
        }
    ) {
        return this.http.put(`/api/wiki/draft-pages/${pageId}`, { publish: Is.yes, ...options });
    }

    /**
     * 保存草稿
     * 协同编辑下，每次调用会生成一个临时版本
     * @param pageId
     * @param content
     * @param stencil_id
     * @returns
     */
    save(
        pageId: string,
        options: {
            page_name: string;
            emoji_icon: string;
            content: WikiPageContent;
            board_options?: BoardOptions;
            stencil_id?: string;
            name?: string;
        }
    ) {
        return this.http.put(`/api/wiki/draft-pages/${pageId}`, options);
    }

    publish(pageId: string, force?: Is) {
        return this.http.put(`/api/wiki/draft-pages/${pageId}/publish`, { force });
    }

    deleteDraft(pageId: string) {
        return this.http.delete(`/api/wiki/draft-pages/${pageId}`);
    }

    fetchDraftPages(keyWords: string, pi: number) {
        return this.http.get(`/api/wiki/draft-pages?keywords=${keyWords}&pi=${pi}`);
    }

    updatePageDraftName(pageId: string, data: { emoji_icon: string; name: string }) {
        return this.http.put(`/api/wiki/draft-pages/${pageId}/name`, data);
    }
}
