import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { API_PREFIX } from '@wiki/app/constants';
import { DraftInfo, PageViewPort } from '@wiki/app/entities/page-info';

@Injectable({
    providedIn: 'root'
})
export class AITableApiService {
    constructor(private http: HttpClient) {}

    saveVersion(pageId: string) {
        return this.http.put(`${API_PREFIX}/ai-table-pages/${pageId}`, {});
    }
}
