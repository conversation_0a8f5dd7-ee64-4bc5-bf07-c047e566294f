import {
    DefaultInlineToolbarDefinition,
    ElementKinds,
    ElementOptionsInfo,
    MarkTypes,
    TheEditor,
    ToolbarActionTypes
} from '@worktile/theia';
import { isFirstNodeInToggleList } from '../plugins/toggle-list/queries/is-first-node-in-toggle-list';
import { INVALID_BLOCKS, WikiPluginTypes } from './editor.types';
import { StyxRichTextAIToolbarKeys } from '@atinc/ngx-styx';

export const GlobalToolbarDefinition = [
    WikiPluginTypes.group,
    ToolbarActionTypes.split,
    ToolbarActionTypes.undo,
    ToolbarActionTypes.redo,
    ToolbarActionTypes.paintformat,
    ToolbarActionTypes.clean,
    ToolbarActionTypes.split,
    ElementKinds.headingList,
    MarkTypes.fontSize,
    ToolbarActionTypes.split,
    MarkTypes.bold,
    MarkTypes.italic,
    MarkTypes.underline,
    MarkTypes.strike,
    ElementKinds.inlineCode,
    MarkTypes.color,
    MarkTypes.backgroundColor,
    ToolbarActionTypes.split,
    ToolbarActionTypes.alignType,
    ElementKinds.indent,
    ToolbarActionTypes.verticalAlign,
    ToolbarActionTypes.split,
    ElementKinds.numberedList,
    ElementKinds.bulletedList,
    ElementKinds.checkItem,
    WikiPluginTypes.toggleList,
    ToolbarActionTypes.split,
    ElementKinds.link,
    ElementKinds.image,
    WikiPluginTypes.emoji,
    WikiPluginTypes.mention,
    ElementKinds.table,
    ElementKinds.blockquote,
    ElementKinds.hr,
    WikiPluginTypes.searchReplace,
    ToolbarActionTypes.split
];

export const GlobalExtraElementOptions: ElementOptionsInfo[] = [
    {
        type: ElementKinds.blockquote,
        invalidChildrenTypes: [...INVALID_BLOCKS, WikiPluginTypes.alert, WikiPluginTypes.textDiagram]
    },
    {
        type: ElementKinds.numberedList,
        invalidChildrenTypes: [...INVALID_BLOCKS, WikiPluginTypes.alert, WikiPluginTypes.textDiagram]
    },
    {
        type: ElementKinds.bulletedList,
        invalidChildrenTypes: [...INVALID_BLOCKS, WikiPluginTypes.alert, WikiPluginTypes.textDiagram]
    },
    {
        type: ElementKinds.table,
        invalidChildrenTypes: [...INVALID_BLOCKS, WikiPluginTypes.textDiagram]
    },
    {
        type: ElementKinds.tableCell,
        invalidChildrenTypes: [...INVALID_BLOCKS, WikiPluginTypes.textDiagram]
    },
    {
        type: WikiPluginTypes.alert,
        invalidChildrenTypes: [
            ...INVALID_BLOCKS,
            ElementKinds.table,
            ElementKinds.tableCell,
            ElementKinds.code,
            WikiPluginTypes.textDiagram
        ]
    },
    {
        type: WikiPluginTypes.layout,
        invalidChildrenTypes: [WikiPluginTypes.layout]
    },
    {
        type: WikiPluginTypes.layoutColumn,
        isSecondaryContainer: true,
        invalidChildrenTypes: INVALID_BLOCKS
    }
];

export const GlobalExtraAutoFormatRules = [
    {
        key: ElementKinds.listItem,
        type: ElementKinds.listItem,
        query: (editor: TheEditor) => {
            return !isFirstNodeInToggleList(editor);
        }
    },
    {
        type: ElementKinds.listItem,
        key: ElementKinds.numberedList,
        query: (editor: TheEditor) => {
            return !isFirstNodeInToggleList(editor);
        }
    },
    {
        key: ElementKinds.code,
        type: ElementKinds.code,
        query: (editor: TheEditor) => {
            return !isFirstNodeInToggleList(editor);
        }
    },
    {
        key: ElementKinds.checkItem,
        type: ElementKinds.checkItem,
        query: (editor: TheEditor) => {
            return !isFirstNodeInToggleList(editor);
        }
    },
    {
        key: ElementKinds.hr,
        type: ElementKinds.hr,
        query: (editor: TheEditor) => {
            return !isFirstNodeInToggleList(editor);
        }
    },
    {
        key: ElementKinds.blockquote,
        type: ElementKinds.blockquote,
        query: (editor: TheEditor) => {
            return !isFirstNodeInToggleList(editor);
        }
    }
];

export const InlineToolbarDefinition = [StyxRichTextAIToolbarKeys.ai, ...DefaultInlineToolbarDefinition];
