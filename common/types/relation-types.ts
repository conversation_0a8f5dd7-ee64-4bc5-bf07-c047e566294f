import {
    AgileBroadObjectTypes,
    ShipBroadObjectTypes,
    TesthubBroadObjectTypes,
    WikiBroadObjectTypes,
    TeamsBroadObjectTypes,
    BroadObjectType,
    StyxTranslateService,
    InsightBroadObjectTypes
} from '@atinc/ngx-styx';
import { TheEditor } from '@worktile/theia';
import { PageExtensionData, PageExtensionInfo, PageExtensionReferences } from '@wiki/app/entities/page-extendsion';
import { WikiPluginTypes } from './editor.types';
import { Node } from 'slate';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

export class WorkItemInfo {
    _id?: string;
    title?: string;
    pin_yin?: string;
    description?: string;
    assignee?: string;
    identifier?: string;
    whole_identifier?: string;
    type?: number;
    state_id?: string;
    state_type?: number;
    created_at?: number;
    created_by?: string;
    completed_at?: number;
    deleted_at?: number;
    deleted_by?: string;
    updated_at?: number;
    updated_by?: string;
    archived_at?: number;
    archived_by?: string;
    completed_by?: string;
    priority?: string;
    position?: number;
    project_id?: string;
    child_ids?: string[];
    parent_ids?: string[];
    parent_id?: string;
}

export enum SyncPageOperationType {
    setNode = 'set_node',
    replaceChildren = 'replace_children'
}

export class SyncPageOperation {
    type: SyncPageOperationType;
    node: Node;
    newNode: Node;
    key?: string;
}

export interface WorkItemTypeInfo {
    _id?: string | number | any;
    key: number;
    name: string;
    icon: string;
    color: string;
    children_work_item_types?: string[];
    tabs?: number[];
}

export interface RelationListEditor<T extends PageExtensionData = PageExtensionData> {
    insert(editor: TheEditor, data: PageExtensionInfo<T>);
    selectItems<D>(
        editor: TheEditor,
        handle?: (data: PageExtensionInfo<T>, references?: PageExtensionReferences) => void,
        elementKey?: string,
        selectedItems?: D[]
    );
    [k: string]: Function;
}

export interface BaseRelationOption {
    broadObjectType: BroadObjectType;
    icon: string;
    iconColor?: string;
    notFoundText?: string;
    permission?: string;
}

export interface RelationItemOption extends BaseRelationOption {
    emptyIcon?: WikiPluginEmptyIcons;
    moreText?: string;
    placeholder?: string;
    routePrefix?: string;
}

export interface RelationListOption extends BaseRelationOption {
    title?: string;
    selectIcon?: string;
    apiPrefix?: string;
    apiRoute?: string;
}

export enum WikiPluginEmptyIcons {
    relationPage = 'wiki-relation-page-empty',
    relationWorkItem = 'wiki-relation-work-item-empty',
    relationTestCase = 'wiki-test-case-empty',
    relationIdea = 'wiki-relation-idea-empty',
    relationObjective = 'wiki-relation-objective-empty',
    relationTicket = 'wiki-relation-ticket-empty'
}

export const getCommonRelationItemOption = (translate: StyxTranslateService) => {
    return {
        [WikiPluginTypes.relationPage]: {
            broadObjectType: WikiBroadObjectTypes.page,
            icon: 'file',
            emptyIcon: WikiPluginEmptyIcons.relationPage,
            moreText: translate.instant<I18nSourceDefinitionType>('styx.morePage'),
            placeholder: translate.instant<I18nSourceDefinitionType>('wiki.relation.pageTitle'),
            notFoundText: translate.instant<I18nSourceDefinitionType>('wiki.relation.pageDeleted'),
            routePrefix: 'wiki/pages'
        },
        [WikiPluginTypes.relationWorkItem]: {
            broadObjectType: AgileBroadObjectTypes.workItem,
            icon: 'task-board',
            emptyIcon: WikiPluginEmptyIcons.relationWorkItem,
            moreText: translate.instant<I18nSourceDefinitionType>('wiki.relation.moreWorkItems'),
            placeholder: translate.instant<I18nSourceDefinitionType>('wiki.relation.workItemTitle'),
            notFoundText: translate.instant<I18nSourceDefinitionType>('wiki.relation.workItemDeleted'),
            routePrefix: 'agile/items',
            permission: 'relation_work_item'
        },
        [WikiPluginTypes.relationTestCase]: {
            broadObjectType: TesthubBroadObjectTypes.testCase,
            icon: 'test-case',
            emptyIcon: WikiPluginEmptyIcons.relationTestCase,
            moreText: translate.instant<I18nSourceDefinitionType>('wiki.relation.moreCases'),
            placeholder: translate.instant<I18nSourceDefinitionType>('wiki.relation.caseTitle'),
            notFoundText: translate.instant<I18nSourceDefinitionType>('wiki.relation.caseDeleted'),
            routePrefix: 'testhub/cases',
            permission: 'relation_test_case'
        },
        [WikiPluginTypes.relationIdea]: {
            broadObjectType: ShipBroadObjectTypes.idea,
            icon: 'bulb',
            emptyIcon: WikiPluginEmptyIcons.relationIdea,
            moreText: translate.instant<I18nSourceDefinitionType>('wiki.relation.moreRequirements'),
            placeholder: translate.instant<I18nSourceDefinitionType>('wiki.relation.requirementTitle'),
            notFoundText: translate.instant<I18nSourceDefinitionType>('wiki.relation.requirementDeleted'),
            routePrefix: 'ship/ideas',
            permission: 'relation_idea'
        },
        [WikiPluginTypes.relationTicket]: {
            broadObjectType: ShipBroadObjectTypes.ticket,
            icon: 'ticket',
            emptyIcon: WikiPluginEmptyIcons.relationTicket,
            moreText: translate.instant<I18nSourceDefinitionType>('wiki.relation.moreTickets'),
            placeholder: translate.instant<I18nSourceDefinitionType>('wiki.relation.ticketTitle'),
            notFoundText: translate.instant<I18nSourceDefinitionType>('wiki.relation.ticketDeleted'),
            routePrefix: 'ship/tickets',
            permission: 'relation_ticket'
        },
        [WikiPluginTypes.relationObjective]: {
            broadObjectType: TeamsBroadObjectTypes.objective,
            icon: 'target',
            emptyIcon: WikiPluginEmptyIcons.relationObjective,
            moreText: translate.instant<I18nSourceDefinitionType>('wiki.relation.moreObjectives'),
            placeholder: translate.instant<I18nSourceDefinitionType>('wiki.relation.objectiveTitle'),
            notFoundText: translate.instant<I18nSourceDefinitionType>('wiki.relation.objectiveDeleted'),
            routePrefix: 'teams/items',
            permission: 'relation_objective'
        }
    };
};

export const getCommonRelationListOption = (translate: StyxTranslateService) => {
    return {
        [WikiPluginTypes.relationWorkItemList]: {
            broadObjectType: AgileBroadObjectTypes.workItem,
            title: translate.instant<I18nSourceDefinitionType>('styx.workItem', { isTitle: true }),
            icon: 'wiki-work-item-list',
            notFoundText: translate.instant<I18nSourceDefinitionType>('common.noResult'),
            selectIcon: 'tickets-select',
            apiRoute: 'work-item',
            permission: 'relation_work_item'
        },
        [WikiPluginTypes.relationTestCaseList]: {
            broadObjectType: TesthubBroadObjectTypes.testCase,
            title: translate.instant<I18nSourceDefinitionType>('styx.testCase'),
            icon: 'wiki-relation-test-case-list',
            notFoundText: translate.instant<I18nSourceDefinitionType>('common.noResult'),
            selectIcon: 'tickets-select',
            apiRoute: 'test-case',
            permission: 'relation_test_case'
        },
        [WikiPluginTypes.relationIdeaList]: {
            broadObjectType: ShipBroadObjectTypes.idea,
            title: translate.instant<I18nSourceDefinitionType>('wiki.resource.productRequirement'),
            icon: 'wiki-relation-idea-list',
            notFoundText: translate.instant<I18nSourceDefinitionType>('common.noResult'),
            selectIcon: 'tickets-select',
            apiRoute: 'idea',
            permission: 'relation_idea'
        },
        [WikiPluginTypes.relationTicketList]: {
            broadObjectType: ShipBroadObjectTypes.ticket,
            title: translate.instant<I18nSourceDefinitionType>('styx.ticket'),
            icon: 'wiki-relation-ticket-list',
            notFoundText: translate.instant<I18nSourceDefinitionType>('common.noResult'),
            selectIcon: 'tickets-select',
            apiRoute: 'ticket',
            permission: 'relation_ticket'
        },
        [WikiPluginTypes.relationObjectiveList]: {
            broadObjectType: TeamsBroadObjectTypes.objective,
            title: translate.instant<I18nSourceDefinitionType>('styx.objective'),
            icon: 'wiki-relation-objective-list',
            notFoundText: translate.instant<I18nSourceDefinitionType>('common.noResult'),
            selectIcon: 'objectives-select',
            apiRoute: 'objectives',
            permission: 'relation_objective'
        },
        [WikiPluginTypes.relationReport]: {
            broadObjectType: InsightBroadObjectTypes.insightView,
            title: translate.instant<I18nSourceDefinitionType>('styx.reportInsight'),
            icon: 'wiki-relation-report',
            notFoundText: translate.instant<I18nSourceDefinitionType>('wiki.relation.reportDeleted'),
            selectIcon: 'objectives-select',
            apiRoute: 'insight',
            permission: 'relation_report'
        }
    };
};

export const getCommonRelationOption = (translate: StyxTranslateService): { [key: string]: RelationItemOption | RelationListOption } => {
    return {
        ...getCommonRelationItemOption(translate),
        ...getCommonRelationListOption(translate)
    };
};

export const WikiPluginEmptySvgs = [
    {
        key: WikiPluginEmptyIcons.relationTestCase,
        svg: `
        <svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <title>测试用例_为空</title>
            <g id="测试用例_为空" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g id="icon">
                    <rect id="矩形" fill="#FFFFFF" x="0" y="0" width="36" height="36" rx="4"></rect>
                    <path d="M32,0 C34.209139,0 36,1.790861 36,4 L36,32 C36,34.209139 34.209139,36 32,36 L4,36 C1.790861,36 0,34.209139 0,32 L0,4 C0,1.790861 1.790861,0 4,0 L32,0 Z M32,1 L4,1 C2.34314575,1 1,2.34314575 1,4 L1,32 C1,33.6568542 2.34314575,35 4,35 L32,35 C33.6568542,35 35,33.6568542 35,32 L35,4 C35,2.34314575 33.6568542,1 32,1 Z" id="矩形" fill="#EEEEEE" fill-rule="nonzero"></path>
                    <g id="编组-4" transform="translate(10.000000, 8.000000)">
                        <path d="M13.4376505,0 L13.4376505,1.65279886 L10.99,1.652 L10.9907309,7.66722672 L16.6299516,16.1617399 C17.3174969,17.4206502 16.8543141,18.9985642 15.5954038,19.6861096 C15.2135975,19.8946305 14.7855296,20.0039062 14.3504928,20.0039062 L2.59725536,20.0039062 C1.16283083,20.0039062 0,18.8410753 0,17.4066508 C0,16.9753631 0.1074013,16.5508639 0.312509505,16.1714699 L5.90906537,7.66910224 L5.909,1.652 L3.52085728,1.65279886 L3.52085728,0 L13.4376505,0 Z" id="形状结合" fill="#EEEEEE" fill-rule="nonzero"></path>
                        <path d="M13.4376505,0 L13.4376505,1.65279886 L10.9906061,1.652 L10.9907309,7.66722672 C12.5068127,9.79427214 13.5552715,11.3895562 14.1361072,12.4530789 C14.5862094,13.2772258 13.5924651,13.8093083 11.1548742,14.0493266 C11.8424195,15.3082369 7.84471472,14.5780054 7.84471472,13.1435808 C7.84471472,12.9538142 7.23666405,12.7942276 6.57311925,12.6426832 L6.20900332,12.5606871 C5.48304896,12.3977578 4.81061343,12.2396091 4.90906537,12.0575 C3.1952353,12.9944027 2.52028872,13.1262624 2.88422563,12.4530789 C3.54174558,11.2368473 4.55002549,9.64218844 5.90906537,7.66910224 L5.90860609,1.652 L3.52085728,1.65279886 L3.52085728,0 L13.4376505,0 Z" id="形状结合" fill="#F5F5F5" fill-rule="nonzero"></path>
                        <path d="M9,5.5 L10.9983076,5.5 L10.9983076,5.5 L10.9983076,6.5 L9,6.5 C8.72385763,6.5 8.5,6.27614237 8.5,6 C8.5,5.72385763 8.72385763,5.5 9,5.5 Z" id="矩形" fill="#D8D8D8"></path>
                        <path d="M9,2.5 L10.9983076,2.5 L10.9983076,2.5 L10.9983076,3.5 L9,3.5 C8.72385763,3.5 8.5,3.27614237 8.5,3 C8.5,2.72385763 8.72385763,2.5 9,2.5 Z" id="矩形备份" fill="#D8D8D8"></path>
                        <path d="M3.47364644,11.369661 C4.97583958,11.1324949 6.49359059,11.5203635 7.98664463,12.5373126 C9.77377834,13.7545654 11.6620253,13.5826696 13.8145461,11.9227661 L14.7306711,13.3012858 C12.0686061,15.3053423 9.45688173,15.5384876 7.05621322,13.9033439 C5.31027324,12.714149 3.65255647,12.6409286 1.94023746,13.6980379 Z" id="形状结合" fill="#6698FF" fill-rule="nonzero"></path>
                    </g>
                </g>
            </g>
        </svg>
        `
    },
    {
        key: WikiPluginEmptyIcons.relationIdea,
        svg: `
            <svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <title>产品需求_为空</title>
                <g id="产品需求_为空" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g id="icon备份">
                        <rect id="矩形" fill="#FFFFFF" x="0" y="0" width="36" height="36" rx="4"></rect>
                        <path d="M32,0 C34.209139,0 36,1.790861 36,4 L36,32 C36,34.209139 34.209139,36 32,36 L4,36 C1.790861,36 0,34.209139 0,32 L0,4 C0,1.790861 1.790861,0 4,0 L32,0 Z M32,1 L4,1 C2.34314575,1 1,2.34314575 1,4 L1,32 C1,33.6568542 2.34314575,35 4,35 L32,35 C33.6568542,35 35,33.6568542 35,32 L35,4 C35,2.34314575 33.6568542,1 32,1 Z" id="矩形" fill="#EEEEEE" fill-rule="nonzero"></path>
                        <g id="编组-8" transform="translate(7.083945, 7.714286)">
                            <g id="编组-5" transform="translate(3.644781, 0.000000)">
                                <path d="M7.71428571,16.4263725 C8.99633991,16.4263725 11.6929999,17.0319868 11.6929999,15.9081121 C11.6929999,15.3459833 12.2370113,13.6262131 12.6594857,13.2923142 C14.353159,11.9537346 15.4285714,9.90928017 15.4285714,7.52547476 C15.4285714,3.26499241 11.9933797,0 7.73289733,0 C3.47241497,0 0,3.26499241 0,7.52547476 C0,9.89338527 1.07261693,11.9264552 2.75700218,13.2654438 C3.07433414,13.5177043 3.75864719,14.8175205 3.75864719,15.2307528 C3.75864719,16.9354647 6.26781947,16.4263725 7.71428571,16.4263725 Z" id="椭圆形" fill="#EEEEEE"></path>
                                <path d="M10.8020894,6.20361598 L9.14914888,6.72074014 L7.60697187,6.20707934 L7.49767067,6.18112005 C7.38693786,6.16487398 7.27318687,6.1777318 7.1679077,6.21937184 L5.94257745,6.70531157 L4.71716101,6.21937184 C4.22043124,6.02290521 3.71468043,6.48282187 3.86321656,6.99592674 L6.78684465,17.0953403 C6.86642589,17.3702466 7.11815502,17.5594394 7.4043484,17.5594394 L8.17205758,17.5594394 C8.46047187,17.5594394 8.71358601,17.3673539 8.79120006,17.0895791 L11.6131129,6.9901655 C11.750312,6.49914039 11.2886814,6.0514403 10.8020894,6.20361598 Z M7.42243459,7.49988299 L8.94258725,8.00543497 L9.04009504,8.02955128 C9.13888978,8.04592546 9.24063116,8.03906522 9.3370918,8.00889834 L10.0568632,7.78274014 L7.78372031,15.914883 L5.46172031,7.89588299 L5.70609365,7.99314248 C5.8579958,8.05322284 6.0270729,8.05322284 6.17897505,7.99314248 L7.42243459,7.49988299 Z" id="路径-2" fill="#6698FF" fill-rule="nonzero"></path>
                                <path d="M5.78285714,18.2022966 L9.64571429,18.2022966 C9.99917653,18.2022966 10.2857143,18.4888343 10.2857143,18.8422966 L10.2857143,21.4251537 C10.2857143,22.4855404 9.42610101,23.3451537 8.36571429,23.3451537 L7.06285714,23.3451537 C6.00247042,23.3451537 5.14285714,22.4855404 5.14285714,21.4251537 L5.14285714,18.8422966 C5.14285714,18.4888343 5.4293949,18.2022966 5.78285714,18.2022966 Z" id="矩形备份-5" fill="#EEEEEE"></path>
                                <path d="M3.92387029,15.630868 L11.6438703,15.630868 C11.9973325,15.630868 12.2838703,15.9174058 12.2838703,16.270868 L12.2838703,18.8537252 C12.2838703,19.9141119 11.424257,20.7737252 10.3638703,20.7737252 L5.20387029,20.7737252 C4.14348357,20.7737252 3.28387029,19.9141119 3.28387029,18.8537252 L3.28387029,16.270868 C3.28387029,15.9174058 3.57040805,15.630868 3.92387029,15.630868 Z" id="蒙版" fill="#DDDDDD"></path>
                                <polygon id="路径" fill="#EEEEEE" fill-rule="nonzero" points="12.2838703 16.8428571 12.2838703 17.8714286 3.28387029 17.8714286 3.28387029 16.8428571"></polygon>
                                <path d="M12.2838703,18.8987893 C12.2631411,19.142844 12.2326046,19.3291737 12.192261,19.4577784 C12.1519173,19.586383 12.0748984,19.7429104 11.9612043,19.9273607 L3.61711881,19.9273607 C3.52105472,19.8060342 3.44391733,19.6607595 3.38570664,19.4915365 C3.32749595,19.3223135 3.2935505,19.1247311 3.28387029,18.8987893 L12.2838703,18.8987893 Z" id="路径" fill="#EEEEEE" fill-rule="nonzero"></path>
                            </g>
                            <g id="编组-7" transform="translate(0.000000, 2.914287)" fill="#DDDDDD" fill-rule="nonzero">
                                <path d="M0.1652498,0.212570395 C0.402893604,-0.0512078516 0.809376489,-0.0723940038 1.07315474,0.1652498 L2.33382474,1.30101599 C2.59760298,1.53865979 2.61878913,1.94514268 2.38114533,2.20892092 C2.14350153,2.47269917 1.73701864,2.49388532 1.47324039,2.25624152 L0.212570395,1.12047533 C-0.0512078516,0.882831528 -0.0723940038,0.476348642 0.1652498,0.212570395 Z" id="路径-4"></path>
                                <path d="M2.15031888,4.57971306 C2.50378112,4.57971306 2.79031888,4.86625082 2.79031888,5.21971306 C2.79031888,5.5731753 2.50378112,5.85971306 2.15031888,5.85971306 L0.642862566,5.85971306 C0.289400326,5.85971306 0.00286256579,5.5731753 0.00286256579,5.21971306 C0.00286256579,4.86625082 0.289400326,4.57971306 0.642862566,4.57971306 L2.15031888,4.57971306 Z" id="路径-5"></path>
                                <path d="M1.47016778,8.11732779 C1.73240725,7.87798702 2.13901837,7.8965501 2.37835913,8.15878957 C2.61769989,8.42102904 2.59913682,8.82764016 2.33689735,9.06698092 L1.07622735,10.2175694 C0.813987878,10.4569102 0.407376759,10.4383471 0.168035998,10.1761076 C-0.0713047638,9.91386815 -0.052741691,9.50725703 0.209497781,9.26791627 L1.47016778,8.11732779 Z" id="路径-6"></path>
                            </g>
                            <g id="编组-7备份" transform="translate(21.176727, 8.107089) scale(-1, 1) translate(-21.176727, -8.107089) translate(19.780139, 2.914287)" fill="#DDDDDD" fill-rule="nonzero">
                                <path d="M0.1652498,0.212570395 C0.402893604,-0.0512078516 0.809376489,-0.0723940038 1.07315474,0.1652498 L2.33382474,1.30101599 C2.59760298,1.53865979 2.61878913,1.94514268 2.38114533,2.20892092 C2.14350153,2.47269917 1.73701864,2.49388532 1.47324039,2.25624152 L0.212570395,1.12047533 C-0.0512078516,0.882831528 -0.0723940038,0.476348642 0.1652498,0.212570395 Z" id="路径-4"></path>
                                <path d="M2.15031888,4.57685592 C2.50535908,4.57685592 2.79317603,4.86467287 2.79317603,5.21971306 C2.79317603,5.57475326 2.50535908,5.86257021 2.15031888,5.86257021 L0.642862566,5.86257021 C0.287822369,5.86257021 5.42293301e-06,5.57475326 5.42293301e-06,5.21971306 C5.42293301e-06,4.86467287 0.287822369,4.57685592 0.642862566,4.57685592 L2.15031888,4.57685592 Z" id="路径-5"></path>
                                <path d="M1.47016778,8.11732779 C1.73240725,7.87798702 2.13901837,7.8965501 2.37835913,8.15878957 C2.61769989,8.42102904 2.59913682,8.82764016 2.33689735,9.06698092 L1.07622735,10.2175694 C0.813987878,10.4569102 0.407376759,10.4383471 0.168035998,10.1761076 C-0.0713047638,9.91386815 -0.052741691,9.50725703 0.209497781,9.26791627 L1.47016778,8.11732779 Z" id="路径-6"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </svg>
        `
    },
    {
        key: WikiPluginEmptyIcons.relationTicket,
        svg: `
            <svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <title>工单_为空</title>
                <g id="工单_为空" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <g id="icon备份">
                        <rect id="矩形" fill="#FFFFFF" x="0" y="0" width="36" height="36" rx="4"></rect>
                        <path d="M32,0 C34.209139,0 36,1.790861 36,4 L36,32 C36,34.209139 34.209139,36 32,36 L4,36 C1.790861,36 0,34.209139 0,32 L0,4 C0,1.790861 1.790861,0 4,0 L32,0 Z M32,1 L4,1 C2.34314575,1 1,2.34314575 1,4 L1,32 C1,33.6568542 2.34314575,35 4,35 L32,35 C33.6568542,35 35,33.6568542 35,32 L35,4 C35,2.34314575 33.6568542,1 32,1 Z" id="矩形" fill="#EEEEEE" fill-rule="nonzero"></path>
                        <g id="编组-2" transform="translate(6.428571, 7.714286)">
                            <path d="M19.2857143,18.0171429 C19.2857143,20.1379163 17.5664877,21.8571429 15.4457143,21.8571429 L1.92857143,21.8571429 L2.07928815,21.8513405 C3.017467,21.7788196 3.76748745,21.0349979 3.84966677,20.0995582 C3.85461196,20.0731836 3.85714286,20.0479911 3.85714286,20.0236794 L3.85714286,3.84 C3.85714286,1.71922656 5.57636942,-9.42687869e-16 7.69714286,0 L21.2142857,0 C20.1491651,0 19.2857143,0.86345084 19.2857143,1.92857143 L19.2857143,18.0171429 Z" id="形状结合" fill="#F5F5F5"></path>
                            <path d="M11.6,10.2857143 C13.375201,10.2857143 14.8142857,8.84662955 14.8142857,7.07142857 C14.8142857,5.29622759 13.375201,3.85714286 11.6,3.85714286 C9.82479902,3.85714286 8.38571429,5.29622759 8.38571429,7.07142857 C8.38571429,8.84662955 9.82479902,10.2857143 11.6,10.2857143 Z" id="椭圆形" fill="#CEDDFC"></path>
                            <path d="M14.747089,4.3578063 C14.9617229,4.17177788 15.2865238,4.19496717 15.4725522,4.40960103 C15.6585807,4.62423489 15.6353914,4.94903584 15.4207575,5.13506426 L11.574963,8.46830845 L10.2988448,7.1921947 C10.0980034,6.99135399 10.0980028,6.66572629 10.2988435,6.46488487 C10.4996842,6.26404345 10.8253119,6.26404288 11.0261533,6.4648836 L11.6254991,7.06329242 L14.747089,4.3578063 Z" id="路径备份-2" fill="#6698FF" fill-rule="nonzero"></path>
                            <path d="M9.0778751,13.5 C9.0778751,13.8550402 8.79005816,14.1428571 8.43501796,14.1428571 L8.35714286,14.1428571 C8.00210266,14.1428571 7.71428571,13.8550402 7.71428571,13.5 C7.71428571,13.1449598 8.00210266,12.8571429 8.35714286,12.8571429 L8.43501796,12.8571429 C8.79005816,12.8571429 9.0778751,13.1449598 9.0778751,13.5 Z" id="直线备份-4" fill="#CACACA" fill-rule="nonzero"></path>
                            <path d="M9.0778751,16.0714286 C9.0778751,16.4264688 8.79005816,16.7142857 8.43501796,16.7142857 L8.35714286,16.7142857 C8.00210266,16.7142857 7.71428571,16.4264688 7.71428571,16.0714286 C7.71428571,15.7163884 8.00210266,15.4285714 8.35714286,15.4285714 L8.43501796,15.4285714 C8.79005816,15.4285714 9.0778751,15.7163884 9.0778751,16.0714286 Z" id="直线备份-5" fill="#CACACA" fill-rule="nonzero"></path>
                            <path d="M15.4285714,18.6391455 C15.4285714,18.9502186 15.1763969,19.2023931 14.8653238,19.2023931 L10.8489619,19.2023931 C10.5378888,19.2023931 10.2857143,18.9502186 10.2857143,18.6391455 C10.2857143,18.3280724 10.5378888,18.0758979 10.8489619,18.0758979 L14.8653238,18.0758979 C15.1763969,18.0758979 15.4285714,18.3280724 15.4285714,18.6391455 Z M15.4285714,16.0677169 C15.4285714,16.37879 15.1763969,16.6309645 14.8653238,16.6309645 L10.8489619,16.6309645 C10.5378888,16.6309645 10.2857143,16.37879 10.2857143,16.0677169 C10.2857143,15.7566439 10.5378888,15.5044693 10.8489619,15.5044693 L14.8653238,15.5044693 C15.1763969,15.5044693 15.4285714,15.7566439 15.4285714,16.0677169 Z M15.4285714,13.4962884 C15.4285714,13.8073614 15.1763969,14.059536 14.8653238,14.059536 L10.8489619,14.059536 C10.5378888,14.059536 10.2857143,13.8073614 10.2857143,13.4962884 C10.2857143,13.1852153 10.5378888,12.9330408 10.8489619,12.9330408 L14.8653238,12.9330408 C15.1763969,12.9330408 15.4285714,13.1852153 15.4285714,13.4962884 Z" id="直线备份-3" fill="#CACACA" fill-rule="nonzero"></path>
                            <path d="M9.0778751,18.6428571 C9.0778751,18.9978973 8.79005816,19.2857143 8.43501796,19.2857143 L8.35714286,19.2857143 C8.00210266,19.2857143 7.71428571,18.9978973 7.71428571,18.6428571 C7.71428571,18.2878169 8.00210266,18 8.35714286,18 L8.43501796,18 C8.79005816,18 9.0778751,18.2878169 9.0778751,18.6428571 Z" id="直线备份-6" fill="#CACACA" fill-rule="nonzero"></path>
                            <path d="M19.2857143,0 L23.1428571,0 L23.1428571,1.92857143 C23.1428571,2.99369202 22.2794063,3.85714286 21.2142857,3.85714286 C20.1491651,3.85714286 19.2857143,2.99369202 19.2857143,1.92857143 L19.2857143,0 L19.2857143,0 Z" id="矩形备份-3" fill="#DDDDDD" transform="translate(21.214286, 1.928571) rotate(-180.000000) translate(-21.214286, -1.928571) "></path>
                            <path d="M0,18 L3.85714286,18 L3.85714286,19.9285714 C3.85714286,20.993692 2.99369202,21.8571429 1.92857143,21.8571429 C0.86345084,21.8571429 -9.16049529e-17,20.993692 0,19.9285714 L0,18 L0,18 Z" id="矩形备份-2" fill="#DDDDDD"></path>
                        </g>
                    </g>
                </g>
            </svg>
        `
    },
    {
        key: WikiPluginEmptyIcons.relationWorkItem,
        svg: `
        <svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <title>工作项_为空</title>
            <g id="工作项_为空" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g id="icon">
                    <rect id="矩形" fill="#FFFFFF" x="0" y="0" width="36" height="36" rx="4"></rect>
                    <path d="M32,0 C34.209139,0 36,1.790861 36,4 L36,32 C36,34.209139 34.209139,36 32,36 L4,36 C1.790861,36 0,34.209139 0,32 L0,4 C0,1.790861 1.790861,0 4,0 L32,0 Z M32,1 L4,1 C2.34314575,1 1,2.34314575 1,4 L1,32 C1,33.6568542 2.34314575,35 4,35 L32,35 C33.6568542,35 35,33.6568542 35,32 L35,4 C35,2.34314575 33.6568542,1 32,1 Z" id="矩形" fill="#EEEEEE" fill-rule="nonzero"></path>
                    <g id="Board" transform="translate(9.000000, 7.000000)">
                        <path d="M2,2 L11.8936014,2 L11.8936014,2 L16,2 C17.1045695,2 18,2.8954305 18,4 L18,8.37384588 L18,8.37384588 L18,20 C18,21.1045695 17.1045695,22 16,22 L2,22 C0.8954305,22 1.3527075e-16,21.1045695 0,20 L0,4 C-1.3527075e-16,2.8954305 0.8954305,2 2,2 Z" id="蒙版" fill="#EEEEEE"></path>
                        <path d="M2.5,3.5 L11.4113345,3.5 L15.5,3.5 C16.0522847,3.5 16.5,3.94771525 16.5,4.5 L16.5,8.917769 L16.5,17.6684302 L16.5,17.6684302 L13.9910591,20.5 L2.5,20.5 C1.94771525,20.5 1.5,20.0522847 1.5,19.5 L1.5,4.5 C1.5,3.94771525 1.94771525,3.5 2.5,3.5 Z" id="蒙版" fill="#FAFAFA"></path>
                        <path d="M12.4028788,8 C12.7342496,8 13.0028788,8.26862915 13.0028788,8.6 C13.0028788,8.89823376 12.7852892,9.14564675 12.5002019,9.19214701 L12.4028788,9.2 L10.6,9.2 C10.2686292,9.2 10,8.93137085 10,8.6 C10,8.30176624 10.2175896,8.05435325 10.5026769,8.00785299 L10.6,8 L12.4028788,8 Z" id="直线" fill="#CACACA" fill-rule="nonzero"></path>
                        <rect id="矩形" fill="#D1E0FF" x="3" y="7" width="5" height="5" rx="0.5"></rect>
                        <polygon id="路径" fill="#6698FF" fill-rule="nonzero" points="6.4105555 8.30630424 6.95823253 8.81870045 5.19647768 10.7017602 3.9848398 9.49012652 4.51516895 8.95979551 5.17800438 9.62250234"></polygon>
                        <rect id="矩形备份" fill="#D1E0FF" x="3" y="14" width="5" height="5" rx="0.5"></rect>
                        <polygon id="路径" fill="#6698FF" fill-rule="nonzero" points="6.4105555 15.3063042 6.95823253 15.8187004 5.19647768 17.7017602 3.9848398 16.4901265 4.51516895 15.9597955 5.17800438 16.6225023"></polygon>
                        <path d="M14,12 C14.3212572,12 14.5816876,12.2686292 14.5816876,12.6 C14.5816876,12.8982338 14.370739,13.1456468 14.0943528,13.192147 L14,13.2 L10.5816876,13.2 C10.2604304,13.2 10,12.9313708 10,12.6 C10,12.3017662 10.2109486,12.0543532 10.4873349,12.007853 L10.5816876,12 L14,12 Z" id="直线" fill="#CACACA" fill-rule="nonzero"></path>
                        <path d="M14,16 C14.3212572,16 14.5816876,16.2686292 14.5816876,16.6 C14.5816876,16.8982338 14.370739,17.1456468 14.0943528,17.192147 L14,17.2 L10.5816876,17.2 C10.2604304,17.2 10,16.9313708 10,16.6 C10,16.3017662 10.2109486,16.0543532 10.4873349,16.007853 L10.5816876,16 L14,16 Z" id="直线" fill="#CACACA" fill-rule="nonzero"></path>
                        <path d="M9,0 C10.5062311,0 11.7272727,1.13650794 11.7272727,2.53846154 L13,2.53846154 C13.5522847,2.53846154 14,2.98617679 14,3.53846154 L14,5 L4,5 L4,3.53846154 C4,2.98617679 4.44771525,2.53846154 5,2.53846154 L6.27272727,2.53846154 C6.27272727,1.13650794 7.49376886,0 9,0 Z" id="形状结合" fill="#D1E0FF"></path>
                        <circle id="椭圆形" fill="#6698FF" cx="9" cy="3" r="1"></circle>
                    </g>
                </g>
            </g>
        </svg>
        `
    },
    {
        key: WikiPluginEmptyIcons.relationObjective,
        svg: `
        <svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <title>目标_为空</title>
            <g id="目标_为空" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g id="icon备份">
                    <rect id="矩形" fill="#FFFFFF" x="0" y="0" width="36" height="36" rx="4"></rect>
                    <path d="M32,0 C34.209139,0 36,1.790861 36,4 L36,32 C36,34.209139 34.209139,36 32,36 L4,36 C1.790861,36 0,34.209139 0,32 L0,4 C0,1.790861 1.790861,0 4,0 L32,0 Z M32,1 L4,1 C2.34314575,1 1,2.34314575 1,4 L1,32 C1,33.6568542 2.34314575,35 4,35 L32,35 C33.6568542,35 35,33.6568542 35,32 L35,4 C35,2.34314575 33.6568542,1 32,1 Z" id="矩形" fill="#EEEEEE" fill-rule="nonzero"></path>
                    <g id="编组-3" transform="translate(18.000000, 18.045460) rotate(-315.000000) translate(-18.000000, -18.045460) translate(7.000000, 7.000000)">
                        <path d="M16.034471,1.30803652 C19.5773991,3.13498615 22,6.83003822 22,11.090919 C22,17.1660513 17.0751322,22.090919 11,22.090919 C4.92486775,22.090919 4.12114787e-13,17.1660513 4.12114787e-13,11.090919 C4.12114787e-13,6.8637712 2.38439375,3.19352836 5.88159327,1.35177849 L8.09895135,5.57073665 C6.12463625,6.60444327 4.77746033,8.67283642 4.77746033,11.0558794 C4.77746033,14.4731413 7.54769844,17.2433794 10.9649603,17.2433794 C14.3822222,17.2433794 17.1524603,14.4731413 17.1524603,11.0558794 C17.1524603,8.67605078 15.8089162,6.61002373 13.838955,5.57492523 L16.034471,1.30803652 Z" id="形状结合" fill="#EEEEEE"></path>
                        <path d="M15.2274603,11.0558794 C15.2274603,13.4099931 13.3190741,15.3183794 10.9649603,15.3183794 C8.61084658,15.3183794 6.70246033,13.4099931 6.70246033,11.0558794 C6.70246033,9.41844628 7.62575115,7.99665932 8.98012172,7.28272955 L10.0571224,9.27801817 C9.41486037,9.62748009 8.97895834,10.3082944 8.97895834,11.090919 C8.97895834,12.2300063 9.90237104,13.153419 11.0414583,13.153419 C12.1805456,13.153419 13.1039583,12.2300063 13.1039583,11.090919 C13.1039583,10.2676696 12.6216278,9.55707446 11.9241419,9.22630872 L12.9266485,7.27061703 C14.2936373,7.98049364 15.2274603,9.40913348 15.2274603,11.0558794 Z" id="形状结合" fill="#DDDDDD" fill-rule="nonzero"></path>
                        <path d="M14.240448,0.784209756 C13.467598,0.297622231 12.1744101,-1.52766688e-13 10.909081,-1.52766688e-13 C9.70447859,-1.52766688e-13 8.55741119,0.210006265 7.86034393,0.66470292 L10.9722276,7.36585514 L14.240448,0.784209756 Z" id="路径" fill="#6698FF" fill-rule="nonzero"></path>
                    </g>
                </g>
            </g>
        </svg>
        `
    },
    {
        key: WikiPluginEmptyIcons.relationPage,
        svg: `
        <svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <title>页面_为空</title>
            <g id="页面_为空" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <g id="icon">
                    <rect id="矩形" fill="#FFFFFF" x="0" y="0" width="36" height="36" rx="4"></rect>
                    <path d="M32,0 C34.209139,0 36,1.790861 36,4 L36,32 C36,34.209139 34.209139,36 32,36 L4,36 C1.790861,36 0,34.209139 0,32 L0,4 C0,1.790861 1.790861,0 4,0 L32,0 Z M32,1 L4,1 C2.34314575,1 1,2.34314575 1,4 L1,32 C1,33.6568542 2.34314575,35 4,35 L32,35 C33.6568542,35 35,33.6568542 35,32 L35,4 C35,2.34314575 33.6568542,1 32,1 Z" id="矩形" fill="#EEEEEE" fill-rule="nonzero"></path>
                    <g id="路径-24" transform="translate(9.000000, 7.000000)">
                        <path d="M2,3 L9.91133451,3 L9.91133451,3 L13,3 C14.1045695,3 15,3.8954305 15,5 L15,9.37384588 L15,9.37384588 L15,21 C15,22.1045695 14.1045695,23 13,23 L2,23 C0.8954305,23 1.3527075e-16,22.1045695 0,21 L0,5 C-1.3527075e-16,3.8954305 0.8954305,3 2,3 Z" id="蒙版备份" fill="#DDDDDD"></path>
                        <path d="M5,0 L13,0 L13,0 L18,5 L18,18 C18,19.1045695 17.1045695,20 16,20 L5,20 C3.8954305,20 3,19.1045695 3,18 L3,2 C3,0.8954305 3.8954305,2.02906125e-16 5,0 Z" id="蒙版" fill="#F5F5F5"></path>
                        <path d="M13,0 L18,5 L14.2,5 C13.5372583,5 13,4.46274168 13,3.79999995 L13,0 L13,0 Z" id="矩形" fill="#D8D8D8"></path>
                        <path d="M12,7.6 C12,7.93137085 11.7313708,8.2 11.4,8.2 L7.6,8.2 C7.26862915,8.2 7,7.93137085 7,7.6 C7,7.26862915 7.26862915,7 7.6,7 L11.4,7 C11.7313708,7 12,7.26862915 12,7.6 Z" id="直线" fill="#6698FF" fill-rule="nonzero"></path>
                        <path d="M14,11.6 C14,11.9313708 13.7313708,12.2 13.4,12.2 L7.6,12.2 C7.26862915,12.2 7,11.9313708 7,11.6 C7,11.2686292 7.26862915,11 7.6,11 L13.4,11 C13.7313708,11 14,11.2686292 14,11.6 Z" id="直线" fill="#73D897" fill-rule="nonzero"></path>
                        <path d="M14,15.6 C14,15.9313708 13.7313708,16.2 13.4,16.2 L7.6,16.2 C7.26862915,16.2 7,15.9313708 7,15.6 C7,15.2686292 7.26862915,15 7.6,15 L13.4,15 C13.7313708,15 14,15.2686292 14,15.6 Z" id="直线" fill="#FF7575" fill-rule="nonzero"></path>
                    </g>
                </g>
            </g>
        </svg>
        `
    }
];
