import { CustomElementKinds, ElementKinds, TheOptions } from '@worktile/theia';

export enum WikiPluginTypes {
    aiWriting = 'ai-writing',
    save = 'save',
    date = 'date',
    label = 'label',
    relationPage = 'relation-page',
    relationWorkItem = 'relation-work-item',
    emoji = 'emoji',
    mention = 'mention',
    attachment = 'attachment',
    attachmentType = 'attachment-type',
    attachmentCloud = 'attachment-upload',
    attachmentSelect = 'attachment-select',
    alert = 'alert',
    toc = 'toc',
    stencil = 'stencil',
    layout = 'layout',
    layoutColumn = 'layout-column',
    searchReplace = 'search-replace',
    group = 'group',
    toggleList = 'toggle-list',
    toggleListItem = 'toggle-list-item',
    formula = 'formula',
    textDiagram = 'text-diagram',
    diagramBoard = 'diagram-board',
    relationPageTree = 'relation-page-tree',
    relationWorkItemList = 'relation-work-item-list',
    relationTestCase = 'relation-test-case',
    relationTestCaseList = 'relation-test-case-list',
    relationIdea = 'relation-idea',
    relationIdeaList = 'relation-idea-list',
    relationTicket = 'relation-ticket',
    relationTicketList = 'relation-ticket-list',
    relationObjective = 'relation-objective',
    relationObjectiveList = 'relation-objective-list',
    outline = 'outline',
    audio = 'audio',
    video = 'video',
    board = 'board',
    flowchart = 'flowchart',
    relationReport = 'relation-report',
    drawio = 'drawio'
}

export enum RelationOpenType {
    BLANK = '_blank',
    SELF = '_self'
}

export enum HoveringToolbarActionTypes {
    discussion = 'discussion'
}

export const INVALID_BLOCKS: CustomElementKinds[] = [WikiPluginTypes.layout, WikiPluginTypes.layoutColumn];

export const HAS_BORDER_TYPES: CustomElementKinds[] = [
    ElementKinds.code,
    WikiPluginTypes.attachment,
    WikiPluginTypes.layout,
    WikiPluginTypes.relationPage,
    WikiPluginTypes.relationWorkItem,
    WikiPluginTypes.alert,
    WikiPluginTypes.diagramBoard,
    WikiPluginTypes.relationPageTree,
    WikiPluginTypes.relationTestCase,
    WikiPluginTypes.relationTicket
];

export const RELATED_TYPES: CustomElementKinds[] = [
    WikiPluginTypes.relationPage,
    WikiPluginTypes.relationWorkItem,
    WikiPluginTypes.relationWorkItemList,
    WikiPluginTypes.relationPageTree,
    WikiPluginTypes.relationTestCase,
    WikiPluginTypes.relationTicket
];

export const FULLSCREEN_TOOLBAR_HIDDEN_TYPES: CustomElementKinds[] = [
    WikiPluginTypes.layout,
    WikiPluginTypes.textDiagram,
    WikiPluginTypes.diagramBoard
];

export const FULLSCREEN_EDIT_TYPES: CustomElementKinds[] = [ElementKinds.table, WikiPluginTypes.textDiagram];

export interface WikiEditorOptions extends TheOptions {
    form?: string;
}

export enum ChangeToneType {
    professional = 'professional',
    casual = 'casual',
    straightForward = 'straightForward',
    confident = 'confident',
    friendly = 'friendly'
}

export enum TranslateType {
    zh = 'zh',
    en = 'en'
}

export enum TextOptimizeType {
    longer = 'longer',
    shorter = 'shorter'
}
