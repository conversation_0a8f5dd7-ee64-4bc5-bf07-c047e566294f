export enum CustomPluginKeys {
    common = 'common',
    label = 'label',
    date = 'date',
    emoji = 'emoji',
    formula = 'formula',
    mention = 'mention-member',
    relationPage = 'relation-page',
    relationWorkItem = 'relation-work-item',
    relationWorkItemList = 'relation-work-item-list',
    attachment = 'attachment',
    alert = 'alert',
    layout = 'layout',
    toggleList = 'toggle-list',
    discussion = 'discussion',
    searchReplace = 'search-replace',
    textDiagram = 'text-diagram',
    diagramBoard = 'diagram-board',
    relationPageTree = 'relation-page-tree',
    outline = 'outline',
    audio = 'audio',
    video = 'video',
    relationTestCase = 'relation-test-case',
    relationTestCaseList = 'relation-test-case-list',
    relationIdea = 'relation-idea',
    relationIdeaList = 'relation-idea-list',
    relationTicket = 'relation-ticket',
    relationTicketList = 'relation-ticket-list',
    relationObjective = 'relation-objective',
    relationObjectiveList = 'relation-objective-list',
    pageHistory = 'page-history',
    relationItem = 'relation-item',
    relationReport = 'relation-report',
    drawio = 'drawio'
}
