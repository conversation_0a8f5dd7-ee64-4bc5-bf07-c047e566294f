import { helpers, Is } from '@atinc/ngx-styx/core';
import { REVIEW_PERMISSION_DEFINITION, REVIEW_PERMISSION_GROUP } from '@atinc/ngx-styx/review';

export const PERMISSION_GROUPS = {
    space: {
        key: 'space',
        text: '空间',
        display_position: 1000
    },
    page: {
        display_position: 2000,
        text: `页面设置`,
        key: `page`
    },
    baseline: {
        display_position: 3000,
        text: `基线管理`,
        key: `baseline`
    },
    ...REVIEW_PERMISSION_GROUP
};
export const wikiPermissionDefinition = {
    space_basic_setting: {
        display_position: 1010,
        storage_position: 1010,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_basic_setting`,
        text: `基本设置`
    },
    space_member_setting: {
        display_position: 1020,
        storage_position: 1020,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_member_setting`,
        text: `成员管理`
    },
    space_stencil_setting: {
        display_position: 1030,
        storage_position: 1030,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_stencil_setting`,
        text: `模板管理`
    },
    tag_manage: {
        display_position: 1035,
        storage_position: 1035,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `tag_manage`,
        text: `标签管理`
    },
    manage_automate_rule: {
        display_position: 1040,
        storage_position: 1040,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `manage_automate_rule`,
        text: `自动化规则管理`
    },
    page_share_publicly: {
        display_position: 1045,
        storage_position: 1045,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `page_share_publicly`,
        text: `公开共享`
    },
    space_page_permission_setting: {
        display_position: 1050,
        storage_position: 1050,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_page_permission_setting`,
        text: `页面权限`
    },
    space_recycle_bin_setting: {
        display_position: 1060,
        storage_position: 1060,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_recycle_bin_setting`,
        text: `回收站管理`
    },
    space_addon_setting: {
        display_position: 1065,
        storage_position: 1065,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_addon_setting`,
        text: `组件管理`
    },
    space_review_setting: {
        display_position: 1070,
        storage_position: 1070,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_review_setting`,
        text: `评审配置`
    },
    space_directory_setting: {
        display_position: 1080,
        storage_position: 1080,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_directory_setting`,
        text: `目录管理`
    },
    space_export_statistics_data: {
        display_position: 1090,
        storage_position: 1090,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_export_statistics_data`,
        text: `导出数据明细`
    },
    space_shared_setting: {
        display_position: 1100,
        storage_position: 1100,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_shared_setting`,
        text: `空间共享`
    },
    space_archive_setting: {
        display_position: 1110,
        storage_position: 1110,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_archive_setting`,
        text: `归档/激活空间`
    },
    space_delete_setting: {
        display_position: 1120,
        storage_position: 1120,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_delete_setting`,
        text: `删除空间`
    },
    space_copy: {
        display_position: 1130,
        storage_position: 1130,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_copy`,
        text: `复制空间`
    },
    space_move: {
        display_position: 1140,
        storage_position: 1140,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.space.key,
        key: `space_move`,
        text: `移动空间`
    },
    page_create: {
        display_position: 2010,
        storage_position: 2010,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_create`,
        text: `新建页面/分组`
    },
    page_edit: {
        display_position: 2020,
        storage_position: 2020,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_edit`,
        text: `编辑页面/分组`
    },
    page_copy: {
        display_position: 2030,
        storage_position: 2030,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_copy`,
        text: `复制页面`
    },
    page_move: {
        display_position: 2040,
        storage_position: 2040,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_move`,
        text: `移动页面`
    },
    page_delete: {
        display_position: 2045,
        storage_position: 2045,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_delete`,
        text: `删除页面/分组`
    },

    page_lock: {
        display_position: 2050,
        storage_position: 2050,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_lock`,
        text: `锁定页面`
    },
    page_sharing_setting: {
        display_position: 2060,
        storage_position: 2060,
        admin_default: Is.yes,
        normal_member_default: Is.no,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_sharing_setting`,
        text: `页面共享`
    },
    page_history_manage: {
        display_position: 2070,
        storage_position: 2070,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_history_manage`,
        text: `历史版本管理`
    },
    page_upload_attachment: {
        display_position: 2080,
        storage_position: 2080,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_upload_attachment`,
        text: `附件上传/编辑`
    },
    page_download_attachment: {
        display_position: 2090,
        storage_position: 2090,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_download_attachment`,
        text: `附件下载`
    },
    page_rename_attachment: {
        display_position: 2100,
        storage_position: 2100,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_rename_attachment`,
        text: `附件重命名`
    },
    page_delete_attachment: {
        display_position: 2110,
        storage_position: 2110,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_delete_attachment`,
        text: `附件删除`
    },
    relation_idea: {
        display_position: 2120,
        storage_position: 2120,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `relation_idea`,
        text: `关联产品需求`
    },
    relation_ticket: {
        display_position: 2130,
        storage_position: 2130,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `relation_ticket`,
        text: `关联工单`
    },
    relation_work_item: {
        display_position: 2140,
        storage_position: 2140,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `relation_work_item`,
        text: `关联工作项`
    },
    relation_test_case: {
        display_position: 2150,
        storage_position: 2150,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `relation_test_case`,
        text: `关联测试`
    },
    relation_report: {
        display_position: 2155,
        storage_position: 2155,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `relation_report`,
        text: `关联报表`
    },
    relation_objective: {
        display_position: 2160,
        storage_position: 2160,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `relation_objective`,
        text: `关联目标`
    },
    page_save_as_stencil: {
        display_position: 2170,
        storage_position: 2170,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_save_as_stencil`,
        text: `另存为模板`
    },
    page_export: {
        display_position: 2180,
        storage_position: 2180,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_export`,
        text: `导出页面`
    },
    page_print: {
        display_position: 2190,
        storage_position: 2190,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.page.key,
        key: `page_print`,
        text: `打印页面`
    },
    // 基线管理
    manage_baseline: {
        display_position: 3010,
        storage_position: 3010,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.baseline.key,
        key: `manage_baseline`,
        text: `基线管理`
    },
    baseline_plan_page: {
        display_position: 3020,
        storage_position: 3020,
        admin_default: Is.yes,
        normal_member_default: Is.yes,
        readonly_member_default: Is.no,
        group: PERMISSION_GROUPS.baseline.key,
        key: `baseline_plan_page`,
        text: `规划页面`
    },
    ...REVIEW_PERMISSION_DEFINITION
};

export const globalPermissionDefinition = {
    create_space: {
        display_position: 10,
        storage_position: 10,
        admin_default: Is.yes,
        key: `create_space`,
        text: `新建空间`
    },
    configuration_settings: {
        display_position: 20,
        storage_position: 20,
        admin_default: Is.yes,
        key: `configuration_settings`,
        text: `配置中心`
    }
};

export const wikiPermissionPoints = helpers.getStoragePermissionPoints(wikiPermissionDefinition);

export const wikiGlobalPermissionPoints = helpers.getStoragePermissionPoints(globalPermissionDefinition);
