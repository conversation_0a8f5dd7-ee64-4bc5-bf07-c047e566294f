import { StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

export const SPACE_SIDEBAR_WIDTH = 'page-detail-sidebar-width';
export const SPACE_SIDEBAR_COLLAPSED = 'page-detail-collapse-status';

export const WIKI_FULLSCREEN = 'wiki-fullscreen';
export const WIKI_FULLSCREEN_NODE = `${WIKI_FULLSCREEN}-node`;
export const WIKI_FULLSCREEN_CONTAINER = `${WIKI_FULLSCREEN}-container`;
export const WIKI_FULLSCREEN_TOOLBAR_HIDDEN = `${WIKI_FULLSCREEN}-toolbar-hidden`;

export const WIKI_EDIT_SCROLL_CONTAINER = '.wiki-editor-scroll-container';
export const WIKI_DETAIL_SCROLL_CONTAINER = '.page-fullscreen-container';
export const WIKI_DETAIL_FULLSCREEN_SCROLL_CONTAINER = '.the-editable-container'; // 预览/编辑页全屏

export const getDefaultDocumentTitle = (translate: StyxTranslateService) => {
    return translate.instant<I18nSourceDefinitionType>('wiki.page.untitled.document');
};

export const getDefaultBoardTitle = (translate: StyxTranslateService) => {
    return translate.instant<I18nSourceDefinitionType>('wiki.page.untitled.board');
};

export const DEFAULT_TABLE_TITLE = '无标题';

export const BOARD_EXPORT_CONFIG = {
    autoFitPadding: 16,
    /**
     * A4 纸的宽度是 794px, 但打印时会有左右 80px 的边距，所以这里取 794 - 80 * 2 = 634
     */
    a4Width: 634,
    /**
     * A4 高度
     */
    a4Height: 800
};

export const DEFAULT_BLOCK_CARD_HEIGHT = 460;

export const ICON_NAMESPACE = 'wiki';

export const BOARD_EXPORT_PREFIX = 'PingCode.wiki-图片';
