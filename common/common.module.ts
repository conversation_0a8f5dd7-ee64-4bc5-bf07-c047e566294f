import { ObserversModule } from '@angular/cdk/observers';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { NgxStyxModule } from '@atinc/ngx-styx';
import { PlaitEditorModule } from '@plait-editor/plait-editor.module';
import { ThyProMediaModule } from '@tethys/pro/media';
import {
    TheEditorComponent,
    ThePluginMenuComponent,
    TheToolbarComponent,
    TheToolbarDropdown,
    TheToolbarGroup,
    TheToolbarGroupToken
} from '@worktile/theia';
import { CodemirrorModule } from 'ng-codemirror';
import { ThyColorPickerModule } from 'ngx-tethys/color-picker';
import { SlateModule } from 'slate-angular';
import { PIPES } from './pipe';
import { FileSuffixToStringPipe } from './pipe/file.pipe';
import {
    ColumnAlignPipe,
    ColumnSortAblePipe,
    IsBoardPipe,
    IsDocumentPipe,
    IsPageGroupPipe,
    IsPagePipe,
    IsSamePageIdentifier,
    PageIdOrShortIdToPageIdPipe,
    IsTablePipe,
    PageIconColorPipe,
    PageIconPipe,
    PageListCountPipe,
    PageNameByTypePipe,
    PageTableColumnClassPipe,
    PageTagsVisibilityPipe,
    SubPageCountPipe
} from './pipe/page.pipe';
import { WikiCommonAlertComponent } from './plugins/alert/alert.component';
import { WikiCommonEditorAttachmentComponent } from './plugins/attachment/attachment.component';
import { WikiCommonAttachmentCardComponent } from './plugins/attachment/card/card.component';
import { WikiCommonAttachmentToolbarItemComponent } from './plugins/attachment/quick-toolbar-item.component';
import { WikiCommonAttachmentTextComponent } from './plugins/attachment/text/text.component';
import { WikiCommonAudioComponent } from './plugins/audio/audio.component';
import { WikiCommonDateComponent } from './plugins/date/date.component';
import { WikiCommonDiagramBoardComponent } from './plugins/diagram-board/diagram-board.component';
import { WikiCommonDrawioComponent } from './plugins/drawio/drawio.component';
import { WikiCommonDiscussionLeafComponent } from './plugins/discussion/leaf/leaf.component';
import { WikiCommonDndComponent } from './plugins/dnd/component/dnd.component';
import { WikiCommonEmojiComponent } from './plugins/emoji/emoji.component';
import { WikiCommonEmojiSuggestionComponent } from './plugins/emoji/suggestion.component';
import { WikiCommonFormulaEditComponent } from './plugins/formula/edit/edit.component';
import { WikiCommonFormulaComponent } from './plugins/formula/formula.component';
import { WikiCommonLabelEditComponent } from './plugins/label/edit/label-edit.component';
import { WikiCommonLabelComponent } from './plugins/label/label.component';
import { WikiCommonLayoutColumnComponent } from './plugins/layout/component/column/column.component';
import { WikiCommonLayoutComponent } from './plugins/layout/component/layout.component';
import { WikiCommonMentionComponent } from './plugins/mention/mention.component';
import { WikiCommonMentionSuggestionComponent } from './plugins/mention/suggestion.component';
import { WikiCommonOutlineComponent } from './plugins/outline/outline.component';
import { WikiCommonOutlineToolbarComponent } from './plugins/outline/toolbar/outline-toolbar.component';
import { WikiCommonPreviewFullscreenComponent } from './plugins/preview-toolbar/preview-toolbar.component';
import { WikiCommonRelationPageTreeComponent } from './plugins/relation-page-tree/relation-page-tree.component';
import { WikiCommonRelationPageTreeToolbarComponent } from './plugins/relation-page-tree/toolbar/relation-page-tree-toolbar.component';
import { WikiCommonRelationCardComponent } from './plugins/relation-page/card/card.component';
import { WikiCommonRelationPageComponent } from './plugins/relation-page/relation-page.component';
import { WikiCommonSearchHighlightingLeafComponent } from './plugins/search-replace/search-highlighting-leaf.component';
import { WikiCommonSearchReplaceToolbarItemComponent } from './plugins/search-replace/toolbar-item.component';
import { WikiCommonSharedTextComponent } from './plugins/shared/text/text.component';
import { WikiCommonTextDiagramComponent } from './plugins/text-diagram/text-diagram.component';
import { WikiCommonTextDiagramToolbarComponent } from './plugins/text-diagram/toolbar/text-diagram-toolbar.component';
import { WikiCommonToggleListItemComponent } from './plugins/toggle-list/components/list-item/list-item.component';
import { WikiCommonToggleListComponent } from './plugins/toggle-list/components/list/list.component';
import { WikiCommonVideoComponent } from './plugins/video/video.component';

import { ScrollingModule } from '@angular/cdk/scrolling';
import { WikiCommonSharedCaretComponent } from './components/caret/caret.component';
import {
    CommonBackTopComponent,
    CommonEmptyContentComponent,
    CommonPageBreadComponent,
    CommonPropertySettingComponent,
    CommonRelationItemCardComponent,
    CommonRelationItemComponent,
    CommonRelationItemSuggestionComponent,
    CommonRelationItemTextComponent,
    CommonRelationListComponent,
    CommonRelationMenuComponent,
    CommonShortcutsHelpComponent,
    CommonTocComponent,
    Material,
    SideDiscussionsComponent,
    WikiCommonEditComponent,
    WikiCommonExpandingToolbarComponent,
    WikiCommonFileUploadingItemsComponent,
    WikiCommonFullscreenHeaderComponent,
    WikiCommonGroupToolbarItemComponent,
    WikiCommonPreviewComponent,
    WikiCommonSpaceSidebarComponent,
    WikiEditBoardComponent,
    WikiEditDocumentComponent,
    WikiPageTagsComponent,
    WikiPreviewBoardComponent,
    WikiPreviewDocumentComponent
} from './components/index';
import { ElementAttributePipe } from './pipe/alert.pipe';
import { WikiCommonHeadingHandleComponent } from './plugins/heading-handle/heading-handle.component';
import { WikiRelationReportComponent } from './plugins/relation-report/relation-report.component';
import { NgxPlanetModule } from '@worktile/planet';
import { WikiCommonGraphEditorComponent } from './plugins/drawio/graph-editor/graph-editor.component';

const COMPONENTS = [
    CommonBackTopComponent,
    CommonTocComponent,
    CommonPageBreadComponent,
    CommonEmptyContentComponent,
    CommonPropertySettingComponent,
    CommonRelationListComponent,
    CommonRelationItemComponent,
    CommonRelationItemSuggestionComponent,
    CommonRelationMenuComponent,
    CommonRelationItemCardComponent,
    CommonShortcutsHelpComponent,
    SideDiscussionsComponent,
    WikiCommonEditorAttachmentComponent,
    WikiCommonAudioComponent,
    WikiCommonVideoComponent,
    WikiCommonAttachmentCardComponent,
    WikiCommonAttachmentTextComponent,
    WikiCommonLabelEditComponent,
    WikiCommonLabelComponent,
    WikiCommonDateComponent,
    WikiCommonSharedCaretComponent,
    WikiCommonSharedTextComponent,
    WikiCommonMentionComponent,
    WikiCommonMentionSuggestionComponent,
    WikiCommonRelationPageComponent,
    WikiCommonRelationCardComponent,
    WikiCommonGroupToolbarItemComponent,
    WikiCommonAlertComponent,
    WikiCommonLayoutComponent,
    WikiCommonLayoutColumnComponent,
    WikiCommonExpandingToolbarComponent,
    WikiCommonDiscussionLeafComponent,
    WikiCommonAttachmentToolbarItemComponent,
    WikiCommonSearchReplaceToolbarItemComponent,
    WikiCommonSearchHighlightingLeafComponent,
    WikiCommonDndComponent,
    WikiCommonHeadingHandleComponent,
    WikiCommonToggleListComponent,
    WikiCommonToggleListItemComponent,
    WikiCommonSpaceSidebarComponent,
    WikiCommonTextDiagramComponent,
    WikiCommonTextDiagramToolbarComponent,
    WikiCommonFormulaEditComponent,
    WikiCommonFormulaComponent,
    WikiCommonFullscreenHeaderComponent,
    WikiCommonPreviewFullscreenComponent,
    WikiCommonDiagramBoardComponent,
    WikiCommonDrawioComponent,
    WikiCommonGraphEditorComponent,
    WikiCommonEmojiComponent,
    WikiCommonEmojiSuggestionComponent,
    WikiCommonRelationPageTreeComponent,
    WikiCommonRelationPageTreeToolbarComponent,
    WikiRelationReportComponent,
    WikiCommonOutlineToolbarComponent,
    WikiCommonOutlineComponent,
    WikiEditDocumentComponent,
    WikiEditBoardComponent,
    WikiCommonEditComponent,
    WikiPreviewDocumentComponent,
    WikiPreviewBoardComponent,
    WikiCommonPreviewComponent,
    WikiPageTagsComponent,
    WikiCommonFileUploadingItemsComponent,
    CommonRelationItemTextComponent,
    Material
];

const standalonePipes = [
    PageIconColorPipe,
    PageIconPipe,
    IsPageGroupPipe,
    IsDocumentPipe,
    FileSuffixToStringPipe,
    IsBoardPipe,
    IsPagePipe,
    IsTablePipe,
    ColumnAlignPipe,
    ColumnSortAblePipe,
    PageTableColumnClassPipe,
    PageListCountPipe,
    PageNameByTypePipe,
    PageTagsVisibilityPipe,
    SubPageCountPipe,
    ElementAttributePipe,
    IsSamePageIdentifier,
    PageIdOrShortIdToPageIdPipe
];

@NgModule({
    imports: [
        NgxPlanetModule,
        CommonModule,
        ScrollingModule,
        NgxStyxModule,
        SlateModule,
        TheEditorComponent,
        TheToolbarComponent,
        TheToolbarDropdown,
        ThePluginMenuComponent,
        CodemirrorModule,
        PlaitEditorModule,
        ObserversModule,
        ThyProMediaModule,
        ThyColorPickerModule,
        ...standalonePipes
    ],
    exports: [NgxPlanetModule, CommonModule, NgxStyxModule, SlateModule, TheToolbarDropdown, ...PIPES, ...standalonePipes, ...COMPONENTS],
    declarations: [...PIPES, ...COMPONENTS],
    providers: [
        {
            provide: TheToolbarGroupToken,
            useValue: TheToolbarGroup
        },
        IsDocumentPipe,
        IsBoardPipe,
        IsPagePipe,
        IsTablePipe,
        IsPageGroupPipe
    ]
})
export class WikiCommonModule {}
