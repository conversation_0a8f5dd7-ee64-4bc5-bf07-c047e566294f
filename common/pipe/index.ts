import { DurationPipe } from './duration.pipe';
import { WikiPageBreadcrumbPipe } from './page-bread.pipe';
import { ScopeTypeIsOrganizationPipe, ScopeTypeIsPersonalPipe, ScopeTypeIsTeamPipe } from './scope-type.pipe';
import { VisibilityIsPrivatePipe, VisibilityIsPublicPipe } from './visibility.pipe';

export * from './alert.pipe';
export * from './duration.pipe';
export * from './file.pipe';
export * from './page-bread.pipe';
export * from './page.pipe';
export * from './scope-type.pipe';
export * from './visibility.pipe';

export const PIPES = [
    DurationPipe,
    WikiPageBreadcrumbPipe,
    VisibilityIsPrivatePipe,
    VisibilityIsPublicPipe,
    ScopeTypeIsOrganizationPipe,
    ScopeTypeIsTeamPipe,
    ScopeTypeIsPersonalPipe
];
