import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'duration',
    standalone: false
})
export class DurationPipe implements PipeTransform {
    transform(time: number) {
        if (!time) {
            return '00:00';
        }
        // 转换为时分秒
        const h = parseInt(`${(time / 60 / 60) % 24}`);
        const hour = h < 10 ? '0' + h : h;
        const m = parseInt(`${(time / 60) % 60}`);
        const minute = m < 10 ? '0' + m : m;
        const s = parseInt(`${time % 60}`);
        const second = s < 10 ? '0' + s : s;
        let result = `${minute}:${second}`;
        if (h) {
            result = `${hour}:${result}`;
        }
        return result;
    }
}
