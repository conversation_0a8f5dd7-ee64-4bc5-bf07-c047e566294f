import { inject, Pipe, PipeTransform } from '@angular/core';
import { AlertElement } from '../custom-types';
import { getAlertTypes } from '../plugins/alert/entities';
import { StyxTranslateService } from '@atinc/ngx-styx';

@Pipe({
    name: 'elementAttribute',
    standalone: true
})
export class ElementAttributePipe implements PipeTransform {
    translate = inject(StyxTranslateService);

    transform(element: AlertElement, type: string): string {
        const alertItem = getAlertTypes(this.translate).find(item => item.alertType === element.alertType);
        if (type === 'icon') {
            return element?.icon ?? alertItem?.icon ?? '';
        }
        if (type === 'color') {
            return element?.color ?? alertItem?.color ?? 'transparent';
        }
    }
}
