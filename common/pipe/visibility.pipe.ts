import { Pipe, PipeTransform } from '@angular/core';
import { Visibility } from '@atinc/ngx-styx/core';

@Pipe({
    name: 'visibilityIsPrivate',
    standalone: false
})
export class VisibilityIsPrivatePipe implements PipeTransform {
    transform(value: Visibility) {
        return value === Visibility.private;
    }
}

@Pipe({
    name: 'visibilityIsPublic',
    standalone: false
})
export class VisibilityIsPublicPipe implements PipeTransform {
    transform(value: Visibility) {
        return value === Visibility.public;
    }
}
