import { Pipe, PipeTransform } from '@angular/core';
import { PageInfo } from '@wiki/app/entities/page-info';
import { keyBy } from 'ngx-tethys/util';

@Pipe({
    name: 'pageBreadcrumb',
    standalone: false
})
export class WikiPageBreadcrumbPipe implements PipeTransform {
    transform(pageId: string, pages: PageInfo[]): PageInfo {
        if (pages) {
            const pagesMap = keyBy([...pages], '_id');
            if (pagesMap[pageId]) {
                return pagesMap[pageId];
            } else {
                return null;
            }
        }
    }
}
