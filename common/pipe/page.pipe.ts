import { Pipe, PipeTransform } from '@angular/core';
import { PropertyType, StyxTranslateService, hasPermissionTransform, helpers } from '@atinc/ngx-styx';
import { PageTypes } from '@wiki/app/constants/page';
import { ColumnInfo, PageInfo } from '@wiki/app/entities';
import { wikiPermissionDefinition, wikiPermissionPoints } from '../constants';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Pipe({
    name: 'pageIconColor',
    standalone: true
})
export class PageIconColorPipe implements PipeTransform {
    transform(page: { type: PageTypes } & PageTypes) {
        switch (page?.type || page) {
            case PageTypes.group:
                return '#F6C659';
            case PageTypes.board:
                return '#F98A7C';
            case PageTypes.table:
                return '#3DD1AE';
            default:
                return '#9C9CFB';
        }
    }
}

@Pipe({
    name: 'subPageCount',
    standalone: true
})
export class SubPageCountPipe implements PipeTransform {
    transform(operationPages: PageInfo[], noneSubPage?: boolean) {
        let count = 0;
        const pages = helpers.cloneDeep(operationPages) as PageInfo[];
        const ids = helpers.map(pages, item => item._id);
        pages.forEach(page => {
            if (page.parent_ids.length > 0 && helpers.intersection(ids, page.parent_ids).length > 0) {
                count++;
            }
        });
        return noneSubPage ? operationPages.length - count : count;
    }
}

@Pipe({
    name: 'pageIcon',
    standalone: true
})
export class PageIconPipe implements PipeTransform {
    transform(page: { type: PageTypes } & PageTypes) {
        switch (page?.type || page) {
            case PageTypes.group:
                return 'folder-fill';
            case PageTypes.board:
                return 'sketchpad-fill';
            case PageTypes.table:
                return 'ai-table-fill';
            default:
                return 'file-fill';
        }
    }
}

@Pipe({
    name: 'isPageGroup',
    standalone: true
})
export class IsPageGroupPipe implements PipeTransform {
    transform(page: { type: PageTypes }) {
        return page && page.type === PageTypes.group;
    }
}

@Pipe({ name: 'isPage', standalone: true })
export class IsPagePipe implements PipeTransform {
    transform(page: { type: PageTypes }) {
        return page && [PageTypes.document, PageTypes.board, PageTypes.table].includes(page.type);
    }
}

@Pipe({ name: 'isDocument', standalone: true })
export class IsDocumentPipe implements PipeTransform {
    transform(page: { type: PageTypes }) {
        return page && page.type === PageTypes.document;
    }
}

@Pipe({ name: 'isBoard', standalone: true })
export class IsBoardPipe implements PipeTransform {
    transform(page: { type: PageTypes }) {
        return page && page.type === PageTypes.board;
    }
}

@Pipe({ name: 'isTable', standalone: true })
export class IsTablePipe implements PipeTransform {
    transform(page: { type: PageTypes }) {
        return page && page.type === PageTypes.table;
    }
}

@Pipe({ name: 'pageNameByType', standalone: true })
export class PageNameByTypePipe implements PipeTransform {
    constructor(public translate: StyxTranslateService) {}

    transform(page: { type: PageTypes } & PageTypes) {
        switch (page?.type || page) {
            case PageTypes.group:
                return this.translate.instant<I18nSourceDefinitionType>('wiki.page.group', { isPlural: false, isTitle: true });
            case PageTypes.board:
                return this.translate.instant<I18nSourceDefinitionType>('styx.paintBoard', { isPlural: false, isTitle: true });
            case PageTypes.document:
                return this.translate.instant<I18nSourceDefinitionType>('styx.document', { isPlural: false, isTitle: true });
            case PageTypes.table:
                return this.translate.instant<I18nSourceDefinitionType>('wiki.page.table', { isPlural: false, isTitle: true });
        }
    }
}

/**
 * 获取去除分组后的页面数
 * @returns 页面数
 */
@Pipe({ name: 'pageListCount', standalone: true })
export class PageListCountPipe implements PipeTransform {
    constructor(private isPageGroupPipe: IsPageGroupPipe) {}

    transform(pages: PageInfo[] = []) {
        return (pages ?? []).filter(node => !this.isPageGroupPipe.transform({ type: node.type })).length ?? 0;
    }
}

@Pipe({ name: 'columnAlign', standalone: true })
export class ColumnAlignPipe implements PipeTransform {
    transform(column: ColumnInfo) {
        const alignLeftPropertyKeys = ['principal_version'];
        if (column.type === PropertyType.number || alignLeftPropertyKeys.includes(column.key)) {
            return 'left';
        }
    }
}

@Pipe({ name: 'columnSortAble', standalone: true })
export class ColumnSortAblePipe implements PipeTransform {
    transform(column: ColumnInfo, sortableColumnKeys: string[]) {
        if (sortableColumnKeys.includes(column.key)) {
            return true;
        } else {
            return false;
        }
    }
}

@Pipe({ name: 'pageTableColumnClass', standalone: true })
export class PageTableColumnClassPipe implements PipeTransform {
    transform(columnKey: string, quickEditColumnKey: string[], propertyType: PropertyType): string {
        if (!quickEditColumnKey.includes(columnKey) && propertyType !== PropertyType.textarea) {
            return 'wiki-table-not-quick-editor-property';
        } else {
            return '';
        }
    }
}

@Pipe({ name: 'pageTagsVisibility', standalone: true })
export class PageTagsVisibilityPipe implements PipeTransform {
    transform(page: PageInfo, versionId?: string): boolean {
        const pageEditable = hasPermissionTransform(page?.permissions, wikiPermissionPoints, wikiPermissionDefinition.page_edit);
        const isDeleted = page?.is_deleted;

        return !!page?.refs?.tags?.length || (pageEditable && !isDeleted && !versionId);
    }
}

@Pipe({ name: 'isSamePageIdentifier', standalone: true })
export class IsSamePageIdentifier implements PipeTransform {
    transform(page: PageInfo, pageIdOrShortId: string): boolean {
        return page && (page?.short_id === pageIdOrShortId || page?._id === pageIdOrShortId);
    }
}

@Pipe({ name: 'pageIdOrShortIdToPageId', standalone: true })
export class PageIdOrShortIdToPageIdPipe implements PipeTransform {
    transform(pageIdOrShortId: string, tree: any[]): string {
        const page = tree.find(i => i?.short_id === pageIdOrShortId || i._id === pageIdOrShortId);
        return page?._id;
    }
}
