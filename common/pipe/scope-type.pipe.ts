import { Pipe, PipeTransform } from '@angular/core';
import { PilotScopeType } from '@atinc/ngx-styx/core';

@Pipe({
    name: 'scopeTypeIsOrganization',
    standalone: false
})
export class ScopeTypeIsOrganizationPipe implements PipeTransform {
    transform(value: PilotScopeType) {
        return value === PilotScopeType.organization;
    }
}

@Pipe({
    name: 'scopeTypeIsTeam',
    standalone: false
})
export class ScopeTypeIsTeamPipe implements PipeTransform {
    transform(value: PilotScopeType) {
        return value === PilotScopeType.team;
    }
}

@Pipe({
    name: 'scopeTypeIsPersonal',
    standalone: false
})
export class ScopeTypeIsPersonalPipe implements PipeTransform {
    transform(value: PilotScopeType) {
        return value === PilotScopeType.personal;
    }
}
