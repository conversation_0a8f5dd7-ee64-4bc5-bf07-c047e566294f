import { Overlay } from '@angular/cdk/overlay';
import { AfterViewInit, Component, DestroyRef, inject, OnD<PERSON>roy, OnInit, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AppRootContext, AttachmentEntity, StyxFileUploader, UtilService } from '@atinc/ngx-styx';
import { ATTACHMENT_TOOLBAR_REF } from '@wiki/common/constants/default';
import { FileElement } from '@wiki/common/custom-types';
import { PageAttachmentEntity } from '@wiki/common/interface';
import { AttachmentContext } from '@wiki/common/services/context/attachment-context';
import { WIKI_ATTACHMENT_CONTEXT } from '@wiki/common/services/context/attachment-context.token';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { isMobileMode } from '@wiki/common/util/extension-mode';
import { getMode, TheBaseElement, TheContextService, TheEditor, TheQueries, topLeftPosition, updatePopoverPosition } from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { filter, finalize } from 'rxjs/operators';
import { Editor, Element, Node, Transforms } from 'slate';
import { BeforeContextChange, SlateElementContext } from 'slate-angular';
import { NODE_TO_ELEMENT } from 'slate-dom';
import { BaseFileEditor } from '../file/base-file.editor';

@Component({
    selector: 'WikiBaseFileElementComponent',
    template: '',
    standalone: false
})
export class WikiBaseFileElementComponent
    extends TheBaseElement<FileElement, Editor>
    implements OnInit, AfterViewInit, OnDestroy, BeforeContextChange<SlateElementContext>
{
    toolbarPopoverRef: ThyPopoverRef<any>;

    fileSrc: string;

    isDanger: boolean;

    isPlaceholder = false;

    disabled = false;

    isUploading = false;

    isUploadFailed = false;

    fileEntity: AttachmentEntity;

    isToolbarOpen = false;

    private uploadingFile?: {
        url: string;
        file: File | string;
    };

    get scopeId() {
        return `wiki-common-editor-attachments-${this.element?.key}`;
    }

    public get isTextMode(): boolean {
        return this.element.mode === 'text';
    }

    @ViewChild('toolbar', { read: TemplateRef, static: true })
    toolbar: TemplateRef<any>;

    private util = inject(UtilService);
    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);
    private destroyRef = inject(DestroyRef);
    private fileUploader = inject(StyxFileUploader);
    private appRootContext = inject(AppRootContext);
    public viewContainerRef = inject(ViewContainerRef);
    private theContextService = inject(TheContextService);
    protected attachmentContext = inject<AttachmentContext>(WIKI_ATTACHMENT_CONTEXT);

    beforeContextChange = (value: SlateElementContext<Element>) => {
        if (value.element !== this.element && value.element._id) {
            this.setFile(value.element);
        }
    };

    ngOnInit() {
        super.ngOnInit();
        this.uploadingFile = this.theContextService.uploadingFiles.find(item => item.url === this.element.url);
        const file = this.uploadingFile?.file;
        const uploadingItems = this.fileUploader.getUploadingItems(this.scopeId);
        const { _id } = this.element;
        if (!this.readonly && uploadingItems.length === 0 && file && file instanceof File) {
            this.uploadFile({ files: [file] });
        }
        if ((this.readonly || !(file instanceof File)) && !_id) {
            this.isPlaceholder = true;
        }

        this.subscribeFileChange();
    }

    ngAfterViewInit() {
        this.mediaPlayChange();
    }

    subscribeFileChange() {
        this.attachmentContext
            .attachmentChange(this.element._id)
            .pipe(
                filter((attachment: PageAttachmentEntity) => !!attachment),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe((attachment: PageAttachmentEntity) => {
                this.setFileAndState(attachment);
                this.cdr.detectChanges();
            });
    }

    setFile(element: Element) {
        if (!element || !element._id) {
            return;
        }

        let file = this.attachmentContext.getAttachmentById(element._id as string);
        if (file) {
            this.setFileAndState(file);
            return;
        }

        this.attachmentContext
            .getPageAttachments()
            .pipe(
                finalize(() => {
                    this.isPlaceholder = false;
                    if (!file) {
                        this.disabled = true;
                    }
                    this.cdr.detectChanges();
                })
            )
            .subscribe((attachments: AttachmentEntity[]) => {
                if (!attachments) {
                    return;
                }
                file = attachments.find((attachment: AttachmentEntity) => attachment?._id === element._id);
                if (file) {
                    let addAttachment$ =
                        this.element.type === WikiPluginTypes.attachment
                            ? this.attachmentContext.addAttachment(file)
                            : this.attachmentContext.addAttachment(file, true, 'embedment');
                    addAttachment$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe();
                    this.setFileAndState(file);
                }
            });
    }

    setFileAndState(file: AttachmentEntity) {
        const { config } = this.appRootContext.globalInfo;
        this.fileEntity = file;
        this.disabled = false;
        this.isUploadFailed = false;
        this.fileSrc = `${config.box.baseUrl}/file/origin-url?version=${this.fileEntity.addition.current_version}&action=download&token=${this.fileEntity.token}`;
    }

    uploadFile(event: { files: File[] }) {
        this.isUploading = true;
        this.theContextService.uploadingStatus$.next(true);
        this.fileUploader
            .uploadAnonymousFiles(this.scopeId, event.files, {
                keepInQueue: false,
                onDone: file => this.addFileToEditor(file)
            })
            .subscribe({
                error: error => {
                    this.theContextService.removeUploadFile(this.uploadingFile);
                    this.uploadingFile = null;
                    setTimeout(() => this.theContextService.uploadingStatus$.next(false), 0);
                    this.util.defaultErrorHandler()(error);
                }
            });
    }

    addFileToEditor(attachment: AttachmentEntity) {
        this.theContextService.removeUploadFile(this.uploadingFile);
        this.uploadingFile = null;
        setTimeout(() => this.theContextService.uploadingStatus$.next(false), 0);

        if (!attachment) {
            this.isUploadFailed = true;
            this.isUploading = false;
            return;
        }

        this.isPlaceholder = true;
        let addAttachment$ =
            this.element.type === WikiPluginTypes.attachment
                ? this.attachmentContext.addAttachment(attachment)
                : this.attachmentContext.addAttachment(attachment, false, 'embedment');

        addAttachment$.subscribe((data: AttachmentEntity) => {
            Transforms.setNodes(
                this.editor,
                {
                    type: (this.element as FileElement).type,
                    _id: data?._id,
                    children: [{ text: '' }]
                },
                { at: TheEditor.findPath(this.editor as TheEditor, this.element) }
            );
            this.isUploading = false;
            this.isPlaceholder = false;
        });
    }

    getMediaElement() {
        let el: HTMLMediaElement;
        if (this.element.type === WikiPluginTypes.audio) {
            el = this.elementRef.nativeElement?.querySelector('audio');
        }
        if (this.element.type === WikiPluginTypes.video) {
            el = this.elementRef.nativeElement?.querySelector('video');
        }
        return el;
    }

    getAllMediaElement() {
        let medias: HTMLMediaElement[] = [];
        const mediaNodeEntry = Array.from(Node.nodes(this.editor));
        const mediaNodes = mediaNodeEntry.filter(
            entry => entry[0].type && [WikiPluginTypes.audio, WikiPluginTypes.video].includes(entry[0].type as WikiPluginTypes)
        );
        const mediaElements = mediaNodes.map(entry => NODE_TO_ELEMENT.get(entry[0]));
        for (let i = 0, len = mediaElements.length; i < len; i++) {
            let audio = mediaElements[i]?.querySelector('audio');
            let video = mediaElements[i]?.querySelector('video');
            audio && medias.push(audio);
            video && medias.push(video);
        }
        return medias;
    }

    mediaPlayChange() {
        const currentMedia = this.getMediaElement();

        currentMedia?.addEventListener('play', () => {
            const mediaElements = this.getAllMediaElement();
            for (let i = 0, len = mediaElements.length; i < len; i++) {
                mediaElements[i] !== currentMedia && mediaElements[i]?.pause();
            }
        });
    }

    openToolbar() {
        if (isMobileMode(getMode(this.editor)) || !TheQueries.isGlobalCollapsed(this.editor) || this.isToolbarOpen) {
            return;
        }

        const origin = this.elementRef?.nativeElement as HTMLElement;
        this.toolbarPopoverRef = this.thyPopover.open(this.toolbar, {
            origin,
            viewContainerRef: this.viewContainerRef,
            minWidth: 0,
            placement: 'topLeft',
            panelClass: 'the-plugin-toolbar-popover',
            hasBackdrop: false,
            manualClosure: true,
            insideClosable: false,
            outsideClosable: false,
            scrollStrategy: this.overlay.scrollStrategies.reposition(),
            autoAdaptive: true
        });
        this.toolbarPopoverRef
            ?.beforeClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this.isToolbarOpen = false;
                ATTACHMENT_TOOLBAR_REF.set(this.editor, null);
            });
        this.toolbarPopoverRef
            ?.afterOpened()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this.isToolbarOpen = true;
                ATTACHMENT_TOOLBAR_REF.set(this.editor, this.toolbarPopoverRef);
            });

        const overlayRef = this.toolbarPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    closeToolbar() {
        if (this.isToolbarOpen) {
            this.toolbarPopoverRef?.close();
            this.isDanger = false;
        }
    }

    deleteFile(event: MouseEvent | AttachmentEntity) {
        BaseFileEditor.removeFile(this.editor, this.element);
    }

    ngOnDestroy() {
        super.ngOnDestroy();
        this.closeToolbar();

        if (this.uploadingFile) {
            this.theContextService.removeUploadFile(this.uploadingFile);
        }
        if (this.isUploading) {
            this.theContextService.uploadingStatus$.next(false);
        }
    }

    onEnterDelete() {
        this.isDanger = true;
    }

    onLeaveDelete() {
        this.isDanger = false;
    }
}
