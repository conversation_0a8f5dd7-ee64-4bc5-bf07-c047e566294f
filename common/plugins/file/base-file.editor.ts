import { StyxAttachmentUploadValidator } from '@atinc/ngx-styx';
import { Id } from '@atinc/ngx-styx/core';
import { FileElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import {
    createEmptyParagraph,
    ElementKinds,
    getPluginOptions,
    TheContextService,
    TheEditor,
    TheQueries,
    TheTransforms
} from '@worktile/theia';
import { Editor, Element, Transforms } from 'slate';
import { AudioPluginOptions } from '../audio/audio.plugin';
import { VideoPluginOptions } from '../video/video.plugin';

export interface attachmentNode {
    type: WikiPluginTypes;
    _id: string;
    url: string;
    children: [{ text: '' }];
    mode?: 'card' | 'text';
}

export const BaseFileEditor = {
    isCard(element: FileElement) {
        return element.mode === 'card';
    },
    openUpload(editor: TheEditor, fileType?: WikiPluginTypes) {
        const inputFile = document.createElement('input');
        inputFile.setAttribute('type', 'file');
        inputFile.setAttribute('multiple', 'multiple');
        if (fileType) {
            switch (fileType) {
                case WikiPluginTypes.audio:
                    const audioTypes = getPluginOptions<AudioPluginOptions>(editor, CustomPluginKeys.audio)?.audioTypes;
                    inputFile.setAttribute('accept', audioTypes.map(i => `.${i}`).toString());
                    break;
                case WikiPluginTypes.video:
                    const videoTypes = getPluginOptions<VideoPluginOptions>(editor, CustomPluginKeys.video)?.videoTypes;
                    inputFile.setAttribute('accept', videoTypes.map(i => `.${i}`).toString());
                    break;
                default:
                    inputFile.setAttribute('accept', `${fileType}/*`);
            }
        }
        inputFile.setAttribute('id', 'uploadFile');
        inputFile.onchange = (event: Event) => {
            const files = (event.target as HTMLInputElement).files;
            const attachments: File[] = [];
            for (const file of files) {
                attachments.push(file);
            }
            BaseFileEditor.addFile(editor, attachments, fileType);
        };
        inputFile.click();
    },
    addIdFile(editor: TheEditor, ids: Id[]) {
        if (!ids.length && !editor.selection.anchor && !editor.selection.focus) {
            return;
        }
        if (ids.length) {
            const fileNodes = ids.map(id => {
                return {
                    type: WikiPluginTypes.attachment,
                    _id: id,
                    mode: 'text',
                    children: [{ text: '' }]
                };
            }) as FileElement[];

            if (fileNodes.length) {
                Transforms.insertNodes(editor, fileNodes, { at: editor.selection, select: true });
                TheEditor.focus(editor);
            }
        }
    },

    addFile(editor: TheEditor, files: File[], fileType?: WikiPluginTypes) {
        if (!files.length && !editor.selection.anchor && !editor.selection.focus) {
            return;
        }
        const uploadValidator = editor.injector.get(StyxAttachmentUploadValidator);
        const validateFiles = uploadValidator.validateHandler(files.map(item => item)).files;

        if (validateFiles.length) {
            const contextService = editor.injector.get(TheContextService);
            const fileNodes = validateFiles.map(file => {
                const url = URL.createObjectURL(file);
                contextService.addUploadingFiles({ url, file });
                return {
                    type: fileType ?? WikiPluginTypes.attachment,
                    _id: undefined,
                    url,
                    children: [{ text: '' }],
                    mode: fileType ? 'card' : 'text'
                };
            }) as FileElement[];

            if (fileNodes.length) {
                if (fileType) {
                    TheTransforms.insertInlineElement(editor, fileNodes as any);
                } else {
                    const insertPath = TheQueries.getInsertElementsPath(editor, [ElementKinds.paragraph]);
                    const at = insertPath ? editor.selection.anchor : Editor.after(editor, editor.selection.anchor.path);
                    Transforms.insertNodes(editor, fileNodes, { at, select: true });
                }
            }
        }
    },
    removeFile(editor: TheEditor, element: Element) {
        Transforms.move(editor, { reverse: true });
        const selection = editor.selection;
        const path = TheEditor.findPath(editor, element);
        Transforms.removeNodes(editor, { at: path });
        if (Editor.isBlock(editor, element)) {
            Transforms.insertNodes(editor, createEmptyParagraph(), { at: path });
            Transforms.select(editor, path);
        } else {
            Transforms.select(editor, selection);
        }
        TheEditor.focus(editor);
    },
    wrapText(editor: TheEditor, element: Element) {
        Editor.withoutNormalizing(editor, () => {
            Transforms.setNodes(editor, { type: ElementKinds.paragraph, mode: null, _id: null });
            Transforms.insertNodes(editor, {
                type: element.type,
                mode: 'text',
                _id: element._id,
                children: [
                    {
                        text: ''
                    }
                ]
            } as FileElement);
            TheEditor.focus(editor);
        });
    }
};
