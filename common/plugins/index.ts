import { ThePlugin } from '@worktile/theia';
import { createAIPlugin } from './ai/ai.plugin';
import { createAlertPlugin } from './alert/alert.plugin';
import { createAttachmentPlugin } from './attachment';
import { createAudioPlugin } from './audio/audio.plugin';
import { createCommonPlugin } from './common.plugin';
import { createDatePlugin } from './date/date.plugin';
import { createDiagramBoardPlugin } from './diagram-board/diagram-board.plugin';
import { createDiscussionPlugin } from './discussion/discussion.plugin';
import { createEmojiPlugin } from './emoji/emoji.plugin';
import { createFormulaPlugin } from './formula/formula.plugin';
import { createLabelPlugin } from './label/label.plugin';
import { createLayoutPlugin } from './layout/layout.plugin';
import { createOutlinePlugin } from './outline/outline.plugin';
import { createRelationIdeaListPlugin } from './relation-idea-list/relation-idea-list.plugin';
import { createRelationIdeaPlugin } from './relation-idea/relation-idea.plugin';
import { createRelationObjectiveListPlugin } from './relation-objective-list/relation-objective-list.plugin';
import { createRelationObjectivePlugin } from './relation-objective/relation-objective.plugin';
import { createRelationPagePlugin } from './relation-page';
import { createRelationPageTreePlugin } from './relation-page-tree/relation-page-tree.plugin';
import { createRelationTestCaseListPlugin } from './relation-test-case-list/relation-test-case-list.plugin';
import { createRelationTestCasePlugin } from './relation-test-case/relation-test-case.plugin';
import { createRelationTicketListPlugin } from './relation-ticket-list/relation-ticket-list.plugin';
import { createRelationTicketPlugin } from './relation-ticket/relation-ticket.plugin';
import { createRelationWorkItemPlugin } from './relation-work-item';
import { createRelationWorkItemListPlugin } from './relation-work-item-list/relation-work-item-list.plugin';
import { createSearchReplacePlugin } from './search-replace/search-replace.plugin';
import { createTextDiagramPlugin } from './text-diagram/text-diagram.plugin';
import { createToggleListPlugin } from './toggle-list/toggle-list.plugin';
import { createVideoPlugin } from './video/video.plugin';
import { createMentionPlugin } from './mention/mention.plugin';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { createRelationReportPlugin } from './relation-report/relation-report.plugin';
import { createDrawioPlugin } from './drawio/drawio.plugin';

export const WikiCommonPlugins = (translateService: StyxTranslateService): ThePlugin[] => [
    createCommonPlugin(translateService),
    createLabelPlugin(translateService),
    createDatePlugin(translateService),
    createRelationPagePlugin(translateService),
    createRelationWorkItemPlugin(translateService),
    createRelationWorkItemListPlugin(translateService),
    ...createMentionPlugin(translateService),
    createAttachmentPlugin(translateService),
    createAlertPlugin(translateService),
    createDiscussionPlugin(),
    createLayoutPlugin(translateService),
    createSearchReplacePlugin(translateService),
    createToggleListPlugin(translateService),
    createEmojiPlugin(translateService), // must be before plugins that include onkeydown events
    createFormulaPlugin(translateService),
    createTextDiagramPlugin(translateService),
    createDiagramBoardPlugin(translateService),
    createRelationPageTreePlugin(translateService),
    createOutlinePlugin(translateService),
    createAudioPlugin(translateService),
    createVideoPlugin(translateService),
    createRelationTestCasePlugin(translateService),
    createRelationTestCaseListPlugin(translateService),
    createRelationIdeaPlugin(translateService),
    createRelationIdeaListPlugin(translateService),
    createRelationTicketPlugin(translateService),
    createRelationTicketListPlugin(translateService),
    createRelationObjectivePlugin(translateService),
    createRelationObjectiveListPlugin(translateService),
    createAIPlugin(translateService),
    createRelationReportPlugin(translateService),
    createDrawioPlugin(translateService)
];
