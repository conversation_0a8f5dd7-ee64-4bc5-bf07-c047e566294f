import { TheEditor } from '@worktile/theia';
import { AppRootContext } from '@atinc/ngx-styx';
import { Text } from 'slate';
import { WikiCommonSharedTextComponent } from './text/text.component';
import { YjsEditor, withCursor } from '@worktile/y-slate';

export const withShared = (sharedEditor: TheEditor) => {
    const { renderLeaf } = sharedEditor;
    const appRootContext = sharedEditor.injector.get(AppRootContext);

    sharedEditor.renderLeaf = (text: Text) => {
        if (text.data && text.data.uid !== appRootContext?.globalInfo?.me?.uid) {
            return WikiCommonSharedTextComponent;
        }
        return renderLeaf(text);
    };
    return withCursor(sharedEditor as YjsEditor, sharedEditor.provider.awareness);
};
