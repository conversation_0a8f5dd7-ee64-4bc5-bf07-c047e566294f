import { Component } from '@angular/core';
import { CustomText, TheEditor } from '@worktile/theia';
import { CursorInfo } from '@worktile/y-slate/main/model';
import { Path } from 'slate';
import { BaseLeafComponent } from 'slate-angular';

@Component({
    selector: 'span[sharedLeaf]',
    template: `
        <span [ngStyle]="{ position: 'relative', backgroundColor: leafData?.data?.alphaColor }">
            @if (isFocusNode) {
                <wiki-common-caret [decorate]="leafData?.data" [isForward]="leafData?.isForward"></wiki-common-caret>
            }
            <span slateString [context]="context" [viewContext]="viewContext"></span>
        </span>
    `,
    standalone: false
})
export class WikiCommonSharedTextComponent extends BaseLeafComponent {
    leafData: CustomText & CursorInfo;

    isFocusNode: boolean;

    onContextChange() {
        super.onContextChange();
        this.leafData = this.leaf as unknown as CustomText & CursorInfo;
        const path = TheEditor.findPath(this.editor, this.context.text);
        this.isFocusNode = Path.equals(this.leafData.originFocusPath, path);
    }
}
