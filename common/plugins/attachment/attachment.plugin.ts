import { hasPermissionTransform, NestedKey, StyxPricingService, StyxTranslateService } from '@atinc/ngx-styx';
import { wikiPermissionPoints } from '@wiki/common/constants';
import { AttachmentElement } from '@wiki/common/custom-types';
import { TheExtensionMode } from '@wiki/common/interface';
import { AttachmentContext, WikiPluginContext } from '@wiki/common/services';
import { CustomPluginKeys, WikiPluginMenuIcons } from '@wiki/common/types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { checkRejectFolderAndHtmlElement } from '@wiki/common/util/file';
import { includesTextSuffixFormat } from '@wiki/common/util/plugin-help';
import {
    createPluginFactory,
    ElementKinds,
    getPluginOptions,
    PluginKeys,
    THE_MODE_TOKEN,
    TheEditor,
    ThePluginMenuItemType
} from '@worktile/theia';
import { Element } from 'slate';
import { AttachmentPluginOptions } from '../../interface';
import { AudioPluginOptions } from '../audio/audio.plugin';
import { BaseFileEditor } from '../file/base-file.editor';
import { VideoPluginOptions } from '../video/video.plugin';
import { WikiCommonEditorAttachmentComponent } from './attachment.component';
import { AttachmentEditor } from './attachment.editor';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withAttachment = <T extends TheEditor>(editor: T): T => {
    const { isVoid, insertData, isBlockCard, renderElement, isInline } = editor;

    editor.isInline = (element: AttachmentElement) => {
        return element.type === WikiPluginTypes.attachment && AttachmentEditor.isText(element) ? true : isInline(element);
    };

    editor.isVoid = (element: Element) => {
        return element.type === WikiPluginTypes.attachment ? true : isVoid(element);
    };

    editor.insertData = data => {
        const pluginContext = editor.injector.get(WikiPluginContext) as WikiPluginContext & AttachmentContext;
        const pagePermissions = pluginContext?.getPagePermissions();
        const hasPermission = hasPermissionTransform(pagePermissions, wikiPermissionPoints, 'page_upload_attachment');
        const pricingService = editor.injector.get(StyxPricingService);

        if (hasPermission && data.files.length) {
            const attachmentFiles: File[] = [];
            const imageTypes = getPluginOptions<any>(editor, PluginKeys.image)?.imageTypes;
            const audioTypes = getPluginOptions<AudioPluginOptions>(editor, CustomPluginKeys.audio)?.audioTypes;
            const videoTypes = getPluginOptions<VideoPluginOptions>(editor, CustomPluginKeys.video)?.videoTypes;
            if (checkRejectFolderAndHtmlElement(data)) {
                if (pricingService.isEnabledCheckpoint()) {
                    for (const file of data.files) {
                        if (!imageTypes.includes(file.type) && !includesTextSuffixFormat([...audioTypes, ...videoTypes], file.name)) {
                            attachmentFiles.push(file);
                        }
                    }
                } else {
                    // 未付费团队不过滤音视频文件，默认处理为附件
                    for (const file of data.files) {
                        if (!imageTypes.includes(file.type)) {
                            attachmentFiles.push(file);
                        }
                    }
                }

                if (attachmentFiles && attachmentFiles.length > 0) {
                    BaseFileEditor.addFile(editor, attachmentFiles);
                }
            }
        }
        insertData(data);
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.attachment) {
            return WikiCommonEditorAttachmentComponent;
        }
        return renderElement(element);
    };

    editor.isBlockCard = (element: AttachmentElement) => {
        if (element && AttachmentEditor.isCard(element)) {
            return true;
        }
        return isBlockCard(element);
    };

    return editor;
};

export const createAttachmentPlugin = (translate: StyxTranslateService) =>
    createPluginFactory<AttachmentPluginOptions>({
        key: CustomPluginKeys.attachment,
        withOverrides: withAttachment,
        menuItems: [
            {
                key: WikiPluginTypes.attachmentSelect,
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.attachmentSelect,
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.attachment'),
                keywords: `yiyouwenjian,yywj,file,appendix,,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.attachment')}`,
                displayKey: '/yywj',
                execute: (editor: TheEditor) => AttachmentEditor.insertAttachment(editor, WikiPluginTypes.attachmentSelect),
                isHidden: (editor: TheEditor) => {
                    const modeConfig = editor.injector.get(THE_MODE_TOKEN);
                    return modeConfig?.mode === TheExtensionMode.stencils;
                }
            },
            {
                key: WikiPluginTypes.attachmentCloud,
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.attachmentCloud,
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.attachmentCloud'),
                keywords: `bendiwenjian,bdwj,file,local files,localfiles,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.attachmentCloud')}`,
                displayKey: '/bdwj',
                execute: (editor: TheEditor) => AttachmentEditor.insertAttachment(editor, WikiPluginTypes.attachmentCloud),
                isDisabled: (editor: TheEditor) => {
                    const pageContext: any = editor.injector.get(WikiPluginContext);
                    return !hasPermissionTransform(pageContext.permissions, wikiPermissionPoints, 'page_upload_attachment');
                }
            }
        ],
        options: {
            allowParentTypes: [
                ElementKinds.tableCell,
                ElementKinds.blockquote,
                WikiPluginTypes.alert,
                WikiPluginTypes.layoutColumn,
                WikiPluginTypes.toggleListItem
            ]
        }
    })();
