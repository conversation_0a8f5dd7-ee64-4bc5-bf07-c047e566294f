@use 'ngx-tethys/styles/variables.scss';
@use './card/card.component.scss';
@use './text/text.component.scss';

.slate-element-attachment {
    max-width: 100%;
    &:not(.text-mode) {
        position: relative;
        display: block;
    }
    &.text-mode {
        display: inline-flex;
        position: relative;
        border: 2px solid transparent;
    }
    .file-uploading {
        overflow: hidden;
        .progress-operation-wrapper {
            overflow: hidden;
        }
        .progress-text {
            width: 35px;
            white-space: nowrap;
        }
    }

    &.disabled {
        &.text-mode .common-plugin-card-element,
        .common-plugin-card-element .name {
            cursor: default;
        }
        .file-thumbnail .thumbnail-wrap {
            pointer-events: none;
        }
    }
}

.the-quick-toolbar {
    .the-toolbar-item {
        flex: 1;
    }
    .thy-popover-origin-active {
        background: variables.$gray-100;
    }
}

.the-editor:not(.the-editor-readonly) {
    .slate-element-attachment {
        // 附件编辑页不需要背景色
        .file-item.file-item-uploading-normal:hover {
            background: none;
        }
    }
}
