import { AfterViewInit, Component, DestroyRef, ElementRef, inject, Input, NgZone } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AttachmentEntity, AttachmentSizes } from '@atinc/ngx-styx';
import { ATTACHMENT_TOOLBAR_REF } from '@wiki/common/constants';
import { TheEditor, topLeftPosition, updatePopoverPosition } from '@worktile/theia';
import { Range } from 'slate';

@Component({
    selector: 'wiki-common-attachment-text, [wikiCommonAttachmentText]',
    templateUrl: 'text.component.html',
    host: {
        class: 'px-2 font-size-base common-plugin-card-element attachment-text'
    },
    standalone: false
})
export class WikiCommonAttachmentTextComponent implements AfterViewInit {
    @Input() attachment: AttachmentEntity;

    @Input() size: AttachmentSizes = AttachmentSizes.md;

    @Input() selection: Range;

    @Input() editor: TheEditor;

    @Input() isCollapsedAndNonReadonly: boolean;

    private elementRef = inject(ElementRef);

    private ngZone = inject(NgZone);

    private destroyRef = inject(DestroyRef);

    ngAfterViewInit(): void {
        if (this.isCollapsedAndNonReadonly) {
            this.ngZone.onStable.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
                const ref = ATTACHMENT_TOOLBAR_REF.get(this.editor);
                if (ref && this.isCollapsedAndNonReadonly) {
                    const overlayRef = ref?.getOverlayRef();
                    const origin = this.elementRef?.nativeElement.parentElement;
                    updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
                }
            });
        }
    }
}
