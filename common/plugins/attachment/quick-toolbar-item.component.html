<thy-icon [thyIconName]="'attachment'" [thyIconRotate]="0" class="quick-toolbar-icon"></thy-icon>
<span class="quick-toolbar-name">{{ item.name }}</span>
<thy-icon class="text-desc" thyIconName="angle-right"></thy-icon>

<ng-template #attachmentMenu>
  <thy-selection-list class="attachment-menu" [thyMultiple]="false">
    <thy-list-option
      [thyDisabled]="!(pluginContext.permissions | hasPermission: 'page_upload_attachment')"
      (mousedown)="execute($event, WikiPluginTypes.attachmentCloud)"
    >
      <thy-icon thyIconName="cloud-upload" class="mr-2 text-muted"></thy-icon>
      <span class="toolbar-name text-body" translate="wiki.plugins.attachment.upload"></span>
    </thy-list-option>
    <thy-list-option (mousedown)="execute($event, WikiPluginTypes.attachmentSelect)">
      <thy-icon thyIconName="tickets-select" class="mr-2 text-muted"></thy-icon>
      <span class="toolbar-name text-body" translate="wiki.plugins.attachment.select"></span>
    </thy-list-option>
  </thy-selection-list>
</ng-template>
