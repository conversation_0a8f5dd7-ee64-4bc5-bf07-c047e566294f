import { <PERSON><PERSON><PERSON>, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, inject } from '@angular/core';
import { DriveCanPreviewPipe, FileIsImagePipe, StyxTranslateService, SubAppRootContext, hasAnyPermissionTransform } from '@atinc/ngx-styx';
import { wikiPermissionPoints } from '@wiki/common/constants';
import { PageAttachmentEntity, TheExtensionMode } from '@wiki/common/interface';
import { AttachmentContext, FileApiService, WIKI_ATTACHMENT_CONTEXT } from '@wiki/common/services';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { PageStore } from '@wiki/common/stores/page.store';
import { isSharedMode } from '@wiki/common/util/extension-mode';
import { THE_MODE_TOKEN, TheModeConfig } from '@worktile/theia';
import { tap } from 'rxjs/operators';
import { Range } from 'slate';
import { Before<PERSON><PERSON><PERSON>t<PERSON>hange, SlateElementContext } from 'slate-angular';
import { WikiCardEditor } from '../common/card-editor';
import { WikiBaseFileElementComponent } from '../file/base';
import { BaseFileEditor } from '../file/base-file.editor';
import { WikiCommonAttachmentCardComponent } from './card/card.component';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-common-editor-attachment',
    templateUrl: 'attachment.component.html',
    host: {
        '[class.danger-mode]': 'isDanger',
        '[class.text-mode]': 'element?.mode === "text"',
        '[class.cursor-pointer]': '!isShared',
        '[class.disabled]': 'isShared'
    },
    providers: [
        {
            provide: WIKI_ATTACHMENT_CONTEXT,
            useExisting: WikiPluginContext
        },
        FileIsImagePipe,
        DriveCanPreviewPipe
    ],
    standalone: false
})
export class WikiCommonEditorAttachmentComponent
    extends WikiBaseFileElementComponent
    implements OnInit, OnDestroy, BeforeContextChange<SlateElementContext>
{
    translate = inject(StyxTranslateService);

    pagePermissions: string;

    prevSelection: Range;

    isDanger: boolean;

    errorText: string;

    @ViewChild(WikiCommonAttachmentCardComponent)
    commonFileAttachmentComponent: WikiCommonAttachmentCardComponent;

    @ViewChild('elementView', { read: ElementRef }) elementView: ElementRef;

    private pageStore = inject(PageStore);
    private modeConfig = inject<TheModeConfig>(THE_MODE_TOKEN);
    private fileApiService = inject(FileApiService);
    private fileIsImagePipe = inject(FileIsImagePipe);
    private driveCanPreview = inject(DriveCanPreviewPipe);
    public subAppRootContext = inject(SubAppRootContext);
    protected attachmentContext = inject<AttachmentContext>(WIKI_ATTACHMENT_CONTEXT);

    get isStencilMode() {
        return this.modeConfig?.mode === TheExtensionMode.stencils;
    }

    get isShared() {
        return isSharedMode(this.modeConfig?.mode as TheExtensionMode);
    }

    onContextChange() {
        super.onContextChange();
        this.pagePermissions = this.attachmentContext.getPagePermissions();
        if (
            this.isCollapsedAndNonReadonly &&
            (hasAnyPermissionTransform(this.pagePermissions, wikiPermissionPoints, [
                'page_upload_attachment',
                'page_download_attachment',
                'page_rename_attachment',
                'page_delete_attachment'
            ]) ||
                this.fileIsImagePipe.transform(this.fileEntity) ||
                this.driveCanPreview.transform(this.fileEntity))
        ) {
            this.openToolbar();
        }
        if (this.readonly || !this.isCollapsedAndNonReadonly) {
            this.closeToolbar();
        }
    }

    ngOnInit() {
        super.ngOnInit();
        this.getErrorText();
    }

    getErrorText() {
        if (!this.element?._id && this.isUploadFailed) {
            this.errorText = this.translate.instant<I18nSourceDefinitionType>('wiki.plugins.attachment.linkFailed');
            return;
        }
        if (this.element?._id && !this.fileEntity) {
            this.errorText = this.translate.instant<I18nSourceDefinitionType>('wiki.plugins.attachment.deleted');
            return;
        }
        this.errorText = null;
    }

    switchText(event: MouseEvent) {
        event.preventDefault();
        BaseFileEditor.wrapText(this.editor, this.element);
    }

    switchCard(event: MouseEvent) {
        event.preventDefault();
        WikiCardEditor.switchCard(this.editor, this.element);
    }

    openImagePreview() {
        const image = this.elementRef.nativeElement.querySelector('img[thyImage]');
        image?.click();
    }

    downloadAttachment(attachment: PageAttachmentEntity) {
        this.fileApiService
            .addAttachmentDownloadLog(this.pageStore.snapshot.page._id, attachment._id, attachment.attachment_version_id)
            .subscribe();
    }

    openModifyName(attachment: PageAttachmentEntity) {
        this.commonFileAttachmentComponent?.enterEditMode(attachment);
        this.closeToolbar();
    }

    modifyName = (attachment: PageAttachmentEntity) => {
        if (this.readonly) {
            return;
        }
        return this.attachmentContext.modifyAttachmentName(attachment._id, attachment.title).pipe(
            tap(() => {
                this.fileEntity = { ...attachment };
            })
        );
    };

    finishModify() {
        this.openToolbar();
    }

    onEnterDelete() {
        if (this.isToolbarOpen) {
            this.isDanger = true;
        }
    }

    onLeaveDelete() {
        this.isDanger = false;
    }
}
