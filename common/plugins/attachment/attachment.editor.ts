import { hasPermissionTransform, helpers } from '@atinc/ngx-styx';
import { wikiPermissionDefinition } from '@wiki/common/constants/permission';
import { AttachmentElement } from '@wiki/common/custom-types';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { ElementKinds, TheEditor, TheTransforms } from '@worktile/theia';
import { Editor, Element } from 'slate';
import { BaseFileEditor } from '../file/base-file.editor';

export const AttachmentEditor = {
    insertAttachment(editor: TheEditor, type: WikiPluginTypes.attachmentCloud | WikiPluginTypes.attachmentSelect) {
        const pluginContext = editor.injector.get(WikiPluginContext);
        if (type === WikiPluginTypes.attachmentCloud) {
            BaseFileEditor.openUpload(editor);
        }
        if (type === WikiPluginTypes.attachmentSelect) {
            pluginContext.openAttachmentList(editor);
        }
    },
    isText(element: AttachmentElement) {
        if (element.type === WikiPluginTypes.attachment && element?.mode === 'text') {
            return true;
        }
        return false;
    },
    isCard(element: AttachmentElement) {
        if (element.type === WikiPluginTypes.attachment && element?.mode !== 'text') {
            return true;
        }
        return false;
    },
    isEmptyParagraph(editor: TheEditor, block: Element) {
        return block.type === ElementKinds.paragraph && block.children.length === 1 && Editor.isEmpty(editor, block);
    },
    removeAttachment(editor: TheEditor, element: Element) {
        TheTransforms.deleteElement(editor, element);
    },
    isDisabled(editor): boolean {
        const pluginContext = editor.injector.get(WikiPluginContext);
        return !hasPermissionTransform(
            pluginContext.permissions,
            helpers.getStoragePermissionPoints(wikiPermissionDefinition),
            'page_upload_attachment'
        );
    }
};
