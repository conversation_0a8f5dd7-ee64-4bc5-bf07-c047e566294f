@if (isPlaceholder) {
  <div class="common-placeholder-loading text-mode-placeholder">
    <thy-icon thyIconName="attachment"></thy-icon>
  </div>
} @else if (isUploading) {
  <file-uploading-items [scopeId]="scopeId" (uploadingAttachmentRemove)="deleteFile($event)"></file-uploading-items>
} @else if (errorText) {
  <common-empty-content [attachment]="fileEntity" [isAttachment]="true" [text]="errorText"></common-empty-content>
} @else {
  @if (element?.mode === 'card' || !element?.mode) {
    <wiki-common-attachment-card
      [attachment]="fileEntity"
      [isUploadFailed]="isUploadFailed"
      [readonly]="readonly"
      [pagePermissions]="pagePermissions"
      [modifyName]="modifyName"
      (finishModify)="finishModify()"
      [styxFilePreview]="readonly && !isShared ? fileEntity : null"
    ></wiki-common-attachment-card>
  } @else {
    <span
      wikiCommonAttachmentText
      [editor]="editor"
      [isCollapsedAndNonReadonly]="isCollapsedAndNonReadonly"
      [attachment]="fileEntity"
      [styxFilePreview]="readonly && !isShared ? fileEntity : null"
    ></span>
  }
}

<ng-template #toolbar>
  <thy-actions thySize="xxs" thePreventDefault>
    @if (!isUploading && !disabled && !isPlaceholder) {
      @if (element?.mode === 'card' || !element?.mode) {
        <a
          href="javascript:;"
          thyAction
          thyActionIcon="inline"
          styxI18nTracking
          [thyTooltip]="'styx.text' | translate"
          (mousedown)="switchText($event)"
        ></a>
      }
      @if (element?.mode === 'text') {
        <a
          href="javascript:;"
          thyAction
          styxI18nTracking
          [thyTooltip]="'styx.card' | translate"
          thyActionIcon="float-center"
          (mousedown)="switchCard($event)"
        ></a>
      }
      @if (fileEntity | fileIsImage) {
        <a
          thyAction
          thyActionIcon="preview"
          href="javascript:;"
          styxI18nTracking
          [thyTooltip]="'styx.preview' | translate"
          thyTooltipPlacement="top"
          thyStopPropagation
          (click)="openImagePreview()"
        ></a>
      }
      @if (fileEntity | driveCanPreview) {
        <a
          thyAction
          thyActionIcon="preview"
          styxI18nTracking
          [thyTooltip]="'styx.preview' | translate"
          thyTooltipPlacement="top"
          [styxFilePreview]="fileEntity"
        ></a>
      }
      @if (fileEntity && (fileEntity | isOfficeEditable)) {
        @if (!isStencilMode && fileEntity?.permissions?.update) {
          <a
            thyAction
            thyActionIcon="edit"
            thySize="sm"
            styxI18nTracking
            [thyTooltip]="'common.edit' | translate"
            thyStopPropagation
            [styxFileEditTrigger]="fileEntity"
          ></a>
        }
        @if (
          isStencilMode &&
          ((subAppRootContext?.snapshot?.globalPermissions | globalApplicationPermission: 'configuration_settings') ||
            (pagePermissions | hasPermission: 'space_stencil_setting'))
        ) {
          <a
            thyAction
            thyActionIcon="edit"
            thySize="sm"
            styxI18nTracking
            [thyTooltip]="'common.edit' | translate"
            thyStopPropagation
            [styxFileEditTrigger]="fileEntity"
          ></a>
        }
      }
      @if ((pagePermissions | hasPermission: 'page_download_attachment') || isShared) {
        <a
          href="javascript:;"
          thyAction
          thyActionIcon="download"
          styxI18nTracking
          [thyTooltip]="'common.download' | translate"
          thyTooltipPlacement="top"
          [href]="fileEntity | driveDownloadUrl"
          (click)="downloadAttachment(fileEntity)"
        ></a>
      }
      @if (pagePermissions | hasPermission: 'page_rename_attachment') {
        <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
        @if (element?.mode !== 'text') {
          <a
            href="javascript:;"
            thyAction
            thyActionIcon="rename"
            styxI18nTracking
            [thyTooltip]="'common.rename' | translate"
            thyTooltipPlacement="top"
            thyStopPropagation
            (click)="openModifyName(fileEntity)"
          ></a>
        }
      }
    }
    @if (pagePermissions | hasPermission: 'page_delete_attachment') {
      <a
        href="javascript:;"
        thyAction
        thyType="danger"
        thyActionIcon="trash"
        styxI18nTracking
        [thyTooltip]="'common.delete' | translate"
        thyTooltipPlacement="top"
        (click)="deleteFile($event)"
        (mouseenter)="onEnterDelete()"
        (mouseleave)="onLeaveDelete()"
      ></a>
    }
  </thy-actions>
</ng-template>
