import { Component, DestroyRef, ElementRef, HostBinding, Input, OnDestroy, OnInit, TemplateRef, ViewChild, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { PluginKeys, QuickInsertEditor, TheEditor, ToolbarItem, getPluginOptions } from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { fromEvent } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Editor, Node, Text } from 'slate';

@Component({
    selector: 'wiki-common-attachment-toolbar-item',
    templateUrl: './quick-toolbar-item.component.html',
    standalone: false
})
export class WikiCommonAttachmentToolbarItemComponent implements OnInit, OnDestroy {
    @HostBinding('class') className = 'wiki-common-attachment-toolbar-item d-flex justify-content-between align-items-center';

    @Input() editor: TheEditor;

    @Input() item: ToolbarItem;

    @ViewChild('attachmentMenu', { static: true })
    menu: TemplateRef<any>;

    private thyPopover = inject(ThyPopover);
    private elementRef = inject(ElementRef);
    private destroyRef = inject(DestroyRef);

    active: string | boolean = false;

    WikiPluginTypes = WikiPluginTypes;

    attachmentMenuPopoverRef: ThyPopoverRef<any>;

    get isOpened() {
        return (
            this.attachmentMenuPopoverRef &&
            this.attachmentMenuPopoverRef.getOverlayRef() &&
            this.attachmentMenuPopoverRef.getOverlayRef().hasAttached()
        );
    }

    get pluginContext() {
        return this.editor.injector.get(WikiPluginContext);
    }

    ngOnInit() {
        const listOptionElement = this.elementRef?.nativeElement.closest('.thy-list-option') as HTMLElement;
        if (listOptionElement) {
            fromEvent(listOptionElement, 'mouseenter')
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => {
                    this.openAttachmentMenuPopover(listOptionElement);
                });
            fromEvent(listOptionElement, 'mouseleave')
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe((menuMouseEvent: MouseEvent) => {
                    this.mouseLeaveHandle(menuMouseEvent);
                });
        }
    }

    ngOnDestroy() {
        if (this.isOpened) {
            this.attachmentMenuPopoverRef.close();
        }
    }

    mouseLeaveHandle(event: MouseEvent) {
        if (!this.isOpened) {
            return;
        }
        const overlayElement = this.attachmentMenuPopoverRef.getOverlayRef().overlayElement;
        if (event.relatedTarget instanceof HTMLElement && overlayElement.contains(event.relatedTarget)) {
            fromEvent(overlayElement, 'mouseleave')
                .pipe(takeUntil(this.attachmentMenuPopoverRef.afterClosed()))
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe((menuMouseEvent: MouseEvent) => {
                    if (!this.elementRef.nativeElement.contains(menuMouseEvent.relatedTarget)) {
                        this.attachmentMenuPopoverRef.close();
                    }
                });
        } else {
            this.attachmentMenuPopoverRef.close();
        }
    }

    public statusChange(editor: Editor) {
        this.active = this.item?.active(editor);
    }

    execute(event: MouseEvent, type: WikiPluginTypes) {
        event.preventDefault();
        event.stopPropagation();
        this.removeHotKey();

        if (!this.item?.includes || !type) {
            return;
        }

        const item = this.item?.includes.find((i: ToolbarItem) => i.key === type) as ToolbarItem;

        if (!item?.execute) {
            return;
        }

        item?.execute(this.editor);
        QuickInsertEditor.closeMenu(this.editor);
    }

    stopPropagation(event) {
        event.preventDefault();
        event.stopPropagation();
    }

    removeHotKey() {
        const hotkey = getPluginOptions(this.editor, PluginKeys.quickInsert)?.hotkey;
        const node = Node.get(this.editor, this.editor.selection.anchor.path) as Text;
        if (node && hotkey === node.text) {
            Editor.deleteBackward(this.editor);
        }
    }

    openAttachmentMenuPopover(origin: HTMLElement) {
        if (this.isOpened) {
            return;
        }
        this.attachmentMenuPopoverRef = this.thyPopover.open(this.menu, {
            origin,
            placement: 'rightTop',
            hasBackdrop: false,
            minWidth: 200,
            panelClass: 'toolbar-container',
            offset: 0
        });
    }
}
