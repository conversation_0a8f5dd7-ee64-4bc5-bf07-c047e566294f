import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    computed,
    DestroyRef,
    ElementRef,
    EventEmitter,
    inject,
    Inject,
    Input,
    OnInit,
    Output
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { AttachmentSizes, DriveDownloadUrlPipe } from '@atinc/ngx-styx';
import { DriveEntity, helpers, SubAppRootContext, UtilService } from '@atinc/ngx-styx/core';
import { StyxTranslateService } from '@atinc/ngx-styx/i18n/core';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { PageAttachmentEntity } from '@wiki/common/interface/base-file';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { FileApiService } from '@wiki/common/services/file-api.service';
import { PageStore } from '@wiki/common/stores/page.store';
import { isSharedMode } from '@wiki/common/util/extension-mode';
import { THE_MODE_TOKEN, TheModeConfig } from '@worktile/theia';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';

@Component({
    selector: 'wiki-common-attachment-card',
    templateUrl: './card.component.html',
    providers: [DriveDownloadUrlPipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'common-plugin-card-element attachment file-item file-item-uploading-normal',
        '[class.readonly]': 'readonly',
        '[class.editing]': '!!this.editedAttachment',
        '[attr.data-file-url]': 'driveDownloadUrl'
    },
    standalone: false
})
export class WikiCommonAttachmentCardComponent implements OnInit {
    translate = inject(StyxTranslateService);

    @Input() attachment: PageAttachmentEntity;

    @Input() size: AttachmentSizes = AttachmentSizes.md;

    @Input() isUploadFailed: boolean;

    @Input() readonly: boolean;

    @Input() pagePermissions: string;

    @Input() modifyName: (FileEntity: PageAttachmentEntity) => Observable<PageAttachmentEntity>;

    @Output() finishModify = new EventEmitter();

    editedAttachment: PageAttachmentEntity;

    isSharedMode = false;

    page = this.pageStore.select(state => state.page);

    isLockedPage = computed(() => !!this.page()?.is_lock);

    pageId = computed(() => this.page()?._id);

    get driveDownloadUrl() {
        return this.readonly && this.attachment ? this.driveDownloadUrlPipe.transform(this.attachment as DriveEntity) : '';
    }

    get isStencilMode() {
        return this.modeConfig?.mode === TheExtensionMode.stencils;
    }

    constructor(
        private driveDownloadUrlPipe: DriveDownloadUrlPipe,
        private cdr: ChangeDetectorRef,
        private elementRef: ElementRef,
        private destroyRef: DestroyRef,
        private util: UtilService,
        @Inject(THE_MODE_TOKEN)
        private modeConfig: TheModeConfig,
        private pageStore: PageStore,
        protected fileApiService: FileApiService,
        public subAppRootContext: SubAppRootContext
    ) {}

    ngOnInit() {
        this.isSharedMode = isSharedMode(this.modeConfig.mode as TheExtensionMode);
    }

    public enterEditMode(attachment: PageAttachmentEntity) {
        this.editedAttachment = helpers.cloneDeep(attachment);
        this.finishModify.emit();
        this.cdr.markForCheck();
    }

    exitRowEditMode() {
        this.editedAttachment = null;
        this.finishModify.emit();
        this.cdr.markForCheck();
    }

    saveEdit() {
        if (this.editedAttachment?.title.length && this.editedAttachment?.title !== this.attachment?.title && this.modifyName) {
            this.modifyName(this.editedAttachment)
                .pipe(
                    takeUntilDestroyed(this.destroyRef),
                    finalize(() => this.exitRowEditMode())
                )
                .subscribe(() => {
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.modifySuccess'));
                });
        } else {
            this.exitRowEditMode();
        }
    }

    downloadAttachment(event: MouseEvent) {
        this.fileApiService.addAttachmentDownloadLog(this.pageId(), this.attachment._id, this.attachment.attachment_version_id).subscribe();
    }
}
