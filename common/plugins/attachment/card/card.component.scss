@use 'ngx-tethys/styles/variables.scss';

.attachment {
    overflow: hidden;
    display: flex;
    align-items: center;
    &:not(.readonly) .name {
        cursor: pointer;
    }

    .file-title {
        display: flex;
        align-items: center;
        width: calc(100% - 144px) !important; // 16 padding * 2 + 3 个 32px icon + 8px icon 之间的间距 = 144px
        .text-block-title {
            line-height: unset;
        }
    }

    .progress-operation-wrapper {
        overflow: hidden;

        .file-size {
            display: inline-block;
            overflow: hidden;
        }
        a {
            display: none;
        }
    }

    .title-input {
        font-size: 15px;
        line-height: 28px;
    }

    &.readonly {
        &:hover {
            .operation-visible {
                .file-size {
                    display: none;
                }
                a {
                    display: inline-block;
                }
            }
        }
        &.file-item {
            .name {
                cursor: pointer;
            }
        }
    }

    &.editing {
        &:hover {
            background-color: variables.$bg-default;
        }
    }
}
