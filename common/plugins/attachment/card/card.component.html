<div class="file-title px-4">
  <styx-file-thumbnail class="mr-2" thyStopPropagation [styxFile]="attachment" [styxSize]="size"></styx-file-thumbnail>

  @if (editedAttachment) {
    <input
      class="title-input border-0 pl-0"
      thyInput
      thyAutofocus
      thyStopPropagation
      styxI18nTracking
      [placeholder]="'styx.pressEnterSave' | translate"
      [(ngModel)]="editedAttachment.title"
      (blur)="enterEditMode(attachment)"
      (thyEnter)="saveEdit()"
    />
  } @else {
    <div class="text-overflow name w-100">
      <span class="text-block-title" thyFlexibleText [thyTooltipContent]="attachment?.title">
        {{ attachment?.title }}
      </span>
    </div>
  }
</div>
@if (attachment) {
  @let isOperationVisible =
    !readonly || (attachment | isOfficeEditable) || (pagePermissions | hasPermission: 'page_download_attachment') || isSharedMode;
  <div class="progress-operation-wrapper pl-4" [ngClass]="{ 'operation-visible': isOperationVisible }">
    <span class="file-size text-muted font-size-base">{{ attachment?.addition?.size | fileSizeDisplay }}</span>
    @if (readonly) {
      @if (!isLockedPage() && attachment | isOfficeEditable) {
        @if (!isStencilMode && attachment?.permissions?.update) {
          <a
            class="ml-2"
            thyAction
            thyActionIcon="edit"
            thySize="sm"
            styxI18nTracking
            [thyTooltip]="'common.edit' | translate"
            thyStopPropagation
            [styxFileEditTrigger]="attachment"
          ></a>
        }
        @if (
          isStencilMode &&
          ((subAppRootContext?.snapshot?.globalPermissions | globalApplicationPermission: 'configuration_settings') ||
            (pagePermissions | hasPermission: 'space_stencil_setting'))
        ) {
          <a
            class="ml-2"
            thyAction
            thyActionIcon="edit"
            thySize="sm"
            styxI18nTracking
            [thyTooltip]="'common.edit' | translate"
            thyStopPropagation
            [styxFileEditTrigger]="attachment"
          ></a>
        }
      }

      @if ((pagePermissions | hasPermission: 'page_download_attachment') || isSharedMode) {
        <a
          class="ml-2"
          thyAction
          thyStopPropagation
          thyActionIcon="download"
          styxI18nTracking
          [thyTooltip]="'common.download' | translate"
          thyTooltipPlacement="top"
          [href]="driveDownloadUrl"
          (click)="downloadAttachment($event)"
        ></a>
      }
    }
  </div>
}
