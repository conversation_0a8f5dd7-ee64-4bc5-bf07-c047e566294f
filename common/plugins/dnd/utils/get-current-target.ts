import { EDITOR_TO_ELEMENT } from 'slate-dom';
import { TheEditor } from '@worktile/theia';
import { COMMON_MARGIN } from '@wiki/common/constants/editor';
import { getInlineParentNodeEntry } from './find-inline-parent-entry';
import { findNodeEntryByPoint } from '../../../util/common';

export function coercePixelsFromCssValue(cssValue: string): number {
    const match = cssValue.match(/(\d+(\.\d+)?)px/);
    if (match) {
        return Number(match[1]);
    }
}

export const getCurrentTarget = (editor: TheEditor, event: MouseEvent, dragElement?: HTMLElement): HTMLElement => {
    const editorElement = EDITOR_TO_ELEMENT.get(editor) as HTMLElement;
    const { left, right } = editorElement.getBoundingClientRect();
    let { paddingLeft, paddingRight } = window.getComputedStyle(editorElement, null);
    const paddingLeftPixels = coercePixelsFromCssValue(paddingLeft);
    const paddingRightPixels = coercePixelsFromCssValue(paddingRight);
    let x = event.x;
    if ((event.x <= left + paddingLeftPixels && event.x >= left) || (event.x >= right - paddingRightPixels && event.x < right)) {
        x = left + paddingLeftPixels;
    }
    const nodeEntry = findNodeEntryByPoint(editorElement, editor, x, event.y);
    const offsetNodeEntry = findNodeEntryByPoint(editorElement, editor, x, event.y - COMMON_MARGIN);
    if (nodeEntry && offsetNodeEntry) {
        let validNodeEntry = nodeEntry[1].length > offsetNodeEntry[1].length ? nodeEntry : offsetNodeEntry;
        const inlineParentNodeEntry = getInlineParentNodeEntry(editor, nodeEntry);
        if (inlineParentNodeEntry) {
            validNodeEntry = inlineParentNodeEntry;
        }
        const element = TheEditor.toDOMNode(editor, validNodeEntry[0]);
        // 如果hover的元素为拖拽元素，返回拖拽元素
        return dragElement && dragElement.contains(element) ? dragElement : element;
    }
};
