import { DropDirection } from '@wiki/common/constants/default';
import { TheEditor } from '@worktile/theia';
import { getCurrentTarget } from './get-current-target';

export const getHoverDirection = (editor: TheEditor, event: MouseEvent, dragElement?: HTMLElement): DropDirection => {
    const rootNode = getCurrentTarget(editor, event, dragElement);
    if (rootNode) {
        const { top, bottom } = rootNode.getBoundingClientRect();
        const middleHeight = (bottom - top) / 2;
        if (top + middleHeight > event.clientY) {
            return 'top';
        }
        return 'bottom';
    }
};
