import { WikiElement } from '@wiki/common/custom-types';
import { TheEditor } from '@worktile/theia';
import { Editor, NodeEntry } from 'slate';

export const getInlineParentNodeEntry = (editor: TheEditor, nodeEntry: NodeEntry<WikiElement>) => {
    if (Editor.isInline(editor, nodeEntry[0])) {
        const parentNode = Editor.above(editor, {
            at: nodeEntry[1],
            mode: 'lowest',
            match: (node: any) => {
                return node && node.type;
            }
        }) as NodeEntry<WikiElement>;
        return parentNode;
    }
};
