import { DROP_THUMB_LINE_DIRECTION } from '@wiki/common/constants/editor';
import { TheEditor } from '@worktile/theia';
import { EDITOR_TO_ELEMENT } from 'slate-dom';

export const removeDropThumbLine = (editor: TheEditor) => {
    const element = EDITOR_TO_ELEMENT.get(editor);
    const thumbLineElements = element.querySelectorAll(`[${DROP_THUMB_LINE_DIRECTION}]`);
    thumbLineElements.forEach(item => {
        item.removeAttribute(DROP_THUMB_LINE_DIRECTION);
    });
};
