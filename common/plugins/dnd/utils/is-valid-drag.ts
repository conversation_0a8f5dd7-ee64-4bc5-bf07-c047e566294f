import { CustomElement, DefaultElementOptions, mergeElementOptions, TheEditor } from '@worktile/theia';
import { Editor } from 'slate';

export const isValidDrag = (editor: TheEditor, dragNode: CustomElement, currentTarget: HTMLElement) => {
    const node = TheEditor.toSlateNode(editor, currentTarget) as CustomElement;
    const dropPath = TheEditor.findPath(editor, node);
    const dragPath = TheEditor.findPath(editor, dragNode);
    if (dragPath[0] === dropPath[0] && dropPath.length > 1) {
        // 阻止list元素拖入自身
        return false;
    }
    let allElementOptions = DefaultElementOptions;
    if (editor.extraElementOptions?.length) {
        const extraInfo = mergeElementOptions(editor.extraElementOptions);
        allElementOptions = Object.values(extraInfo);
    }
    const types = allElementOptions.map(item => item.type);
    if (types.indexOf(node.type) > -1 && allElementOptions[types.indexOf(node.type)].isSecondaryContainer) {
        // 阻止向table-cell等二级容器中拖入
        return false;
    }
    let containerParent = Editor.above(editor, {
        at: dropPath,
        mode: 'lowest',
        match: (node: CustomElement) => {
            return node && node.type && types.includes(node.type);
        }
    });

    if (containerParent) {
        const { invalidChildrenTypes } = allElementOptions.find(item => item.type === (containerParent[0] as CustomElement).type);
        if (invalidChildrenTypes.includes(dragNode.type)) {
            return false;
        }
    }
    return true;
};
