import { Path, Editor, Transforms, Node, Location, Range, Element } from 'slate';
import { removeDropThumbLine } from './remove-drop-thumb-line';
import { EDITOR_TO_ELEMENT } from 'slate-dom';
import { getFollowElements, HeadingElement, isStandardHeadingElement, TheEditor } from '@worktile/theia';
import { DROP_THUMB_LINE_DIRECTION } from '@wiki/common/constants/editor';
import { DragNode } from '@wiki/common/types/dnd';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { ToggleListElement, ToggleListItemElement, WikiElement } from '@wiki/common/custom-types';
import { DropDirection } from '@wiki/common/constants';

export const moveDragNode = (editor: TheEditor, dragNode: DragNode, event: MouseEvent) => {
    event.stopPropagation();
    const element = EDITOR_TO_ELEMENT.get(editor).querySelector(`[${DROP_THUMB_LINE_DIRECTION}]`);
    if (element) {
        const node = TheEditor.toSlateNode(editor, element);
        let dropPath = node && TheEditor.findPath(editor, node);
        const direction = element.getAttribute(DROP_THUMB_LINE_DIRECTION) as DropDirection;
        const dragPath = dragNode.path;
        if (dropPath.length && dragPath[0] !== dropPath[0]) {
            // 特殊处理toggle-list
            const parentNode = Node.parent(editor, dropPath) as WikiElement;
            let isToggleHeader = false;
            let isEmptyToggleList = false;
            if (parentNode && parentNode.type === WikiPluginTypes.toggleList) {
                const toggleListInfo = handleToggleDrop(editor, dropPath, parentNode, direction);
                dropPath = toggleListInfo.path;
                isEmptyToggleList = toggleListInfo.isEmptyToggleList;
                isToggleHeader = true;
            }
            const before = Path.isBefore(dragPath, dropPath) && Path.isSibling(dragPath, dropPath);
            if (!before) {
                dropPath = !isToggleHeader && direction === 'bottom' ? Path.next(dropPath) : dropPath;
            } else {
                dropPath = direction === 'top' ? Path.previous(dropPath) : dropPath;
            }
            dropPath = getDropPathByFoldedHeading(editor, dropPath, dragPath, direction);
            if (dropPath.length) {
                Editor.withoutNormalizing(editor, () => {
                    // 对于无子节点的toggleList，插入toggleListItem
                    if (isEmptyToggleList) {
                        Transforms.insertNodes(
                            editor,
                            {
                                type: WikiPluginTypes.toggleListItem,
                                children: []
                            },
                            {
                                at: dropPath.slice(0, dropPath.length - 1)
                            }
                        );
                    }
                    let dragLocation: Location = dragPath;
                    const range = getDragHeadingRangeWithFoldedElements(editor, dragPath[0]);
                    if (range) {
                        dragLocation = range;
                    }
                    Transforms.moveNodes(editor, {
                        at: dragLocation,
                        to: dropPath,
                        mode: 'highest'
                    });
                });

                setTimeout(() => {
                    const path = TheEditor.findPath(editor, dragNode.node);
                    TheEditor.focus(editor);
                    Transforms.select(editor, Editor.start(editor, path));
                });
            }
        }
    }

    removeDropThumbLine(editor);
};

const handleToggleDrop = (
    editor: TheEditor,
    dropPath: Path,
    parentNode: ToggleListElement,
    direction: string
): { path: Path; isEmptyToggleList: boolean } => {
    const path = TheEditor.findPath(editor, parentNode);
    let isEmptyToggleList = false;
    if (direction === 'top') {
        dropPath = path;
    } else {
        const { isExpanded } = parentNode;
        if (parentNode.children[1]) {
            const toggleListItem = parentNode.children[1] as ToggleListItemElement;
            const toggleListItemPath = toggleListItem && TheEditor.findPath(editor, toggleListItem);
            if (isExpanded) {
                dropPath = [...toggleListItemPath, 0];
            } else {
                dropPath = [...toggleListItemPath, toggleListItem.children.length + 1];
            }
        } else {
            isEmptyToggleList = true;
            dropPath = [...path.concat(1), 0];
        }
    }
    return {
        path: dropPath,
        isEmptyToggleList
    };
};

/**
 * 获取折叠的标题元素的数据 Range
 */
export const getDragHeadingRangeWithFoldedElements = (editor: Editor, headingIndex: number): Range | null => {
    const headingElement = editor.children[headingIndex] as HeadingElement;
    if (isStandardHeadingElement(headingElement) && headingElement.isCollapsed) {
        const startPath = [headingIndex];
        const followedElements = getFollowElements(editor, headingElement);
        const endPath = [headingIndex + followedElements.length];
        return {
            anchor: Editor.start(editor, startPath),
            focus: Editor.end(editor, endPath)
        };
    }
    return null;
};

export const getDropPathByFoldedHeading = (editor: Editor, dropPath: Path, dragPath: Path, dropDirection: DropDirection): Path => {
    if (dropPath.length === 1 && dropPath[0] > 0 && dropDirection === 'bottom') {
        const isBefore = Path.isBefore(dragPath, dropPath);
        // 定位真正的上一个元素，如果 drag 元素在 drop 元素之前，则上一个元素不需要 -1
        const previousElement = (isBefore ? editor.children[dropPath[0]] : editor.children[dropPath[0] - 1]) as Element;
        if (isStandardHeadingElement(previousElement) && previousElement.isCollapsed) {
            const followedElements = getFollowElements(editor, previousElement);
            const newDropPath = [dropPath[0] + followedElements.length];
            return newDropPath;
        }
    }
    return dropPath;
};
