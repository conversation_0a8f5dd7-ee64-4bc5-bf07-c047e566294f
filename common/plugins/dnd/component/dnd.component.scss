@use 'ngx-tethys/styles/variables.scss';
@use 'ngx-tethys/styles/bootstrap/variables.scss' as bootstrapVariables;

.the-drag-drop {
    position: absolute;
    .the-drag-drop-icon {
        color: variables.$gray-500;
        width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        font-size: variables.$font-size-base;
        border-radius: 3px;
        cursor: grab;

        &:hover {
            cursor: pointer;
            background-color: rgba(variables.$primary, 0.15);
            color: variables.$primary;
        }
    }
}
.drag-container {
    position: absolute;
    top: 0;
    left: 0;
}
.the-editor-typo.drag-snapshot-container {
    position: absolute;
    transition: all 0.2 linear;
    background: variables.$bg-default;
    padding: 5px;
    top: 0px;
    left: 0px;
    overflow: hidden;
    transform-origin: left top;
    box-shadow: 0px 4px 10px 5px rgba(variables.$black, 0.07);
    border-radius: 4px;
    min-height: 0;
    z-index: 1800;
    display: none;
    pointer-events: none;
    user-select: none;
    ol,
    ul,
    blockquote,
    .slate-element-table .the-table {
        margin: 0;
    }
    .slate-element-paragraph {
        line-height: variables.$line-height-base;
    }
}
.wiki-common-editor {
    .the-editor-typo {
        // .the-sticky-row / .the-header-cell 标题行/列固定 cell 样式
        &.slate-editable-container > *:not(.the-sticky-row):not(.the-header-cell),
        .slate-element-paragraph,
        .slate-block:not(.the-sticky-row):not(.the-header-cell) {
            position: relative;
        }
    }
}

[drop-thumb-line-direction]::after {
    content: '';
    width: 100%;
    left: 0px;
    right: 0px;
    height: 2px;
    pointer-events: none;
    user-select: none;
    background: variables.$primary;
    position: absolute;
}
[drop-thumb-line-direction='bottom']::after {
    bottom: -5px;
}
[drop-thumb-line-direction='top']::after {
    top: -5px;
}
.slate-element-table[drop-thumb-line-direction='bottom']::after {
    bottom: 2px;
}
.slate-element-table[drop-thumb-line-direction='top']::after {
    top: 5px;
}

.dragging {
    cursor: grabbing !important;
}

.opacity-container {
    position: relative;
    margin-right: bootstrapVariables.$spacer * 0.75;
    .image-content {
        margin-right: 0 !important;
    }
    &::before {
        content: '';
        width: 100%;
        height: 100%;
        z-index: 1;
        position: absolute;
        background: rgba($color: variables.$white, $alpha: 0.7);
        top: 0;
        left: 0;
    }
}
