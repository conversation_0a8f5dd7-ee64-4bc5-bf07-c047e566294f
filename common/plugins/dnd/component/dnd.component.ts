import { ScrollDispatcher } from '@angular/cdk/scrolling';
import {
    AfterViewInit,
    Component,
    computed,
    ElementRef,
    EventEmitter,
    Input,
    NgZone,
    OnDestroy,
    Output,
    Renderer2,
    ViewChild
} from '@angular/core';
import { DROP_THUMB_LINE_DIRECTION } from '@wiki/common/constants/editor';
import { setOffset, setVisibility } from '@wiki/common/util/block-position';
import { CustomElement, ElementKinds, TheEditor, TheQueries } from '@worktile/theia';
import { Subject, fromEvent, interval, merge } from 'rxjs';
import { filter, take, takeUntil } from 'rxjs/operators';
import { Operation } from 'slate';
import { AngularEditor } from 'slate-angular';
import { EDITOR_TO_ELEMENT } from 'slate-dom';
import { DragNode } from '../../../types/dnd';
import { findNodeEntryByPoint } from '../../../util/common';
import { getCurrentTarget } from '../utils/get-current-target';
import { getHoverDirection } from '../utils/get-hover-direction';
import { isValidDrag } from '../utils/is-valid-drag';
import { moveDragNode } from '../utils/move-drag-node';
import { removeDropThumbLine } from '../utils/remove-drop-thumb-line';
import { ICON_OFFSET } from '../../heading-handle/constants';
import { DropDirection } from '@wiki/common/constants';

const SNAPSHOT_PADDING_WIDTH = 5;
const SNAPSHOT_LIMI_HEIGHT = 70;
const SNAPSHOT_MIN_WIDTH = 350;
const DRAG_ICON_SIZE = 24;
const DRAG_SNAPSHOT_OFFSET_X = 15; // 拖拽内容距离鼠标 x 方向偏移量
const DRAG_SNAPSHOT_OFFSET_Y = -10; // 拖拽内容距离鼠标 y 方向偏移量
const OEACITY_CONTAINER = 'opacity-container';

@Component({
    selector: '[wikiDnd]',
    templateUrl: './dnd.component.html',
    host: {
        class: 'the-drag-drop'
    },
    standalone: false
})
export class WikiCommonDndComponent implements AfterViewInit, OnDestroy {
    isDragging = false;

    isMouseDown = false;

    dragNode: DragNode;

    dragSnapshotContent: HTMLElement;

    private destroy$ = new Subject<void>();

    @Input() editor: TheEditor;

    @Output() dragChange: EventEmitter<boolean> = new EventEmitter();

    dragElement: HTMLElement;

    dragElementHeight: number;

    dragElementWidth: number;

    isScrolling: boolean;

    editableTop: number;

    @ViewChild('content', { static: false }) content: ElementRef;

    get nativeElement(): HTMLElement {
        return this.elementRef.nativeElement;
    }

    get editableElement() {
        return this.editor && EDITOR_TO_ELEMENT.get(this.editor);
    }

    basicOffsetTop = computed(() => {
        const containerTop = this.nativeElement.parentElement.parentElement.getBoundingClientRect().top;
        const editorTop = this.editableElement.getBoundingClientRect().top;
        return editorTop - containerTop;
    });

    constructor(
        private renderer: Renderer2,
        private elementRef: ElementRef,
        private ngZone: NgZone,
        private scrollDispatcher: ScrollDispatcher
    ) {}

    ngAfterViewInit(): void {
        this.ngZone.runOutsideAngular(() => {
            merge(fromEvent<MouseEvent>(this.editableElement, `mousemove`), fromEvent<MouseEvent>(this.editableElement, `mouseleave`))
                .pipe(
                    takeUntil(this.destroy$),
                    filter(() => {
                        return !this.editor?.options.readonly;
                    })
                )
                .subscribe(e => this.setDragIcon(e));
        });
        setVisibility(this.nativeElement.parentNode as HTMLElement, false);
        this.valueChangeHandle();
    }

    valueChangeHandle() {
        const { onChange } = this.editor;
        this.editor.onChange = () => {
            const ops = this.editor.operations;
            const skip = ops.length === 1 && Operation.isSelectionOperation(ops[0]);
            if (!skip) {
                setVisibility(this.nativeElement.parentNode as HTMLElement, false);
            }
            onChange();
        };
    }

    private updateDndContainerPosition(left: string, top: string) {
        const element = this.elementRef.nativeElement as HTMLElement;
        const parentElement = element.parentElement;
        top && this.renderer.setStyle(parentElement, 'top', top);
        left && this.renderer.setStyle(parentElement, 'left', left);
    }

    fakeDragAndDropEvent(event: MouseEvent) {
        event.preventDefault();
        const target = (event.target as HTMLElement).closest('[the-draggable="true"]');
        let dragging = false;
        const scrollContainer = this.getScrollContainer();
        const scrollContainerRect = scrollContainer.getBoundingClientRect();
        let direction = null;
        this.isMouseDown = true;
        this.ngZone.runOutsideAngular(() => {
            const interval$ = interval(20).subscribe(() => {
                if (direction) {
                    this.scrollContainer(scrollContainer, direction, scrollContainerRect);
                }
            });
            const mouseMove$ = fromEvent<MouseEvent>(document, `mousemove`).subscribe(e => {
                if (!dragging) {
                    this.onDragStart({ ...event, target });
                }
                dragging = true;
                document.body.classList.add('dragging');
                this.onDragover(e);
                direction = this.getAutoScrollDirection(e, scrollContainer);
            });
            fromEvent<MouseEvent>(document, `mouseup`)
                .pipe(take(1))
                .subscribe(e => {
                    mouseMove$.unsubscribe();
                    interval$.unsubscribe();
                    this.isMouseDown = false;
                    if (dragging) {
                        this.onDrop(e);
                        this.onDragEnd({ ...event, target });
                        document.body.classList.remove('dragging');
                    }
                });
        });
    }

    getAutoScrollDirection(event: MouseEvent, container: HTMLElement) {
        let top = 0;
        let bottom = 0;
        if (container instanceof HTMLBodyElement) {
            bottom = window.innerHeight;
        } else {
            const rect = container.getBoundingClientRect();
            top = rect.top;
            bottom = rect.bottom;
        }
        const auditOffset = 30;
        if (event.y - auditOffset < top) {
            return 'top';
        }
        if (event.y + auditOffset > bottom) {
            return 'bottom';
        }
        return null;
    }

    scrollContainer(container: HTMLElement, direction: DropDirection, rect: DOMRect) {
        let scrollTop = 0;
        let scrollHeight = 0;
        if (container instanceof HTMLBodyElement) {
            scrollTop = window.scrollY;
            scrollHeight = window.document.body.scrollHeight;
        } else {
            scrollTop = container.scrollTop;
            scrollHeight = container.scrollHeight;
        }

        if (direction === 'top') {
            scrollTop = scrollTop - 10;
            if (scrollTop < 0) {
                scrollTop = 0;
            }
        }
        if (direction === 'bottom') {
            scrollTop = scrollTop + 10;
            if (scrollTop + rect.height > scrollHeight) {
                scrollTop = scrollHeight - rect.height;
            }
        }
        if (container instanceof HTMLBodyElement) {
            window.scrollTo({ top: scrollTop });
        } else {
            container.scrollTop = scrollTop;
        }
    }

    getScrollContainer() {
        let scrollContainer: HTMLElement = window.document.body;
        this.scrollDispatcher.scrollContainers.forEach((value, key) => {
            if (key.getElementRef().nativeElement.contains(this.nativeElement)) {
                scrollContainer = key.getElementRef().nativeElement;
            }
        });
        return scrollContainer;
    }

    onDragStart(event: MouseEvent) {
        this.isDragging = true;
        this.dragChange.emit(this.isDragging);
        this.dragSnapshotContent = (this.elementRef.nativeElement as HTMLElement).nextSibling as HTMLElement;
        this.dragSnapshotContent.className = 'the-editor-typo drag-snapshot-container';
        this.dragSnapshotContent.appendChild(this.dragElement.cloneNode(true));
        if (this.dragNode.node.type === ElementKinds.image && this.dragNode.node.layout) {
            this.dragElement.classList.add(OEACITY_CONTAINER);
        } else {
            this.dragElement.style.opacity = '0.3';
        }
        this.setSnapshotStyle(event);
    }

    onDragEnd(event: MouseEvent) {
        this.dragElement.style.opacity = '';
        this.dragElement.classList.remove(OEACITY_CONTAINER);
        this.isDragging = false;
        setVisibility(this.nativeElement.parentNode as HTMLElement, false);
        this.resetDragSnapshotContent();
        this.dragChange.emit(this.isDragging);
    }

    onDrop(event: MouseEvent) {
        event.preventDefault();
        if (this.isDragging) {
            moveDragNode(this.editor, this.dragNode, event);
        }
    }

    onDragover(event: MouseEvent) {
        event.preventDefault();
        if (this.isDragging && !this.isScrolling) {
            const offsetWidth = document.documentElement.offsetWidth;
            const { left: editorLeft, top: editorTop, height } = this.editableElement.getBoundingClientRect();
            let top = 0;
            let left = 0;
            if (event.clientX + this.dragElementWidth / 2 > offsetWidth) {
                left = event.pageX - editorLeft - this.nativeElement.offsetWidth;
            } else {
                left = Math.max(event.pageX - editorLeft, -editorLeft) + DRAG_SNAPSHOT_OFFSET_X;
            }

            const scrollTop = document.documentElement.scrollTop;
            const editorMarginTop = editorTop + scrollTop; // 编辑器距离顶部间距
            if (event.clientY + this.dragElementHeight + DRAG_SNAPSHOT_OFFSET_Y > document.documentElement.offsetHeight) {
                top = event.pageY - editorMarginTop - this.nativeElement.offsetHeight + DRAG_SNAPSHOT_OFFSET_Y;
                top = Math.min(top, height - this.editableElement.offsetTop);
            } else {
                top = event.pageY - editorMarginTop + this.editableElement.offsetTop + DRAG_SNAPSHOT_OFFSET_Y;
            }
            this.updateDndContainerPosition(left + 'px', this.basicOffsetTop() + top + 'px');
            this.setSnapshotPosition(event);
            this.setDropThumbLine(event);
        }
    }

    setDropThumbLine(event: MouseEvent) {
        let element = getCurrentTarget(this.editor, event, this.dragElement);
        const direction = getHoverDirection(this.editor, event, this.dragElement);
        const { top, right, left, height } = this.editableElement.getBoundingClientRect();
        const scrollTop = document.documentElement.scrollTop;
        const editorMarginTop = top + scrollTop; // 编辑器距离顶部间距
        if (event.pageX > right || event.pageX < left || event.pageY < top || event.pageY > height + editorMarginTop) {
            removeDropThumbLine(this.editor);
            return;
        }
        if (element && direction && element && isValidDrag(this.editor, this.dragNode.node, element)) {
            removeDropThumbLine(this.editor);
            element.setAttribute(DROP_THUMB_LINE_DIRECTION, direction);
        }
    }

    setDragIcon(event: MouseEvent) {
        if (!this.isDragging && !this.isMouseDown) {
            const nodeEntry = findNodeEntryByPoint(this.editableElement, this.editor, event.x, event.y, 'highest');
            if (nodeEntry && !TheQueries.isEmptyParagraphElement(this.editor, nodeEntry[0])) {
                const rootNode = AngularEditor.toDOMNode(this.editor, nodeEntry[0]);
                this.dragElement = rootNode;
                this.dragNode = {
                    node: nodeEntry[0] as CustomElement,
                    path: nodeEntry[1]
                };
                setOffset(
                    rootNode,
                    this.renderer,
                    this.nativeElement.parentNode as HTMLElement,
                    nodeEntry,
                    DRAG_ICON_SIZE,
                    ICON_OFFSET,
                    this.basicOffsetTop()
                );
            } else {
                setVisibility(this.nativeElement.parentNode as HTMLElement, false);
            }
        }
    }

    setSnapshotPosition(event: MouseEvent) {
        const { width: scaleWidth, height: scaleHeight } = this.dragSnapshotContent.getBoundingClientRect();
        this.dragElementHeight = scaleHeight;
        this.dragElementWidth = scaleWidth;
        let snapshotTop = 0;
        let snapshotLeft = 0;
        // 上下移动：超出屏幕高度，重新设置snapshot的位置
        if (event.clientY + this.dragElementHeight + DRAG_SNAPSHOT_OFFSET_Y > document.documentElement.offsetHeight) {
            snapshotTop = this.dragElementHeight;
        } else {
            snapshotTop = 0;
        }

        // 左右移动：超出屏幕宽度，重新设置snapshot的位置
        if (event.clientX + this.dragElementWidth / 2 > document.documentElement.offsetWidth) {
            snapshotLeft = this.dragElementWidth;
        } else {
            snapshotLeft = 0;
        }
        this.dragSnapshotContent.style.top = `${-snapshotTop}px`;
        this.dragSnapshotContent.style.left = `${-snapshotLeft}px`;
    }

    setSnapshotStyle(event: MouseEvent) {
        const { width, height } = this.dragElement.getBoundingClientRect();
        let dragSnapshotContentStyle = `display: block; min-height: 0; min-width: ${SNAPSHOT_MIN_WIDTH / 0.45}px; width: ${
            width + SNAPSHOT_PADDING_WIDTH * 2
        }px;`;
        if (height > SNAPSHOT_LIMI_HEIGHT) {
            dragSnapshotContentStyle = dragSnapshotContentStyle + `transform: scale(0.45);`;
        }
        this.dragSnapshotContent.setAttribute('style', dragSnapshotContentStyle);
    }

    resetDragSnapshotContent() {
        this.dragSnapshotContent.className = 'drag-snapshot-container';
        this.dragSnapshotContent.setAttribute('style', '');
        this.dragSnapshotContent.innerHTML = '';
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }
}
