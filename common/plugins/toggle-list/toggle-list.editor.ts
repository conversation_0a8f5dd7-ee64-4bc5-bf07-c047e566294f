import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { TheEditor, TheTransforms } from '@worktile/theia';
import { Editor, Element, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { ToggleListElement } from '../../../common/custom-types';
import { createToggleList } from './transforms/create-toggle-list';
import { createToggleListItem } from './transforms/create-toggle-list-item';

export const ToggleListEditor = {
    insertToggleList(editor: TheEditor) {
        if (!editor.selection) {
            return;
        }
        TheTransforms.insertElements(editor, createToggleList());
    },
    insertToggleListItem(editor: TheEditor, toggleList: ToggleListElement) {
        const toggleListPath = AngularEditor.findPath(editor, toggleList);
        const toggleListItemPath = toggleListPath.concat(1);
        const toggleListItem = createToggleListItem();
        Transforms.insertNodes(editor, toggleListItem, { at: toggleListItemPath });
    },
    isActive(editor: Editor) {
        const [list] = Editor.nodes(editor, { match: n => Element.isElement(n) && n.type === WikiPluginTypes.toggleList });
        return !!list;
    }
};
