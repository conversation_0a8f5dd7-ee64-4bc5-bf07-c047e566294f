import { ToggleListElement, WikiElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { computeOverrideByKey } from '@wiki/common/util/compute-override-by-key';
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    PluginKeys,
    TheEditor,
    ThePluginMenuItemType,
    TheQueries,
    createPluginFactory,
    setClipboardDataByDom
} from '@worktile/theia';
import { isKeyHotkey } from 'is-hotkey';
import { Editor, Element, NodeEntry, Path, Transforms } from 'slate';
import { AngularEditor, ELEMENT_TO_COMPONENT, OriginEvent, hasBlockCard, hotkeys } from 'slate-angular';
import { NODE_TO_PARENT } from 'slate-dom';
import { WikiCommonToggleListItemComponent } from './components/list-item/list-item.component';
import { WikiCommonToggleListComponent } from './components/list/list.component';
import { getToggleNodes } from './queries/copy-toggle-list';
import { getFirstNodeAndList } from './queries/get-first-node-and-list';
import { getFoldedToggleListEntry } from './queries/get-folded-toggle-list-entry';
import { getNextNode } from './queries/get-next-node';
import { isEndInToggleListHeader } from './queries/is-end-in-toggle-list-header';
import { isFirstNodeInToggleList } from './queries/is-first-node-in-toggle-list';
import { isInToggleList } from './queries/is-in-toggle-list';
import { isStartInToggleListHeader } from './queries/is-start-in-toggle-list-header';
import { isToggleList } from './queries/is-toggle-list';
import { ToggleListEditor } from './toggle-list.editor';
import { deleteBackwardAndMerge } from './transforms/delete-backward-and-merge';
import { firstItemDeleteBackward } from './transforms/first-item-delete-backward';
import { firstItemInsertBreak } from './transforms/first-item-insert-break';
import { insertListData } from './transforms/insert-list-data';
import { listNormalize } from './transforms/list-normalize';
import { moveListItemChildUp } from './transforms/move-list-item-child-up';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withToggleList = (editor: TheEditor) => {
    const { renderElement, insertBreak, isContainer, deleteBackward, onKeydown, setFragmentData, insertData, normalizeNode, isExpanded } =
        editor;

    editor.insertBreak = () => {
        let isHandle = false;
        const isFirstInToggleList = isFirstNodeInToggleList(editor);

        if (isFirstInToggleList) {
            isHandle = firstItemInsertBreak(editor);
            if (isHandle) return;
        }

        isHandle = moveListItemChildUp(editor);
        if (isHandle) return;

        insertBreak();
    };

    editor.deleteBackward = unit => {
        let isHandle = false;
        const isFirstInToggleList = isFirstNodeInToggleList(editor);

        if (isFirstInToggleList) {
            isHandle = firstItemDeleteBackward(editor);
            if (isHandle) return;
        }

        if (!isFirstInToggleList) {
            isHandle = deleteBackwardAndMerge(editor);
            if (isHandle) return;
        }

        deleteBackward(unit);
    };

    editor.onKeydown = event => {
        const isFirstInToggleList = isFirstNodeInToggleList(editor);
        if (isFirstInToggleList && (isKeyHotkey('Tab', event) || isKeyHotkey('shift+Tab', event))) {
            event.preventDefault();
            return;
        }
        const isMoveBackward = hotkeys.isMoveBackward(event);
        const isMoveForward = hotkeys.isMoveForward(event);
        const isMoveUp = hotkeys.isMoveUp(event);
        if (isMoveBackward) {
            const foldedToggleListEntry = getFoldedToggleListEntry(editor);
            if (foldedToggleListEntry && ((isFirstInToggleList && isStartInToggleListHeader(editor)) || !isFirstInToggleList)) {
                event.preventDefault();
                Transforms.select(editor, Editor.end(editor, foldedToggleListEntry[1].concat(0)));
                return;
            }
        }

        if (isMoveForward) {
            const firstAndList = getFirstNodeAndList(editor);
            if (firstAndList) {
                const { list } = firstAndList;
                if (isEndInToggleListHeader(editor) && !(list[0] as ToggleListElement).isExpanded) {
                    event.preventDefault();
                    const nextNode = getNextNode(editor, list[1]);
                    if (nextNode) {
                        const nextPath = TheEditor.findPath(editor, nextNode);
                        if (editor.isBlockCard(nextNode)) {
                            AngularEditor.moveBlockCard(editor, nextNode, { direction: 'left' });
                        } else {
                            Transforms.select(editor, Editor.end(editor, Editor.start(editor, nextPath)));
                        }
                        return;
                    }
                }
            }
        }
        if (isMoveUp) {
            const domSelection = window.getSelection();
            const anchorNode = domSelection.anchorNode;
            if (anchorNode && hasBlockCard(domSelection)) {
                const [, path] = AngularEditor.toSlateCardEntry(editor, anchorNode);
                const previousPath = Path.hasPrevious(path) && Path.previous(path);
                const previousNode = previousPath && (TheQueries.getNode(editor, previousPath) as Element);
                if (previousNode && previousNode.type === WikiPluginTypes.toggleList && !previousNode.isExpanded) {
                    Transforms.select(editor, Editor.end(editor, previousPath.concat(0)));
                    event.preventDefault();
                    return;
                }
            }
        }

        onKeydown(event);
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.toggleList) {
            return WikiCommonToggleListComponent;
        }
        if (element.type === WikiPluginTypes.toggleListItem) {
            return WikiCommonToggleListItemComponent;
        }
        return renderElement(element);
    };

    editor.isContainer = (element: Element) => {
        return element.type === WikiPluginTypes.toggleListItem ? true : isContainer(element);
    };

    editor.isExpanded = (element: Element) => {
        if (element.type === WikiPluginTypes.toggleListItem) {
            const toggleListElement: any = NODE_TO_PARENT.get(element);
            // 基于 toggle-list 状态确定 toggle-list-item 是展开还是收起
            if (toggleListElement && toggleListElement.type === WikiPluginTypes.toggleList) {
                const listComponent = ELEMENT_TO_COMPONENT.get(toggleListElement) as WikiCommonToggleListComponent;
                return listComponent.isExpanded;
            }
        }
        return isExpanded(element);
    };

    editor.setFragmentData = (data: DataTransfer, originEvent?: OriginEvent) => {
        const { selection } = editor;
        const [start] = Editor.edges(editor, selection);
        if (isInToggleList(editor, start)) {
            const fragmentData = editor.getFragment();
            // 第一个元素必须是折叠列表，如果第一个元素不是折叠列表那就不是需要处理的场景（解除包裹/向上提级）
            // 通过这个条件可以过滤掉折叠列表中复制表格的场景
            if (isToggleList(fragmentData[0] as WikiElement)) {
                const nodes = getToggleNodes(fragmentData as Element[]);
                // 处理 toggle-list -> paragraph/heading 场景
                if (isToggleList(nodes[0]) && nodes[0].children.length === 1) {
                    nodes[0] = nodes[0].children[0];
                }
                setClipboardDataByDom(editor, nodes, data);
                return;
            }
        }
        setFragmentData(data, originEvent);
    };

    editor.insertData = async (data: DataTransfer) => {
        let isHandle = false;

        isHandle = await insertListData(editor, data);
        if (isHandle) return;

        insertData(data);
    };

    editor.normalizeNode = (entry: NodeEntry) => {
        let isHandle = false;

        isHandle = listNormalize(editor, entry);
        if (isHandle) return;

        normalizeNode(entry);
    };

    return editor;
};

const pluginKeys = [PluginKeys.hr, PluginKeys.table, PluginKeys.code];
const byKey = computeOverrideByKey(pluginKeys, [WikiPluginTypes.toggleListItem]);

export const createToggleListPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.toggleList,
        withOverrides: withToggleList,
        overrideByKey: {
            ...byKey
        },
        nestedStructureByKey: {
            [WikiPluginTypes.toggleList]: WikiPluginTypes.toggleListItem
        },
        toolbarItems: [
            {
                key: WikiPluginTypes.toggleList,
                icon: 'list-toggle',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.toggleList'),
                execute: (editor: TheEditor) => ToggleListEditor.insertToggleList(editor),
                active: (editor: TheEditor) => ToggleListEditor.isActive(editor)
            }
        ],
        menuItems: [
            {
                key: WikiPluginTypes.toggleList,
                keywords: `zhedieliebiao,zdlb,toggle list,togglelist,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.toggleList')}`,
                execute: (editor: TheEditor) => ToggleListEditor.insertToggleList(editor),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.toggleList'),
                icon: 'list-toggle',
                type: ThePluginMenuItemType.icon,
                menuIcon: WikiPluginMenuIcons.toggleList,
                displayKey: '/zdlb'
            }
        ],
        options: {
            allowParentTypes: [
                ElementKinds.tableCell,
                ElementKinds.blockquote,
                WikiPluginTypes.alert,
                WikiPluginTypes.layoutColumn,
                WikiPluginTypes.toggleListItem
            ]
        }
    })();
