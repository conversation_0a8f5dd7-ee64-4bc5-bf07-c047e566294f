import { ToggleListElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { ElementKinds, HEADING_TYPES, TheEditor, TheQueries } from '@worktile/theia';
import { AngularEditor } from 'slate-angular';

export const isFirstNodeInToggleList = (editor: TheEditor, at = editor?.selection?.anchor) => {
    const listType = WikiPluginTypes.toggleList;
    const firstTypes = [ElementKinds.default, ...HEADING_TYPES];

    const isBlockCardLeftCursor = AngularEditor.isBlockCardLeftCursor(editor);
    const isBlockCardRightCursor = AngularEditor.isBlockCardRightCursor(editor);

    if (!at || isBlockCardLeftCursor || isBlockCardRightCursor) {
        return false;
    }

    const anchorEntry = TheQueries.getAnchorBlockEntry(editor, at);
    if (!anchorEntry || !anchorEntry[0] || !firstTypes.includes(anchorEntry[0].type)) return;

    const [, cursorPath] = anchorEntry;
    const parentEntry = TheQueries.getParent(editor, cursorPath);
    const [parentNode] = parentEntry;
    if (!parentNode || (parentNode as ToggleListElement).type !== listType) return;

    return TheQueries.isFirstChild(cursorPath);
};
