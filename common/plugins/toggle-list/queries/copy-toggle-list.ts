import { Element } from 'slate';
import { isToggleList } from './is-toggle-list';
import { CustomElementKinds, ElementKinds, HEADING_TYPES } from '@worktile/theia';

function isParagraph(type: CustomElementKinds) {
    return type === ElementKinds.paragraph;
}

function isHeading(type: CustomElementKinds) {
    return HEADING_TYPES.includes(type);
}

export const getToggleNodes = (fragmentData: Element[]) => {
    const result = [];
    let isOk = true;
    fragmentData.forEach(item => {
        if (isToggleList(item) && !isParagraph(item.children[0].type) && !isHeading(item.children[0].type)) {
            isOk = false;
            result.push(...(item.children[0] as Element).children);
        } else {
            result.push(item);
        }
    });
    if (!isOk) {
        return getToggleNodes(result);
    }
    return result;
};
