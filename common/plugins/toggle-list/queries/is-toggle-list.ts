import { Element } from 'slate';
import { CustomElementKinds } from '@worktile/theia';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { ToggleListElement } from '../../../custom-types';

export function isToggleList(value: Element): value is ToggleListElement {
    return value.type === WikiPluginTypes.toggleList;
}

export function isToggleListItem(type: CustomElementKinds) {
    return type === WikiPluginTypes.toggleListItem;
}
