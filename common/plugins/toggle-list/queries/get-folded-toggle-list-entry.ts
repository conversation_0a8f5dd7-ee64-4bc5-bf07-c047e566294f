import { ToggleListElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { TheEditor, TheQueries } from '@worktile/theia';
import { NodeEntry, Path, Editor, Element } from 'slate';
import { hasBlockCard, isCardLeft, AngularEditor } from 'slate-angular';

export const getFoldedToggleListEntry = (editor: TheEditor) => {
    let foldedToggleListEntry: NodeEntry<ToggleListElement>;
    const domSelection = window.getSelection();
    const anchorNode = domSelection.anchorNode;
    if (anchorNode && hasBlockCard(domSelection)) {
        const isCardLeftCursor = isCardLeft(anchorNode);
        if (isCardLeftCursor) {
            const [, path] = AngularEditor.toSlateCardEntry(editor, anchorNode);
            const previousPath = Path.hasPrevious(path) && Path.previous(path);
            const previousNode = previousPath && (TheQueries.getNode(editor, previousPath) as Element);
            if (previousNode.type === WikiPluginTypes.toggleList) {
                if (previousNode.isExpanded) {
                    const lastNode = Editor.last(editor, previousPath);
                    const previousEntry = Editor.above(editor, {
                        at: lastNode[1],
                        mode: 'highest',
                        match: (n: Element) => n.type === WikiPluginTypes.toggleList && !n.isExpanded
                    }) as NodeEntry<ToggleListElement>;
                    foldedToggleListEntry = previousEntry ? previousEntry : null;
                } else {
                    foldedToggleListEntry = [previousNode, previousPath];
                }
            }
        }
    } else {
        const previousPath = Editor.before(editor, editor.selection);
        if (!previousPath) return false;
        const previousEntry = Editor.above(editor, {
            at: previousPath,
            mode: 'highest',
            match: (n: Element) => n.type === WikiPluginTypes.toggleList && !n.isExpanded
        }) as NodeEntry<ToggleListElement>;
        foldedToggleListEntry = previousEntry ? previousEntry : null;
    }
    return foldedToggleListEntry;
};
