import { TheQueries } from '@worktile/theia';
import { Editor, Path, Point, Range } from 'slate';
import { WikiPluginTypes } from '../../../types/editor.types';

export const isInToggleList = (editor: Editor, at: Path | Range | Point | null = editor.selection) => {
    return TheQueries.isNodeTypeIn(editor, [WikiPluginTypes.toggleList], { at });
};
export const isInToggleListItem = (editor: Editor, at: Path | Range | Point | null = editor.selection) => {
    return TheQueries.isNodeTypeIn(editor, [WikiPluginTypes.toggleListItem], { at });
};
