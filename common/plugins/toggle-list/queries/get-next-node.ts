import { TheEditor, TheQueries } from '@worktile/theia';
import { Editor, Path } from 'slate';

export const getNextNode = (editor: TheEditor, path: Path) => {
    const nextPath = Path.next(path);
    const nextNode = TheQueries.getNode(editor, nextPath);
    if (nextNode) {
        return nextNode;
    } else {
        const parent = Editor.parent(editor, path);
        const parentPath = parent && parent[1];
        return parentPath && getNextNode(editor, parentPath);
    }
};
