import { Path, Point, Element } from 'slate';
import { TheEditor, TheQueries } from '@worktile/theia';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { allowFirstTypes } from '../type';
import { AngularEditor } from 'slate-angular';

export const getFirstNodeAndList = (editor: TheEditor, at: Path | Point = editor.selection.anchor) => {
    const listType = WikiPluginTypes.toggleList;

    const isBlockCardLeftCursor = AngularEditor.isBlockCardLeftCursor(editor);
    const isBlockCardRightCursor = AngularEditor.isBlockCardRightCursor(editor);

    if (!at || isBlockCardLeftCursor || isBlockCardRightCursor) {
        return null;
    }

    const currentEntry = TheQueries.getAnchorBlockEntry(editor, at);
    if (!currentEntry || !allowFirstTypes.includes(currentEntry[0].type)) return;
    const [, cursorPath] = currentEntry;

    const parentEntry = TheQueries.getParent(editor, cursorPath);
    const [parentNode] = parentEntry;
    if (!parentNode || (parentNode as Element).type !== listType) return;

    const isFirstChild = TheQueries.isFirstChild(cursorPath);

    if (isFirstChild) {
        return {
            current: currentEntry,
            list: parentEntry
        };
    }
    return null;
};
