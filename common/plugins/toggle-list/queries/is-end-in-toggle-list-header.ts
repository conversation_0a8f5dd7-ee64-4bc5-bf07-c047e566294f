import { TheEditor } from '@worktile/theia';
import { Editor } from 'slate';
import { getFirstNodeAndList } from './get-first-node-and-list';

export const isEndInToggleListHeader = (editor: TheEditor) => {
    const firstAndList = getFirstNodeAndList(editor);
    if (!firstAndList) return false;
    const { current } = firstAndList;
    const [, currentPath] = current;
    const cursor = editor.selection.focus;
    return Editor.isEnd(editor, cursor, currentPath);
};
