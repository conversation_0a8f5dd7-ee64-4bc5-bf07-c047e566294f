import { Editor, Location, Element } from 'slate';
import { TheQueries } from '@worktile/theia';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { ToggleListItemElement } from '@wiki/common/custom-types';
import { isToggleList } from './is-toggle-list';

/**
 * 如果 at（默认 = selection）位于 toggleList > item > p 中，则返回 item 和 toggleList 节点
 */
// export const getListItemEntry = (editor: Editor, { at = editor.selection }: { at?: Location | null } = {}) => {
//     const listItemType = WikiPluginTypes.toggleListItem;
//     if (at && TheQueries.isNodeTypeIn(editor, listItemType, { at })) {
//         const selectionParent = TheQueries.getParent(editor, at);
//         if (!selectionParent) return;
//         const [, paragraphPath] = selectionParent;

//         const listItem = TheQueries.getAboveByType(editor, listItemType, { at }) || TheQueries.getParent(editor, paragraphPath);

//         if (!listItem) return;
//         const [listItemNode, listItemPath] = listItem;

//         if ((listItemNode as ToggleListItemElement).type !== listItemType) return;

//         const list = TheQueries.getParent(editor, listItemPath);
//         if (!list || !isToggleList(list[0] as Element)) return;

//         return {
//             list,
//             listItem
//         };
//     }
// };
