import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ToggleListItemElement } from '@wiki/common/custom-types';
import { TheBaseElement, TheEditor } from '@worktile/theia';

@Component({
    selector: '[wikiCommonToggleListItem]',
    template: '',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiCommonToggleListItemComponent extends TheBaseElement<ToggleListItemElement, TheEditor> implements OnInit {
    ngOnInit() {
        super.ngOnInit();
    }

    // 调用底层的更新 children view 渲染的方法，因为要基于 toggle-list 的状态去渲染（依赖的是父元素的状态），所以没法完全通过数据驱动完成
    updateExpandedState() {
        this.updateChildrenView();
    }
}
