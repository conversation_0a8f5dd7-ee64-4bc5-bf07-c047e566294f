<div class="toggle-list-container">
  <thy-icon
    #icon
    class="toggle-list-status-icon"
    contenteditable="false"
    thyIconName="caret-right"
    (mousedown)="toggleExpanded($event)"
  ></thy-icon>
  <slate-children-outlet></slate-children-outlet>
</div>
@if (!readonly && isExpanded && !hasToggleListItem) {
  <div contenteditable="false" class="empty-toggle-list-item-block" (mousedown)="emptyListItemBlockMousedownHandle($event)">
    <span class="text">{{ emptyListText }}</span>
  </div>
}
