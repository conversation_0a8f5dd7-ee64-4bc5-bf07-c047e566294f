@use 'ngx-tethys/styles/variables.scss';
@use '@worktile/theia/styles/variables.scss' as theia;

.slate-element-toggle-list {
    position: relative;
    padding-left: 20px;
    .toggle-list-container {
        position: relative;

        > [drop-thumb-line-direction='top']::after {
            left: -20px;
            width: calc(100% + 20px);
        }
        > .toggle-list-status-icon {
            position: absolute;
            left: -24px;
            top: 5px;
            margin-right: 5px;
            padding: 2px;
            cursor: pointer;
            opacity: 0.5;

            &:hover {
                background: rgba(variables.$primary, 0.2);
                border-radius: 4px;
            }

            svg {
                transition: transform 200ms ease-out 0s;
            }
        }
    }

    &.has-toggle-list-item {
        > .toggle-list-container > .toggle-list-status-icon {
            opacity: 1;
        }
    }
    &.is-expanded {
        > .toggle-list-container > .toggle-list-status-icon {
            svg {
                transform: rotateZ(90deg);
            }
        }
    }
    .empty-toggle-list-item-block {
        cursor: pointer;
        margin-top: theia.$block-mb;
        border-radius: 2px;
        padding: 0 2px;
        .text {
            color: variables.$gray-500;
        }
        &:hover {
            background-color: rgba(variables.$primary, 0.15);
        }
    }
}
