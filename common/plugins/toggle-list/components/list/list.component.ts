import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    HostBinding,
    inject,
    OnInit,
    Renderer2,
    ViewChild
} from '@angular/core';
import { ToggleListElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { isPrintMode, TheBaseElement, TheEditor } from '@worktile/theia';
import { Editor, Element, Node, Transforms } from 'slate';
import { AngularEditor, ELEMENT_TO_COMPONENT } from 'slate-angular';
import { NODE_TO_ELEMENT } from 'slate-dom';
import { ToggleListEditor } from '../../toggle-list.editor';
import { WikiCommonToggleListItemComponent } from '../list-item/list-item.component';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'div[wikiCommonToggleList]',
    templateUrl: './list.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiCommonToggleListComponent extends TheBaseElement<ToggleListElement, TheEditor> implements OnInit, AfterViewInit {
    translate = inject(StyxTranslateService);

    isExpanded = false;

    emptyListText = this.translate.instant<I18nSourceDefinitionType>('wiki.plugins.toggleList.clickToInput');

    @ViewChild('icon', { read: ElementRef, static: false })
    iconElement: ElementRef<any>;

    get hasToggleListItem() {
        return this.children?.length > 1;
    }

    @HostBinding('class.is-expanded')
    get bindIsExpanded() {
        return this.isExpanded;
    }

    @HostBinding('class.has-toggle-list-item')
    get bindHasToggleListItem() {
        return this.hasToggleListItem;
    }

    private renderer = inject(Renderer2);

    onContextChange() {
        super.onContextChange();
        if (this.initialized) {
            this.getFirstItem();
        }
    }

    ngOnInit() {
        this.isExpanded = isPrintMode(this.editor) ? true : this.element.isExpanded;
        super.ngOnInit();
    }

    ngAfterViewInit() {
        this.getFirstItem();
    }

    toggleExpanded(event: Event) {
        event.preventDefault();
        event.stopPropagation();
        const path = TheEditor.findPath(this.editor, this.element);
        const isInToggleListItem = Editor.above(this.editor, { match: (n: Element) => n.type === WikiPluginTypes.toggleListItem });
        // 折叠时，如果当前光标在 list 中，则移动到 list 的第一个 item 上
        if (this.isExpanded && isInToggleListItem) {
            const firstChildPath = [...path, 0];
            Transforms.select(this.editor, firstChildPath);
            Transforms.collapse(this.editor, { edge: 'end' });
        }
        this.isExpanded = !this.isExpanded;
        if (!this.readonly) {
            Transforms.setNodes(this.editor, { isExpanded: this.isExpanded }, { at: path });
        }
        this.updateListItemExpandedState();
    }

    getFirstItem() {
        Promise.resolve().then(() => {
            const firstItemNode = Node.child(this.element, 0);
            if (!firstItemNode) return;

            const firstItemHTMLElement = NODE_TO_ELEMENT.get(firstItemNode);
            if (!firstItemHTMLElement) return;

            const iconElementHeight = 20;
            const firstItemLineHeight = parseInt(window.getComputedStyle(firstItemHTMLElement).lineHeight, 10);
            const iconElementTop = (firstItemLineHeight - iconElementHeight) / 2 + 'px';
            this.renderer.setStyle(this.iconElement.nativeElement, 'top', iconElementTop);
        });
    }

    updateListItemExpandedState() {
        if (this.element.children.length > 1 && Node.child(this.element, 1)?.type === WikiPluginTypes.toggleListItem) {
            const listItemNode = Node.child(this.element, 1);
            const listItemComponent = ELEMENT_TO_COMPONENT.get(listItemNode) as WikiCommonToggleListItemComponent;
            if (listItemComponent) {
                listItemComponent.updateExpandedState();
            }
        }
    }

    emptyListItemBlockMousedownHandle(event: MouseEvent) {
        event.preventDefault();
        ToggleListEditor.insertToggleListItem(this.editor, this.element);
        Promise.resolve().then(() => {
            const toggleListPath = AngularEditor.findPath(this.editor, this.element);
            const toggleListItemPath = toggleListPath.concat(1);
            Transforms.select(this.editor, Editor.start(this.editor, toggleListItemPath));
            if (!AngularEditor.isFocused(this.editor)) {
                AngularEditor.focus(this.editor);
            }
        });
    }
}
