import { Editor, Element, Range } from 'slate';
import { THE_MODE_TOKEN, TheEditor, TheQueries } from '@worktile/theia';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { isFirstNodeInToggleList } from '../queries/is-first-node-in-toggle-list';
import { isEmptyHeading } from '../queries/is-empty-heading';
import { isMobileMode } from '@wiki/common/util/extension-mode';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

export const toggleListDecorate = (editor: TheEditor) => {
    const decorations = [];
    if (!editor?.readonly && editor.selection && Range.isCollapsed(editor.selection)) {
        const cursorAnchor = editor.selection?.anchor;
        const isFirstNode = isFirstNodeInToggleList(editor, cursorAnchor);
        const isEmptyParagraph = TheQueries.isEmptyParagraph(editor, cursorAnchor);
        const isHeading = isEmptyHeading(editor, cursorAnchor);
        const translate = editor.injector.get(StyxTranslateService);
        if (isFirstNode && (isEmptyParagraph || isHeading)) {
            const start = Editor.start(editor, cursorAnchor);
            decorations.push({
                placeholder: translate.instant<I18nSourceDefinitionType>('wiki.plugins.toggleList.inputTitle'),
                anchor: start,
                focus: start
            });
        }

        const directlyParent = TheQueries.getDirectlyParent(editor);
        if (!isFirstNode && isEmptyParagraph && directlyParent) {
            const [directlyParentNode] = directlyParent;
            const isToggleListItem = (directlyParentNode as Element).type === WikiPluginTypes.toggleListItem;
            const modeConfig = editor.injector.get(THE_MODE_TOKEN);
            let placeholder = translate.instant<I18nSourceDefinitionType>('wiki.plugin.placeholder');
            if (isMobileMode(modeConfig.mode as TheExtensionMode)) {
                placeholder = translate.instant<I18nSourceDefinitionType>('wiki.plugins.toggleList.placeholder');
            }
            if (isToggleListItem) {
                const start = Editor.start(editor, cursorAnchor);
                return [
                    {
                        placeholder,
                        anchor: start,
                        focus: start
                    }
                ];
            }
        }
    }

    return decorations;
};
