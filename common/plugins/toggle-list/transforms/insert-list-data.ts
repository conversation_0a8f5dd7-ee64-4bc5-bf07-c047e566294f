import { Editor, Element, Transforms, Node } from 'slate';
import { <PERSON>ement<PERSON><PERSON><PERSON>, TheEditor, extractFragment } from '@worktile/theia';
import { getFirstNodeAndList } from '../queries/get-first-node-and-list';
import { isFirstNodeInToggleList } from '../queries/is-first-node-in-toggle-list';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { ToggleListElement } from '@wiki/common/custom-types';

export const insertListData = async (editor: TheEditor, data: DataTransfer) => {
    const fragment = await extractFragment(data);
    const isFirstInList = isFirstNodeInToggleList(editor);

    if (isFirstInList && fragment && (fragment.length > 1 || fragment[0].type !== ElementKinds.paragraph)) {
        const listItemElement = {
            type: WikiPluginTypes.toggleListItem,
            children: fragment
        };
        const { current, list } = getFirstNodeAndList(editor);
        const [listNode, listPath] = list;
        const hasListItem = listNode.children.length > 1 && (listNode.children[1] as Element)?.type === WikiPluginTypes.toggleListItem;
        const at = hasListItem ? listPath.concat(1).concat(0) : listPath.concat(1);
        const nodes = hasListItem ? fragment : listItemElement;

        Editor.withoutNormalizing(editor, () => {
            Transforms.insertNodes(editor, nodes as Node[], {
                at,
                select: (listNode as ToggleListElement).isExpanded ? true : false
            });
        });
        return true;
    }
    return false;
};
