import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { TheQueries } from '@worktile/theia';
import { Editor, Path, Transforms, Element } from 'slate';

export const moveListItemChildUp = (editor: Editor) => {
    if (!TheQueries.isParagraph(editor)) return false;

    const cursor = editor.selection.anchor;
    const moveBlockEntry = TheQueries.getAnchorBlockEntry(editor, cursor);
    const listItemEntry = Editor.above<Element>(editor, {
        at: cursor,
        match: (n: Element) => n.type === WikiPluginTypes.toggleListItem,
        mode: 'lowest'
    });
    if (!moveBlockEntry || !listItemEntry) return false;

    const [listItem, listItemPath] = listItemEntry;
    const listItemLength = listItem.children.length;
    if (listItemLength <= 1) return false;

    const [, moveBlockPath] = moveBlockEntry;
    const lastChildPath = listItemPath.concat(listItemLength - 1);
    const isStart = Editor.isStart(editor, cursor, moveBlockPath);
    const isEqual = Path.equals(moveBlockPath, lastChildPath);
    if (!isEqual || !isStart) {
        return false;
    }

    Editor.withoutNormalizing(editor, () => {
        const listPath = Path.parent(listItemPath);
        const nextChildPath = Path.next(listPath);
        Transforms.moveNodes(editor, {
            at: moveBlockPath,
            to: nextChildPath
        });
    });

    return true;
};
