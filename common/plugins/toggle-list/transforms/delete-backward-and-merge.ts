import { Editor, Transforms, Path } from 'slate';
import { TheEditor } from '@worktile/theia';
import { getFoldedToggleListEntry } from '../queries/get-folded-toggle-list-entry';
import { hasBlockCard } from 'slate-angular';

export const deleteBackwardAndMerge = (editor: TheEditor) => {
    const beforePoint = Editor.before(editor, editor.selection);
    if (!beforePoint) return false;

    const beforeEntry = getFoldedToggleListEntry(editor);
    if (beforeEntry) {
        const domSelection = window.getSelection();
        const anchorNode = domSelection.anchorNode;
        if (anchorNode && hasBlockCard(domSelection)) {
            Transforms.select(editor, Editor.end(editor, beforeEntry[1].concat(0)));
        } else {
            const firstChildPath = beforeEntry[1].concat(0);
            Editor.withoutNormalizing(editor, () => {
                Transforms.moveNodes(editor, { at: editor.selection, to: Path.next(firstChildPath) });
                Transforms.mergeNodes(editor);
            });
        }
        return true;
    }

    return false;
};
