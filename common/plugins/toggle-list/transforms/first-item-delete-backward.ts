import { TheEditor, TheQueries } from '@worktile/theia';
import { unwrapToggleList } from './un-wrap-toggle-list';
import { isStartInToggleListHeader } from '../queries/is-start-in-toggle-list-header';

export const firstItemDeleteBackward = (editor: TheEditor) => {
    // 焦点在节点起始，则转换列表为段落
    if (TheQueries.isCollapsed(editor.selection) && isStartInToggleListHeader(editor)) {
        unwrapToggleList(editor);
        return true;
    }

    return false;
};
