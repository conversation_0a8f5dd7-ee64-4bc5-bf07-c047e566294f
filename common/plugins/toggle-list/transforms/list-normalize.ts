import { NodeEntry, Element, Transforms, Editor } from 'slate';
import { TheEditor } from '@worktile/theia';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';

export const listNormalize = (editor: TheEditor, entry: NodeEntry) => {
    const [node, path] = entry;

    if ((node as Element).type === WikiPluginTypes.toggleList) {
        const listChildrenLength = (node as Element).children.length;

        if (listChildrenLength === 1) {
            const listContent = (node as Element).children[0] as Element;
            if (listContent.type === WikiPluginTypes.toggleListItem) {
                Editor.withoutNormalizing(editor, () => {
                    Transforms.unwrapNodes(editor, { at: path.concat(0) });
                    Transforms.unwrapNodes(editor, { at: path });
                });
                return true;
            }
        }

        // const listItemIndex = (node as Element).children.findIndex(child => child.type === WikiPluginTypes.toggleListItem);
        // if (listChildrenLength > 1 && listItemIndex === -1) {
        //     Editor.withoutNormalizing(editor, () => {
        //         Transforms.select(editor, {
        //             anchor: { path: path.concat(1), offset: 0 },
        //             focus: { path: path.concat(listChildrenLength - 1), offset: 0 }
        //         });
        //         Transforms.wrapNodes(editor, { type: WikiPluginTypes.toggleListItem, children: [] });
        //         Transforms.select(editor, Editor.end(editor, path));
        //     });
        //     return true;
        // }
    }
    return false;
};
