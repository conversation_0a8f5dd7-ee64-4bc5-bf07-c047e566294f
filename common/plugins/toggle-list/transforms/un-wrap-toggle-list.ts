import { TheEditor } from '@worktile/theia';
import { Editor, Node, Transforms, Element } from 'slate';
import { getFirstNodeAndList } from '../queries/get-first-node-and-list';

export const unwrapToggleList = (editor: TheEditor) => {
    const firstAndList = getFirstNodeAndList(editor);
    if (!firstAndList) return;

    const { list } = firstAndList;
    const [listNode, listPath] = list;
    Editor.withoutNormalizing(editor, () => {
        Editor.withoutNormalizing(editor, () => {
            const length = listNode.children.length;
            if (length > 1) {
                const listItem = Node.get(editor, listPath.concat(1)) as Element;
                Transforms.unwrapNodes(editor, {
                    at: listPath.concat(1),
                    match: (n: Element) => n.key === listItem.key
                });
            }
            Transforms.unwrapNodes(editor, {
                match: n => Element.isElement(n) && n.key === (listNode as Element).key
            });
        });
    });
};
