import { TheEditor } from '@worktile/theia';
import { createEditor, Descendant, Transforms } from 'slate';
import { withAngular } from 'slate-angular';
import { withToggleList } from '../toggle-list.plugin';
import { correctDataAfterDeletion, correctDataAfterHandle, deleteOpData, noToggleListLitemData } from './list-normalize-data';

describe('toggle-list normalize', () => {
    const editor = withToggleList(withAngular(createEditor()) as TheEditor);

    it(`should ensure that the data format is normal after deletion`, () => {
        editor.children = deleteOpData as Descendant[];
        editor.selection = {
            anchor: { path: [0, 0], offset: 3 },
            focus: { path: [1, 1, 0, 0], offset: 3 }
        };
        Transforms.delete(editor);
        expect(editor.children).toEqual(correctDataAfterDeletion as Descendant[]);
    });

    // it(`should fix no listItem in toggle list`, () => {
    //     editor.children = noToggleListLitemData as Descendant[];
    //     editor.selection = {
    //         anchor: { path: [0, 0, 0], offset: 0 },
    //         focus: { path: [0, 0, 0], offset: 0 }
    //     };
    //     Transforms.insertText(editor, 'A');
    //     expect(editor.children).toEqual(correctDataAfterHandle as Descendant[]);
    // });
});
