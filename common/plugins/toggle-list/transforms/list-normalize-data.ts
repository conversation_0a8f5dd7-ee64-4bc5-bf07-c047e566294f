export const deleteOpData = [
    {
        type: 'paragraph',
        key: 'ntecZ',
        children: [
            {
                text: 'CCCCCC'
            }
        ]
    },
    {
        type: 'toggle-list',
        isExpanded: true,
        key: 'izZnF',
        children: [
            {
                type: 'paragraph',
                key: 'DMnsH',
                children: [
                    {
                        text: 'DDD'
                    }
                ]
            },
            {
                type: 'toggle-list-item',
                children: [
                    {
                        type: 'paragraph',
                        key: 'FaAxA',
                        children: [
                            {
                                text: 'EEE'
                            }
                        ]
                    },
                    {
                        type: 'paragraph',
                        key: 'jGRQE',
                        children: [
                            {
                                text: 'FFF'
                            }
                        ]
                    }
                ]
            }
        ]
    },
    {
        type: 'paragraph',
        key: 'nXKFw',
        children: [
            {
                text: ''
            }
        ]
    }
];

export const correctDataAfterDeletion = [
    {
        type: 'paragraph',
        key: 'ntecZ',
        children: [
            {
                text: 'CCC'
            }
        ]
    },
    {
        type: 'paragraph',
        key: 'jGRQE',
        children: [
            {
                text: 'FFF'
            }
        ]
    },
    {
        type: 'paragraph',
        key: 'nXKFw',
        children: [
            {
                text: ''
            }
        ]
    }
];

export const noToggleListLitemData = [
    {
        type: 'toggle-list',
        isExpanded: true,
        key: 'izZnF',
        children: [
            {
                type: 'paragraph',
                key: 'DMnsH',
                children: [
                    {
                        text: 'DDD'
                    }
                ]
            },
            {
                type: 'paragraph',
                key: 'FaAxA',
                children: [
                    {
                        text: 'EEE'
                    }
                ]
            },
            {
                type: 'paragraph',
                key: 'jGRQE',
                children: [
                    {
                        text: 'FFF'
                    }
                ]
            }
        ]
    },
    {
        type: 'paragraph',
        key: 'nXKFw',
        children: [
            {
                text: ''
            }
        ]
    }
];

export const correctDataAfterHandle = [
    {
        type: 'toggle-list',
        isExpanded: true,
        key: 'izZnF',
        children: [
            {
                type: 'paragraph',
                key: 'DMnsH',
                children: [
                    {
                        text: 'ADDD'
                    }
                ]
            },
            {
                type: 'toggle-list-item',
                children: [
                    {
                        type: 'paragraph',
                        key: 'FaAxA',
                        children: [
                            {
                                text: 'EEE'
                            }
                        ]
                    },
                    {
                        type: 'paragraph',
                        key: 'jGRQE',
                        children: [
                            {
                                text: 'FFF'
                            }
                        ]
                    }
                ]
            }
        ]
    },
    {
        type: 'paragraph',
        key: 'nXKFw',
        children: [
            {
                text: ''
            }
        ]
    }
];
