import { Editor, Path, Transforms, Element, Node } from 'slate';
import { createEmptyParagraph, TheEditor, TheQueries } from '@worktile/theia';
import { ToggleListElement } from '@wiki/common/custom-types';
import { getFirstNodeAndList } from '../queries/get-first-node-and-list';
import { createToggleList } from './create-toggle-list';
import { unwrapToggleList } from './un-wrap-toggle-list';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';

export const firstItemInsertBreak = (editor: TheEditor) => {
    const firstAndList = getFirstNodeAndList(editor);
    if (!firstAndList) return false;

    const { current, list } = firstAndList;
    const [listNode, listPath] = list;
    const [, currentPath] = current;
    const cursor = editor.selection.focus;
    const isStart = Editor.isStart(editor, cursor, currentPath);

    // 首个节点如果为空，则转换列表为段落
    if (TheQueries.isEmptyParagraphByPath(editor, currentPath)) {
        unwrapToggleList(editor);
        return true;
    }

    // 在首个节点起始，则在上方插入新段落
    if (TheQueries.isCollapsed(editor.selection) && isStart) {
        Editor.withoutNormalizing(editor, () => {
            Transforms.insertNodes(editor, createEmptyParagraph(), { at: TheEditor.findPath(editor, listNode) });
        });
        return true;
    }

    // 列表折叠状态, 则在该列表下插入新列表
    if (!(listNode as ToggleListElement).isExpanded) {
        const nextListPath = Path.next(listPath);
        const nextNodePath = Path.next(currentPath);
        Editor.withoutNormalizing(editor, () => {
            Transforms.splitNodes(editor, { at: editor.selection, always: true });
            Transforms.wrapNodes(editor, createToggleList(), { at: nextNodePath });
            Transforms.moveNodes(editor, {
                at: nextNodePath,
                to: nextListPath
            });
        });
        return true;
    }

    // 列表展开状态
    if ((listNode as ToggleListElement).isExpanded) {
        const length = listNode.children.length;

        // 无 listItem 时，拆分段落后创建 listItem
        if (length === 1) {
            Editor.withoutNormalizing(editor, () => {
                Transforms.splitNodes(editor, { at: editor.selection, always: true });
                Transforms.wrapNodes(editor, {
                    type: WikiPluginTypes.toggleListItem,
                    children: []
                });
            });
            return true;
        }
        // 有 listItem 时，拆分段落后移动段落为 listItem 的首个 child
        if (length > 1 && Node.child(listNode, 1).type === WikiPluginTypes.toggleListItem) {
            const listItem = Node.child(listNode, 1);
            const listItemPath = TheEditor.findPath(editor, listItem);
            const listItemPathRef = Editor.pathRef(editor, listItemPath);
            Editor.withoutNormalizing(editor, () => {
                Transforms.splitNodes(editor, { at: editor.selection, always: true });
                Transforms.moveNodes(editor, {
                    to: listItemPathRef.current.concat(0)
                });
            });
            return true;
        }
    }

    return false;
};
