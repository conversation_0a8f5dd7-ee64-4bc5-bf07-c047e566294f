import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { createPluginFactory, TheEditor, ThePluginMenuItem, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';
import { RelationReportEditor } from './relation-report.editor';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';
import { WikiRelationReportComponent } from './relation-report.component';
import { verifyAvailableAppAndPermission } from '@wiki/common/util/common';

export const withRelationReport = <T extends TheEditor>(editor: T): T => {
    const { renderElement, isBlockCard, isVoid } = editor;

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationReport) {
            return WikiRelationReportComponent;
        }
        return renderElement(element);
    };

    editor.isVoid = (element: Element) => {
        return element.type === WikiPluginTypes.relationReport ? true : isVoid(element);
    };

    editor.isBlockCard = (element: Element) => {
        if (element && element.type === WikiPluginTypes.relationReport) {
            return true;
        }
        return isBlockCard(element);
    };
    return editor;
};

export const relationReportMenu = (translate: StyxTranslateService): ThePluginMenuItem => {
    return {
        key: WikiPluginTypes.relationReport,
        keywords: `baobiao,bb,report,insight,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationReport')}`,
        execute: (editor: TheEditor) => RelationReportEditor.selectReport(editor, data => RelationReportEditor.insert(editor, data)),
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationReport'),
        type: ThePluginMenuItemType.group,
        menuIcon: WikiPluginMenuIcons.relationReport,
        displayKey: '/baobiao',
        isDisabled: (editor: TheEditor) => {
            return !verifyAvailableAppAndPermission(translate, editor, WikiPluginTypes.relationReport);
        }
    };
};

export const createRelationReportPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.relationReport,
        withOverrides: withRelationReport,
        menuItems: [relationReportMenu(translate)]
    })();
