import { createEmptyParagraph, TheEditor, TheTransforms } from '@worktile/theia';
import { of } from 'rxjs';
import { Editor, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { RelationReportElement } from '../../custom-types';
import { CommonBroadObjectService } from '../../services/broad-object.service';
import { WikiPluginTypes } from '../../types/editor.types';

export const RelationReportEditor = {
    insert(editor: TheEditor, ids: string[]) {
        const elements: RelationReportElement[] = ids.map(id => ({
            type: WikiPluginTypes.relationReport,
            mode: 'card',
            children: [{ text: '' }],
            _id: id
        }));
        TheTransforms.insertElements(editor, elements);
    },

    selectReport(editor: TheEditor, handle: (ids: string[]) => void, id?: string) {
        const broadObjectService = editor.injector.get(CommonBroadObjectService);
        broadObjectService.opeReportSelection(items => {
            handle(items.map(x => x._id));
            return of(true);
        });
    },

    setAttribute(editor: Editor, element: RelationReportElement, value: { [key: string]: unknown }) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.setNodes(editor, value, { at });
    },

    removeNode(editor: Editor, element: RelationReportElement) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.removeNodes(editor, { at });
        Transforms.insertNodes(editor, createEmptyParagraph(), { at });
        AngularEditor.focus(editor);
    }
};
