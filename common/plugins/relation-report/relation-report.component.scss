@use 'ngx-tethys/styles/variables.scss';

.wiki-relation-report-container {
    width: calc(100% - 2px);
    max-width: 100%;
    user-select: text;

    .common-plugin-card-element {
        width: 100%;
        min-height: 460px;
        height: 460px;

        .plait-toolbar {
            display: none;
        }
        &.slate-focus-element {
            .plait-toolbar {
                display: block;
            }
        }
    }

    &.wiki-fullscreen-node {
        margin: 0 !important;
        .common-plugin-card-element {
            border: none;
            height: calc(100vh - 52px);
        }
    }
    
}
