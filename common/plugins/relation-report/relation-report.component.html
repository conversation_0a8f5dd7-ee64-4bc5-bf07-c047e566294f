<div
  class="common-plugin-card-element"
  thyResizable
  [thyMinHeight]="minHeight"
  [thyBounds]="resizeBounds"
  [style.height.px]="resizeHeight"
  (thyResize)="onResize($event)"
  (thyResizeEnd)="onEndResize()"
>
  @if (!isEmpty) {
    @if (initialState.reportId) {
      <ng-container
        planetComponentOutlet="insight-chart"
        planetComponentOutletApp="insight"
        [planetComponentOutletInitialState]="initialState"
        (planetComponentLoaded)="componentLoaded($event)"
      ></ng-container>
    }
    @if (isCollapsedAndNonReadonly && !isFullscreen) {
      <thy-resize-handle thyDirection="bottom" class="the-resizable-handle-bottom"></thy-resize-handle>
    }
  } @else {
    <thy-empty [thyContainer]="elementRef" thyTopAuto thySize="lg" [thyMessage]="emptyText"></thy-empty>
  }
</div>

<ng-template #toolbar>
  <thy-actions thySize="xxs">
    <a
      href="javascript:;"
      thyAction
      thyActionIcon="arrows-alt"
      styxI18nTracking
      [thyTooltip]="'common.fullScreen' | translate"
      thyTooltipPlacement="top"
      (pointerup)="toggleFullscreen($event)"
    ></a>
    @if (isAvailableApp) {
      <a
        href="javascript:;"
        thyAction
        thyActionIcon="publish"
        styxI18nTracking
        [thyTooltip]="'styx.openNew' | translate"
        (mousedown)="openNewWindow($event)"
      ></a>
    }
    <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      thyTooltipPlacement="top"
      (click)="removeNode($event)"
    ></a>
  </thy-actions>
</ng-template>

<ng-template #placeholderTemplate>
  <div class="common-placeholder-loading">
    <thy-icon thyIconName="task-board"></thy-icon>
  </div>
</ng-template>
