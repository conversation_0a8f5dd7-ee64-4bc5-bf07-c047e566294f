import mermaid from 'mermaid';
import { Observable } from 'rxjs';

let instance: Mermaid | null = null;
export class Mermaid {
    private constructor() {
        this.initialize();
    }

    private initialize() {
        mermaid.initialize({ startOnLoad: true });
    }

    public isVaild(text: string): boolean {
        return mermaid.parse(text);
    }

    public render(id: any, text: string) {
        return new Observable(observer => {
            mermaid.render(id, text, (svgCode, bindFunctions) => {
                observer.next(svgCode);
                (<any>mermaid).parseError = (error: any) => {
                    observer.error(error);
                };
                observer.complete();
            });
        });
    }

    public static getInstance() {
        if (!instance) {
            instance = new Mermaid();
        }
        return instance;
    }
}
