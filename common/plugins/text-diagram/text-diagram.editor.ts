import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { createEmptyParagraph, ElementKinds, TheEditor, TheQueries, TheTransforms } from '@worktile/theia';
import { Editor, Element, Range, Node, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { TextDiagramElement, WikiElement } from '../../custom-types';
import { TextDiagramType } from './constants';

export const TextDiagramEditor = {
    insert(editor: TheEditor) {
        const isSelectContent = Editor.string(editor, editor.selection);
        const [startPoint, endPoint] = Range.edges(editor.selection);
        const elementEntryIterator = Node.elements(editor, {
            from: startPoint.path,
            to: endPoint.path,
            pass: ([node, path]) => {
                return Element.isElement(node) && Editor.isBlock(editor, node);
            }
        });
        const elementEntrys = Array.from(elementEntryIterator);
        const isAllParagraph = elementEntrys.every(item => item[0].type === ElementKinds.paragraph);
        if (isSelectContent && editor.selection.anchor.path.length === 2 && isAllParagraph) {
            let content = elementEntrys.reduce((content, [element, path], index) => {
                const text = Node.string(element);
                return content + (elementEntrys.length - 1 === index ? `${text}` : `${text}\n`);
            }, '');
            const textElement: any = {
                type: WikiPluginTypes.textDiagram,
                content,
                diagramType: TextDiagramType.plantUML,
                children: [{ text: '' }]
            };
            TheTransforms.insertElements(editor, textElement);
        } else {
            const textElement: any = {
                type: WikiPluginTypes.textDiagram,
                content: '',
                diagramType: TextDiagramType.plantUML,
                children: [{ text: '' }]
            };
            TheTransforms.insertElements(editor, textElement);
        }
    },

    setAttribute(editor: Editor, element: TextDiagramElement, value: { [key: string]: unknown }) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.setNodes(editor, value, {
            at
        });
    },

    removeNode(editor: TheEditor, element: Element) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.removeNodes(editor, { at });
        Transforms.insertNodes(editor, createEmptyParagraph(), { at });
        AngularEditor.focus(editor);
        Transforms.select(editor, at);
    },

    isActive(editor: TheEditor) {
        const [textCode] = Editor.nodes(editor, { match: n => Element.isElement(n) && n.type === WikiPluginTypes.textDiagram });
        return !!textCode;
    }
};
