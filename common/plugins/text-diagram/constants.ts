import { StyxTranslateService } from '@atinc/ngx-styx';

/**
 * 页面需要渲染的名称
 */
export enum TextDiagramName {
    plantUML = 'PlantUML',
    graphviz = 'Graphviz',
    mermaid = 'Mermaid',
    flowchart = 'Flowchart'
}

export enum TextDiagramType {
    plantUML = 'plant-uml',
    graphviz = 'graphviz',
    mermaid = 'mermaid',
    flowchart = 'flowchart'
}

export interface TextDiagramTemplate {
    name: string;
    value: string;
}

export interface TextDiagramInfo {
    name: TextDiagramName;
    key: TextDiagramType;
    templates: TextDiagramTemplate[];
}

export const getTextDiagramList = (translate: StyxTranslateService): TextDiagramInfo[] => {
    return [
        {
            name: TextDiagramName.plantUML,
            key: TextDiagramType.plantUML,
            templates: [
                {
                    name: translate.instant('wiki.plugins.textDiagram.templates.sequence.name'),
                    value: `@startuml
    
    autonumber
    
    actor "${translate.instant('wiki.plugins.textDiagram.templates.sequence.actors.user')}" as User
    participant "${translate.instant('wiki.plugins.textDiagram.templates.sequence.actors.browser')}" as Browser
    participant "${translate.instant('wiki.plugins.textDiagram.templates.sequence.actors.server')}" as Server #orange
    
    activate User
    
    User -> Browser: ${translate.instant('wiki.plugins.textDiagram.templates.sequence.actions.inputUrl')}
    activate Browser
    
    Browser -> Server: ${translate.instant('wiki.plugins.textDiagram.templates.sequence.actions.requestServer')}
    activate Server
    
    Server -> Server: ${translate.instant('wiki.plugins.textDiagram.templates.sequence.actions.renderTemplate')}
    note right of Server: ${translate.instant('wiki.plugins.textDiagram.templates.sequence.actions.comment')}
    
    Server -> Browser: ${translate.instant('wiki.plugins.textDiagram.templates.sequence.actions.returnHtml')}
    deactivate Server
    
    Browser --> User
    
    @enduml`
                },
                {
                    name: translate.instant('wiki.plugins.textDiagram.templates.useCase.name'),
                    value: `@startuml
    
    actor A
    actor B
    
    A -up-> (up)
    A -right-> (center)
    A -down-> (down)
    A -left-> (left)
    
    B -up-> (up)
    B -left-> (center)
    B -right-> (right)
    B -down-> (down)
    
    @enduml`
                },
                {
                    name: translate.instant('wiki.plugins.textDiagram.templates.class.name'),
                    value: `@startuml
    
    class Car {
      color
      model
      +start()
      #run()
      #stop()
    }
    
    Car <|- Bus
    Car *-down- Tire
    Car *-down- Engine
    Bus o-down- Driver
    
    @enduml`
                },
                {
                    name: translate.instant('wiki.plugins.textDiagram.templates.flow.name'),
                    value: `@startuml
    
    start
    
    :step 1;
    
    if (try) then (true)
      :step 2;
      :step 3;
    else (false)
      :error;
      end
    endif
    
    stop
    
    @enduml`
                },
                {
                    name: translate.instant('wiki.plugins.textDiagram.templates.activity.name'),
                    value: `@startuml
    
    |A Section|
    start
    :step1;
    |#AntiqueWhite|B Section|
    :step2;
    :step3;
    |A Section|
    :step4;
    |B Section|
    :step5;
    stop
    
    @enduml`
                },
                {
                    name: translate.instant('wiki.plugins.textDiagram.templates.component.name'),
                    value: `@startuml
    
    DataAccess - [First Component]
    [First Component] ..> HTTP : use
    
    @enduml`
                },
                {
                    name: translate.instant('wiki.plugins.textDiagram.templates.state.name'),
                    value: `@startuml
    
    [*] --> State1
    State1 --> [*]
    State1 : this is a string
    State1 : this is another string
    
    State1 -> State2
    State2 --> [*]
    
    @enduml`
                },
                {
                    name: translate.instant('wiki.plugins.textDiagram.templates.object.name'),
                    value: `@startuml
    
    object Car
    object Bus
    object Tire
    object Engine
    object Driver
    
    Car <|- Bus
    Car *-down- Tire
    Car *-down- Engine
    Bus o-down- Driver
    
    @enduml`
                }
            ]
        },
        {
            name: TextDiagramName.graphviz,
            key: TextDiagramType.graphviz,
            templates: [
                {
                    name: 'Finite State Machine',
                    value: `digraph finite_state_machine {
        rankdir=LR;
        size="8,5"
        node [shape = doublecircle]; LR_0 LR_3 LR_4 LR_8;
        node [shape = circle];
        LR_0 -> LR_2 [ label = "SS(B)" ];
        LR_0 -> LR_1 [ label = "SS(S)" ];
        LR_1 -> LR_3 [ label = "S($end)" ];
        LR_2 -> LR_6 [ label = "SS(b)" ];
        LR_2 -> LR_5 [ label = "SS(a)" ];
        LR_2 -> LR_4 [ label = "S(A)" ];
        LR_5 -> LR_7 [ label = "S(b)" ];
        LR_5 -> LR_5 [ label = "S(a)" ];
        LR_6 -> LR_6 [ label = "S(b)" ];
        LR_6 -> LR_5 [ label = "S(a)" ];
        LR_7 -> LR_8 [ label = "S(b)" ];
        LR_7 -> LR_5 [ label = "S(a)" ];
        LR_8 -> LR_6 [ label = "S(b)" ];
        LR_8 -> LR_5 [ label = "S(a)" ];
    }`
                }
            ]
        },
        {
            name: TextDiagramName.mermaid,
            key: TextDiagramType.mermaid,
            templates: [
                {
                    name: 'Flow Chart',
                    value: `graph TD
        A[Start] --> B{Is it?};
        B -->|Yes| C[OK];
        C --> D[Rethink];
        D --> B;
        B ---->|No| E[End];`
                },
                {
                    name: 'Sequence Diagram',
                    value: `sequenceDiagram
        participant John
        participant Alice
        Alice->>John: Hello John, how are you?
        John-->>Alice: Great!`
                },
                {
                    name: 'Class Diagram',
                    value: `classDiagram
        Animal <|-- Duck
        Animal <|-- Fish
        Animal <|-- Zebra
        Animal : +int age
        Animal : +String gender
        Animal: +isMammal()
        Animal: +mate()
        class Duck{
            +String beakColor
            +swim()
            +quack()
        }
        class Fish{
            -int sizeInFeet
            -canEat()
        }
        class Zebra{
            +bool is_wild
            +run()
        }`
                },
                {
                    name: 'State Diagram',
                    value: `stateDiagram-v2
        [*] --> Still
        Still --> [*]
    
        Still --> Moving
        Moving --> Still
        Moving --> Crash
        Crash --> [*]`
                },
                {
                    name: 'ER Diagram',
                    value: `erDiagram
        CUSTOMER ||--o{ ORDER : places
        ORDER ||--|{ LINE-ITEM : contains
        CUSTOMER }|..|{ DELIVERY-ADDRESS : uses`
                },
                {
                    name: 'Gantt',
                    value: `gantt
        title A Gantt Diagram
        dateFormat  YYYY-MM-DD
        section Section
        A task           :a1, 2014-01-01, 30d
        Another task     :after a1  , 20d
        section Another
        Task in sec      :2014-01-12  , 12d
        another task      : 24d`
                },
                {
                    name: 'Pie Chart',
                    value: `pie
        title Key elements in Product X
        "Calcium" : 42.96
        "Potassium" : 50.05
        "Magnesium" : 10.01
        "Iron" :  5`
                }
            ]
        },
        {
            name: TextDiagramName.flowchart,
            key: TextDiagramType.flowchart,
            templates: [
                {
                    name: 'Flowchart',
                    value: `st=>start: Start
    e=>end
    op1=>operation: My Operation
    sub1=>subroutine: My Subroutine
    cond=>condition: Yes or No?
    io=>inputoutput: catch something...
    para=>parallel: parallel tasks
    
    st->op1->cond
    cond(yes)->io->e
    cond(no)->para
    para(path1, bottom)->sub1(right)->op1
    para(path2, top)->op1`
                }
            ]
        }
    ];
};
