<div
  #textDiagramContent
  class="common-plugin-card-element d-flex"
  contenteditable="false"
  thyResizable
  [thyMinHeight]="46"
  [thyBounds]="resizeBounds"
  [style.height.px]="resizeHeight"
  (thyResize)="onResize($event)"
  (thyResizeEnd)="onEndResize()"
  [ngClass]="{ 'change-preview': !isPreview, 'ai-assistant-opened': isAIAssistantOpen() }"
>
  @if (!readonly) {
    <div class="text-diagram-edit-container border-radius-covering-left">
      @if (startRenderCodemirror) {
        <ng-codemirror
          #textCode
          class="ng-codemirror-wrapper"
          contenteditable="false"
          [options]="options"
          [ngModel]="code"
          [delayRefreshTime]="300"
          (ngModelChange)="textChange($event)"
          (focusChange)="focusChange($event)"
          [autoMaxHeight]="maxHeight"
        >
        </ng-codemirror>
      }
    </div>
  }
  <div #scrollContainer class="text-diagram-preview-container align-items-center justify-content-center" [ngClass]="{ readonly: readonly }">
    <!-- 加此 div 是为了超出外层 div 的宽度和高度，id 是 flowchart 要绑定的 -->
    @if (isImageDiagramType()) {
      <div [attr.id]="id" #diagramViewer class="diagram-viewer p-1">
        <img #diagramViewerImage [ngClass]="{ loading: loading() }" (load)="scrollToCenter()" (error)="onerror($event)" draggable="false" />
      </div>
      @if (loading()) {
        <thy-loading></thy-loading>
      }
    } @else {
      <div [attr.id]="id" #diagramViewer class="diagram-viewer p-1"></div>
    }
  </div>
  <!-- 聚焦时才显示 全屏时隐藏 -->
  @if (isCollapsedAndNonReadonly && !isFullscreen) {
    <thy-resize-handle thyDirection="bottom" class="the-resizable-handle-bottom"></thy-resize-handle>
  }
</div>
