<thy-actions class="text-diagram-toolbar align-items-center" contenteditable="false" thySize="xxs">
  <a
    class="ai-assistant mr-0"
    href="javascript:;"
    thyAction
    thyActionIcon="ai-star"
    styxI18nTracking
    [thyTooltip]="'styx.pingcode' | translate"
    (click)="generateFromAiAssistant($event)"
  ></a>
  <thy-divider [thyVertical]="true" thyColor="light" class="mx-2"></thy-divider>
  <the-toolbar-dropdown
    class="mr-1"
    [menus]="diagramInfos"
    [toolbarItem]="selectedDiagramType"
    [dropdownItemKey]="selectedDiagramType?.key"
    [itemMousedownHandle]="changeDiagramType"
  >
  </the-toolbar-dropdown>
  <a
    #diagramTemplate
    thyAction
    thyActiveClass="active"
    href="javascript:;"
    (mousedown)="preventDefault($event)"
    (click)="toggleTemplate($event)"
    styxI18nTracking
  >
    {{ 'styx.template' | translate }} <thy-icon thyIconName="caret-down" class="font-size-sm text-desc ml-1"></thy-icon>
  </a>
  <a
    href="javascript:;"
    thyAction
    thyActionIcon="wiki:two-columns"
    styxI18nTracking
    [thyTooltip]="'styx.preview' | translate"
    thyTooltipPlacement="top"
    [thyActionActive]="isPreview"
    (mousedown)="togglePreview($event)"
  ></a>
  @if (!isFullscreen) {
    <a
      href="javascript:;"
      thyAction
      thyActionIcon="arrows-alt"
      styxI18nTracking
      [thyTooltip]="'common.fullScreen' | translate"
      thyTooltipPlacement="top"
      (mousedown)="setFullscreenHandle($event)"
    ></a>
  }
  @if (!isFullscreen) {
    <a
      class="mr-0"
      href="javascript:;"
      thyAction
      thyActionIcon="copy"
      styxI18nTracking
      [thyTooltip]="'common.copy' | translate"
      thyTooltipPlacement="top"
      (mousedown)="onCopy($event)"
    ></a>
    <thy-divider class="mx-2 align-self-center" thyColor="light" [thyVertical]="true"></thy-divider>
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      thyTooltipPlacement="top"
      (mousedown)="removeNode($event)"
    ></a>
  }
</thy-actions>

<ng-template #dropdownMenuTpl>
  <div class="thy-dropdown-menu">
    @for (template of diagramTemplates; track $index) {
      <a thyDropdownMenuItem href="javascript:;" (mousedown)="preventDefault($event)" (click)="changeTemplate(template.value)">
        <span thyDropdownMenuItemName>{{ template.name }}</span>
      </a>
    }
  </div>
</ng-template>
