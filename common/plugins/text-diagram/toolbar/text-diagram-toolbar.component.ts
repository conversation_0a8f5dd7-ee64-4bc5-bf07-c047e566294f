import { Overlay } from '@angular/cdk/overlay';
import {
    ChangeDetectorRef,
    Component,
    DestroyRef,
    ElementRef,
    inject,
    Input,
    ModelSignal,
    OnDestroy,
    OnInit,
    TemplateRef,
    ViewChild,
    ViewContainerRef
} from '@angular/core';
import { TextDiagramElement } from '@wiki/common/custom-types';
import { bottomLeftPosition, copyNode, topLeftPosition, updatePopoverPosition } from '@worktile/theia';
import { ThyNotifyService } from 'ngx-tethys/notify';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { of, Subscription } from 'rxjs';
import { Editor } from 'slate';
import { FullscreenService, FullscreenState } from '../../../services/fullscreen.service';
import { getTextDiagramList, TextDiagramInfo, TextDiagramType } from '../constants';
import { TextDiagramEditor } from '../text-diagram.editor';
import { NgCodeMirrorOptions } from 'ng-codemirror';
import { AIWritingAssistant, StyxAIAssistantService, StyxTranslateService } from '@atinc/ngx-styx';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
    selector: 'wiki-common-text-diagram-toolbar',
    templateUrl: './text-diagram-toolbar.component.html',
    standalone: false
})
export class WikiCommonTextDiagramToolbarComponent implements OnInit, OnDestroy {
    translate = inject(StyxTranslateService);

    @Input() editor: Editor;

    @Input() element: TextDiagramElement;

    @Input() isPreview: boolean;

    @Input() diagramType: TextDiagramType = TextDiagramType.plantUML;

    @Input() codemirror: NgCodeMirrorOptions;

    @Input() textContentChange: (value: string) => void;

    @Input() diagramTypeChange: (key: TextDiagramType) => void;

    @Input() previewStateChange: () => void;

    @Input() afterAIAssistantClose: () => void;

    @Input() setFullscreenHandle: (event: Event) => void;

    @Input() isAIAssistantOpen: ModelSignal<boolean>;

    @Input() elementRef: ElementRef<any>;

    @ViewChild('dropdownMenuTpl', { static: true }) dropdownMenu: TemplateRef<any>;

    @ViewChild('diagramTemplate') diagramTemplate: any;

    public diagramInfos: TextDiagramInfo[] = getTextDiagramList(this.translate);

    isFullscreen: boolean;

    private fullscreen$: Subscription;

    dropdownPopoverRef: ThyPopoverRef<any>;

    assistantKeysMapByDiagramType = {
        [TextDiagramType.plantUML]: AIWritingAssistant.textDiagramPlantUML,
        [TextDiagramType.graphviz]: AIWritingAssistant.textDiagramGraphviz,
        [TextDiagramType.mermaid]: AIWritingAssistant.textDiagramMermaid,
        [TextDiagramType.flowchart]: AIWritingAssistant.textDiagramFlowchart
    };

    get selectedDiagramType() {
        return this.diagramInfos.find(x => x.key === this.diagramType) || this.diagramInfos[0];
    }

    get diagramTemplates() {
        return this.selectedDiagramType.templates;
    }

    destroyRef = inject(DestroyRef);

    constructor(
        public cdr: ChangeDetectorRef,
        private thyNotifyService: ThyNotifyService,
        private overlay: Overlay,
        private thyPopover: ThyPopover,
        private viewContainerRef: ViewContainerRef,
        private fullscreenService: FullscreenService,
        private aIAssistantService: StyxAIAssistantService
    ) {}

    ngOnInit(): void {
        this.fullscreen$ = this.fullscreenService.subscribe(this.element, value => {
            this.isFullscreen = value.state === FullscreenState.on;
            this.cdr.detectChanges();
        });

        this.aIAssistantService.aiAssistantIsOpen$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((isOpen: boolean) => {
            this.isAIAssistantOpen.set(isOpen);
            if (!isOpen) {
                this.afterAIAssistantClose();
            }
        });
    }

    generateFromAiAssistant(event: Event) {
        this.isAIAssistantOpen.set(true);
        this.aIAssistantService.open({
            origin: event.currentTarget as HTMLElement,
            key: this.assistantKeysMapByDiagramType[this.diagramType],
            action: (text?: string) => {
                if (text) {
                    let result = text;
                    const codeIdentifier = '```';
                    const lines = text.split('\n');
                    const startLineIndex = lines.findIndex(line => line.includes(codeIdentifier));
                    const endLineIndex = lines.lastIndexOf(codeIdentifier);
                    if (startLineIndex !== endLineIndex) {
                        result = lines
                            .slice(startLineIndex + 1, endLineIndex)
                            .join('\n')
                            .trim();
                    }
                    this.textContentChange(result);
                }
                return of(true);
            }
        });
    }

    changeDiagramType = (item: TextDiagramInfo) => {
        this.diagramType = item.key;
        this.diagramTypeChange(item.key);
    };

    preventDefault(event: MouseEvent) {
        event.stopPropagation();
        event.preventDefault();
    }

    togglePreview = (event: MouseEvent) => {
        this.preventDefault(event);
        this.isPreview = !this.isPreview;
        this.cdr.detectChanges();
        this.previewStateChange();
    };

    changeTemplate(value: string) {
        this.textContentChange(value);
        this.thyPopover.close();
    }

    onCopy(event: MouseEvent) {
        this.preventDefault(event);
        copyNode(this.editor, this.element, this.thyNotifyService);
    }

    toggleTemplate(e: Event) {
        this.dropdownPopoverRef = this.thyPopover.open(this.dropdownMenu, {
            origin: e.currentTarget as HTMLElement,
            originActiveClass: 'active',
            placement: 'bottomLeft',
            insideClosable: false,
            outsideClosable: true,
            backdropClosable: true,
            hasBackdrop: false,
            offset: 10,
            minWidth: 0,
            viewContainerRef: this.viewContainerRef,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });

        const origin = this.diagramTemplate.elementRef.nativeElement;
        const overlayRef = this.dropdownPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [bottomLeftPosition, topLeftPosition]);
    }

    removeNode(event: MouseEvent) {
        this.preventDefault(event);
        TextDiagramEditor.removeNode(this.editor, this.element);
    }

    ngOnDestroy() {
        this.fullscreen$.unsubscribe();
    }
}
