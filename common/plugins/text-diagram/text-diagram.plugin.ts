import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { createPluginFactory, TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { TextDiagramEditor } from './text-diagram.editor';
import { Element } from 'slate';
import { WikiCommonTextDiagramComponent } from './text-diagram.component';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withTextDiagram = <T extends TheEditor>(editor: T): T => {
    const { renderElement, isBlockCard, isVoid } = editor;

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.textDiagram) {
            return WikiCommonTextDiagramComponent;
        }
        return renderElement(element);
    };

    editor.isVoid = (element: Element) => {
        return element.type === WikiPluginTypes.textDiagram ? true : isVoid(element);
    };

    editor.isBlockCard = (element: Element) => {
        if (element && element.type === WikiPluginTypes.textDiagram) {
            return true;
        }
        return isBlockCard(element);
    };
    return editor;
};

export const createTextDiagramPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.textDiagram,
        withOverrides: withTextDiagram,
        toolbarItems: [
            {
                key: WikiPluginTypes.textDiagram,
                icon: 'wiki:text-drawing',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.textDiagram'),
                execute: (editor: TheEditor) => TextDiagramEditor.insert(editor)
            }
        ],
        menuItems: [
            {
                key: WikiPluginTypes.textDiagram,
                keywords: `wenbenhuitu,wbht,text drawing,textdrawing,draw,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.textDiagram')}`,
                execute: (editor: TheEditor) => TextDiagramEditor.insert(editor),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.textDiagram'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.textDiagram,
                displayKey: '/wbht'
            }
        ],
        options: {
            // 允许插入的元素 #WIK-8053
            allowParentTypes: [WikiPluginTypes.layoutColumn, WikiPluginTypes.toggleListItem],
            showFullscreen: true
        }
    })();
