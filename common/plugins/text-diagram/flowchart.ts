import * as FlowChart from 'flowchart.js';
import { Observable, Subscriber } from 'rxjs';

/**
 * 如果有多个容器推荐创建多个实例，每个实例要维护当前实例绘制 SVG（可能是多个）的生命周期
 */
export class Flowchart {
    private instance: FlowChart.Instance;

    // 维护渲染状态主要是防止清除节点报错（FlowChart 底层没有维护渲染状态）
    private isRendered = false;

    constructor(private id: string) {}

    /**
     * 渲染 SVG
     * @param text 根据文本绘制 SVG
     * @param options 可以设置宽高、大小、颜色等样式
     * @returns Observable<string | Error>
     */
    render(text: string, options?: FlowChart.DrawOptions, isClean = true): Observable<any> {
        const handle = (observer: Subscriber<any>) => {
            try {
                this.instance = FlowChart.parse(text);
                this.instance.drawSVG(this.id, options);
                observer.next('success');
                this.isRendered = true;
            } catch (error) {
                observer.error(error);
            }
            observer.complete();
        };
        return new Observable(observer => {
            isClean ? this.clean().subscribe(() => handle(observer)) : handle(observer);
        });
    }

    /**
     * 注意：如果不主动调 clean 清除，则下次绘制会保留当前的 SVG
     * chartflow clean 实现：
     *  1. 获取 id 对应的容器 A
     *  2. 获取 svg node 节点 B
     *  3. 执行 A.removeChild(B)
     */
    public clean(): Observable<void> {
        return new Observable(observer => {
            if (this.isRendered) {
                this.isRendered = false;
                this.instance?.clean();
            }
            observer.next();
            observer.complete();
        });
    }
}
