@use 'ngx-tethys/styles/variables.scss';

.slate-element-text-diagram {
    display: block;

    .common-plugin-card-element {
        width: 100%;
        .text-diagram-edit-container {
            max-height: unset;
            width: 50%;
            border-color: variables.$gray-300;
            background: rgba(243, 243, 243, 1);
            display: block;
            transition: width 314ms ease;

            .ng-codemirror-wrapper {
                border-radius: inherit;
                display: block;
                position: relative;
                overflow: auto;
                height: 100%;
                width: 100%;
            }

            .cm-editor {
                border: none;
                height: 100% !important;
                color: variables.$gray-600;
                background: transparent;
                border-right: 1px solid variables.$gray-300;

                .cm-gutters {
                    background-color: rgba(243, 243, 243, 1);
                    border-right: 1px rgba(240, 240, 240, 1) solid;
                    .cm-lineNumbers {
                        .cm-gutterElement {
                            min-width: 36px;
                            font-size: 15px;
                            color: variables.$gray-600;
                            text-align: center;
                        }
                    }
                    .cm-foldGutter {
                        display: none !important;
                    }
                }

                .cm-content {
                    padding: 10px 0;

                    .cm-line {
                        color: variables.$gray-600;
                        padding: 0 4px 0 10px;
                        height: 22px;
                        line-height: 22px;
                        font-size: 15px;
                    }
                }
            }
        }

        .text-diagram-preview-container {
            display: flex;
            width: 50%;
            overflow: auto;
            .diagram-viewer {
                max-height: 100%;
                max-width: 100%;
                margin: 0 auto;
                #mermaid {
                    width: auto;
                }
            }

            .loading {
                visibility: hidden;
                width: 0;
            }
        }

        .text-diagram-preview-container.readonly {
            width: 100%;
        }
        &.change-preview {
            .text-diagram-edit-container {
                width: 100%;
                .cm-editor {
                    border: 0;
                }
            }

            .text-diagram-preview-container {
                display: hidden;
                width: 0;
            }
        }
        &.ai-assistant-opened {
            border-color: variables.$primary;
        }
    }
}
