import { Overlay } from '@angular/cdk/overlay';
import { HttpClient } from '@angular/common/http';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    computed,
    effect,
    ElementRef,
    HostListener,
    model,
    NgZone,
    OnDestroy,
    OnInit,
    Signal,
    signal,
    viewChild,
    ViewChild,
    ViewContainerRef
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AppRootContext } from '@atinc/ngx-styx';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { isMobileMode } from '@wiki/common/util/extension-mode';
import { getDrawingApi } from '@wiki/common/util/get-drawing-api';
import {
    TheBaseElement,
    TheContextService,
    TheModeType,
    TheQueries,
    getMode,
    idCreator,
    topLeftPosition,
    updatePopoverPosition
} from '@worktile/theia';
import * as _lodash from 'lodash';
import { CodeMirrorComponent } from 'ng-codemirror';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { ThyResizeEvent } from 'ngx-tethys/resizable';
import { Subject, Subscription } from 'rxjs';
import { finalize, take } from 'rxjs/operators';
import { Editor, Transforms } from 'slate';
import { AngularEditor, SafeAny } from 'slate-angular';
import { TextDiagramElement } from '../../custom-types';
import { WikiPluginContext } from '../../services/context/plugin.context';
import { FullscreenService, FullscreenState } from '../../services/fullscreen.service';
import { TextDiagramType } from './constants';
import { Flowchart } from './flowchart';
import { Mermaid } from './mermaid';
import { TextDiagramEditor } from './text-diagram.editor';
import { WikiCommonTextDiagramToolbarComponent } from './toolbar/text-diagram-toolbar.component';

@Component({
    selector: 'wiki-common-text-diagram',
    templateUrl: './text-diagram.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiCommonTextDiagramComponent extends TheBaseElement<TextDiagramElement, Editor> implements OnInit, AfterViewInit, OnDestroy {
    diagramPopoverRef: ThyPopoverRef<any>;

    maxHeight = 500;

    public isPreview: boolean = true;

    options = {
        mode: '',
        lineNumbers: true,
        readOnly: false,
        autofocus: false,
        lineWiseCopyCut: true,
        lineWrapping: false,
        cursorBlinkRate: 500
    };

    resizeBounds = null;

    resizeHeight: number;

    destroy$ = new Subject<void>();

    startRenderCodemirror = false;

    loading = signal(false);

    private fullscreen$: Subscription;

    @ViewChild('textCode', { read: CodeMirrorComponent, static: false })
    textCode: CodeMirrorComponent;

    @ViewChild('textDiagramContent', { static: true })
    textDiagramContent: ElementRef;

    diagramViewerImage: Signal<ElementRef<HTMLImageElement>> = viewChild('diagramViewerImage');

    scrollContainer = viewChild<ElementRef<HTMLElement>>('scrollContainer');

    diagramViewer: Signal<ElementRef<HTMLElement>> = viewChild('diagramViewer');

    @HostListener('mousedown', ['$event'])
    mousedown(event) {
        if ((event.target as HTMLElement).className.includes('thy-resizable-handle')) {
            return;
        }
        event.stopPropagation();
        event.preventDefault();
        if (!this.readonly) {
            const isTextCode = this.nativeElement.querySelector('.ng-codemirror').contains(event.target as HTMLElement);
            if (!isTextCode) {
                this.focusCode();
            }
        }
    }

    get code() {
        return this.element.content;
    }

    get isToolbarOpen() {
        return this.diagramPopoverRef && this.diagramPopoverRef.getOverlayRef() && this.diagramPopoverRef.getOverlayRef().hasAttached();
    }

    isAIAssistantOpen = model<boolean>(false);

    mermaid: Mermaid;

    flowchart: Flowchart;

    id: string;

    isFullscreen: boolean;

    mode: TheModeType;

    get isBlob() {
        return this.mode === TheExtensionMode.print || this.diagramType() === TextDiagramType.graphviz;
    }

    constructor(
        public cdr: ChangeDetectorRef,
        private ngZone: NgZone,
        private overlay: Overlay,
        private contextService: TheContextService,
        private route: ActivatedRoute,
        private thyPopover: ThyPopover,
        public appRootContext: AppRootContext,
        private fullscreenService: FullscreenService,
        private wikiPluginContext: WikiPluginContext,
        private http: HttpClient,
        public viewContainerRef: ViewContainerRef
    ) {
        super();
        this.id = idCreator(5);
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.mermaid = Mermaid.getInstance();
        this.flowchart = new Flowchart(this.id);
        this.fullscreen$ = this.fullscreenService.subscribe(this.element, value => {
            this.isFullscreen = value.state === FullscreenState.on;
            if (value.state === FullscreenState.off) {
                this.updatePositionStrategy();
            }
        });
    }

    ngAfterViewInit(): void {
        if (!this.readonly) {
            // edit mode can not delay
            this.ngZone.onStable.pipe(take(1)).subscribe(() => {
                // 自动聚焦
                this.initializeCodemirrorFocus();
                this.resizeBounds = {
                    nativeElement: this.contextService.getEditableElement()
                };
                this.startRenderCodemirror = true;
                this.resizeHeight = this.element.height;
                this.cdr.detectChanges();
            });
        }
        setTimeout(() => this.renderDiagram());
    }

    onContextChange() {
        super.onContextChange();
        this.diagramType.set(this.element.diagramType || TextDiagramType.plantUML);
        if (!this.mode) {
            this.mode = getMode(this.editor);
        }
        if (isMobileMode(this.mode)) {
            return;
        }
        if (this.initialized) {
            // 解决选中多个文本绘图出现多个工具栏的处理
            if (this.isToolbarOpen && !this.isCollapsedAndNonReadonly && !this.isAIAssistantOpen()) {
                this.diagramPopoverRef.close();
            }
            if ((!this.isToolbarOpen && this.isCollapsedAndNonReadonly) || this.isAIAssistantOpen()) {
                this.openDiagramToolbar();
            }
        }
    }

    diagramType = signal(TextDiagramType.plantUML);

    isImageDiagramType = computed(() => {
        return this.diagramType() === TextDiagramType.plantUML || this.diagramType() === TextDiagramType.graphviz;
    });

    diagramTypeChange = (key: TextDiagramType) => {
        this.cleanDiagram();
        TextDiagramEditor.setAttribute(this.editor, this.element, { diagramType: key });
    };

    private changeTextAndRender(value?: string) {
        // 没有开启预览模式，就不用渲染视图了
        if ((value && value === this.code) || !this.isPreview) {
            return;
        }
        // 绘制前，必须清除上一次绘制的 SVG（如果有的话）
        if (this.diagramType() !== TextDiagramType.flowchart) {
            this.loading.set(true);
            this.flowchart.clean().subscribe();
        }

        if (value) {
            TextDiagramEditor.setAttribute(this.editor, this.element, { content: value });
        }
        setTimeout(() => this.renderDiagram());
    }

    textContentChange = (value: string) => {
        this.changeTextAndRender(value);
    };

    renderDiagram() {
        switch (this.diagramType()) {
            case TextDiagramType.plantUML:
            case TextDiagramType.graphviz:
                this.renderPlantumlOrGraphviz();
                break;
            case TextDiagramType.mermaid:
                this.renderMermaid();
                break;
            case TextDiagramType.flowchart:
                this.renderFlowchart();
                break;
            default:
                break;
        }
    }

    cleanDiagram() {
        if (this.isImageDiagramType()) {
            this.diagramViewerImage().nativeElement.src = '';
        } else {
            if (this.diagramType() === TextDiagramType.flowchart) {
                this.flowchart.clean().subscribe();
            } else {
                this.diagramViewer().nativeElement.innerHTML = '';
            }
        }
    }

    private renderFlowchart() {
        // 内容为空时，要清除 SVG
        if (this.code?.trim() === '') {
            this.flowchart.clean().subscribe();
            return;
        }
        this.flowchart.render(this.code, undefined).subscribe();
    }

    private renderMermaid() {
        if (!this.code || !this.code.trim()) {
            this.diagramViewer().nativeElement.innerHTML = '';
            return;
        }
        this.mermaid.render(`mermaid-${this.id}`, this.code).subscribe(
            (result: string) => {
                this.loading.set(false);
                this.diagramViewer().nativeElement.innerHTML = result;
                this.scrollToCenter();
            },
            (err: any) => {
                console.error('语法错误: ', err);
                this.diagramViewerImage().nativeElement.src = '';
                this.loading.set(false);
                this.cdr.detectChanges();
            }
        );
    }

    private renderPlantumlOrGraphviz() {
        if (this.code?.trim()) {
            this.loading.set(true);
            let pageId = this.wikiPluginContext.getPageId();
            let spaceId = this.wikiPluginContext.getSpaceId();
            // 外部共享的取路由短 id
            if (/^outside-(space|page)/.test(this.mode)) {
                pageId = this.route.snapshot.paramMap.get('pageIdOrShortId') || '';
                spaceId = this.route.parent.snapshot.paramMap.get('sid') || '';
            }
            const apiUrl = getDrawingApi({
                appRootContext: this.appRootContext,
                mode: this.mode,
                type: this.diagramType(),
                spaceId,
                pageId,
                printResourceType: this.diagramType() === TextDiagramType.graphviz ? 'png' : 'svg'
            });
            this.http
                .post(
                    apiUrl,
                    { code: this.code },
                    {
                        responseType: (this.isBlob ? 'blob' : 'text') as SafeAny
                    }
                )
                .pipe(
                    finalize(() => {
                        this.loading.set(false);
                    })
                )
                .subscribe({
                    next: data => {
                        this.setViewImageSrc(data as SafeAny);
                    },
                    error: data => {
                        this.setViewImageSrc(data.error);
                    }
                });
        }
    }

    setViewImageSrc(data: Blob | string) {
        if (this.isBlob) {
            const reader = new FileReader();
            reader.onload = () => {
                this.diagramViewerImage().nativeElement.src = reader.result as string;
            };
            reader.readAsDataURL(data as Blob);
        } else {
            this.diagramViewerImage().nativeElement.src = `data:image/svg+xml,${encodeURIComponent(data as string)}`;
        }
    }

    initializeCodemirrorFocus() {
        if (isMobileMode(this.mode)) {
            return;
        }
        if (this.isCollapsedAndNonReadonly) {
            this.openDiagramToolbar();
            setTimeout(() => {
                if (this.isCollapsedAndNonReadonly && this.textCode?.view && !this.textCode.view.hasFocus) {
                    this.focusCode();
                }
            }, 200);
        }
    }

    afterAIAssistantClose = () => {
        if (!this.readonly) {
            this.focusCode();
        }
    };

    focusCode() {
        AngularEditor.blur(this.editor);
        this.textCode.view.focus();
        this.textCode.view.requestMeasure();
    }

    onResize({ height }: ThyResizeEvent) {
        this.resizeHeight = height;
        this.maxHeight = height;
    }

    onEndResize() {
        Transforms.select(this.editor, AngularEditor.findPath(this.editor, this.element));
        TextDiagramEditor.setAttribute(this.editor, this.element, { height: this.resizeHeight });
    }

    scrollToCenter() {
        const scrollContainerElement = this.scrollContainer().nativeElement;
        const imgOrSVGElement = this.diagramViewer().nativeElement.firstElementChild;
        const { width, height } = imgOrSVGElement.getBoundingClientRect();
        scrollContainerElement.scrollLeft = (width - scrollContainerElement.clientWidth) * 0.5;
        scrollContainerElement.scrollTop = (height - scrollContainerElement.clientHeight) * 0.5;
    }

    onerror(event) {
        this.loading.set(false);
    }

    textChange = _lodash.debounce((text: string) => {
        this.changeTextAndRender(text);
    }, 1000);

    focusChange(textCodeFocused: boolean) {}

    openDiagramToolbar() {
        if (!TheQueries.isGlobalCollapsed(this.editor) || this.isToolbarOpen) {
            return;
        }

        const panelClass = ['diagram-toolbar-container', 'the-plugin-toolbar-popover', this.isFullscreen ? 'wiki-fullscreen-hidden' : ''];
        const origin = this.textDiagramContent.nativeElement as HTMLElement;
        this.diagramPopoverRef = this.thyPopover.open(WikiCommonTextDiagramToolbarComponent, {
            initialState: {
                editor: this.editor,
                element: this.element,
                elementRef: this.elementRef,
                isPreview: this.isPreview,
                diagramType: this.diagramType(),
                codemirror: this.textCode?.view,
                isAIAssistantOpen: this.isAIAssistantOpen,
                previewStateChange: this.previewStateChange,
                textContentChange: this.textContentChange,
                diagramTypeChange: this.diagramTypeChange,
                setFullscreenHandle: this.setFullscreenHandle,
                afterAIAssistantClose: this.afterAIAssistantClose
            },
            origin,
            viewContainerRef: this.viewContainerRef,
            minWidth: 0,
            panelClass: panelClass,
            placement: 'topLeft',
            hasBackdrop: false,
            manualClosure: true,
            insideClosable: false,
            outsideClosable: false,
            scrollStrategy: this.overlay.scrollStrategies.reposition(),
            autoAdaptive: true
        });

        this.updatePositionStrategy();
    }

    updatePositionStrategy() {
        const overlayRef = this.diagramPopoverRef?.getOverlayRef();
        const origin = this.textDiagramContent.nativeElement as HTMLElement;
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    previewStateChange = () => {
        this.isPreview = !this.isPreview;
        this.changeTextAndRender();
        this.cdr.detectChanges();
    };

    setFullscreenHandle = (event: MouseEvent) => {
        event.stopPropagation();
        event.preventDefault();
        this.fullscreenService.setFullscreen(this.editor, event, this.element, true);
    };

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
        this.fullscreen$.unsubscribe();
    }
}
