import { Text } from 'slate';
import { createPluginFactory, TheEditor } from '@worktile/theia';
import { AngularEditor } from 'slate-angular';
import { HoveringToolbarActionTypes } from '@wiki/common/types/editor.types';
import { WikiCommonDiscussionLeafComponent } from './leaf/leaf.component';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';

export const withDiscussion = (editor: TheEditor) => {
    const { renderLeaf } = editor;

    editor.renderLeaf = (text: Text) => {
        const isReadonly = AngularEditor.isReadOnly(editor);
        if (isReadonly && text.type === HoveringToolbarActionTypes.discussion) {
            return WikiCommonDiscussionLeafComponent;
        }

        return renderLeaf(text);
    };

    return editor;
};

export const createDiscussionPlugin = createPluginFactory({
    key: CustomPluginKeys.discussion,
    withOverrides: withDiscussion
});
