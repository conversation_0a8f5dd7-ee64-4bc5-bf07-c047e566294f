import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Editor, Node, Element } from 'slate';
import { TheEditor } from '@worktile/theia';
import { Discussion } from '@wiki/common/custom-types';
import { HoveringToolbarActionTypes } from '@wiki/common/types/editor.types';

export const DiscussionEditor = {
    discussionDecorate(editor: TheEditor, nodeEntry: NodeEntry, discussions: Discussion[]) {
        if (!editor || !nodeEntry) {
            return;
        }
        const [currentNode, path] = nodeEntry;
        let decorations = [];

        if (
            discussions &&
            Element.isElement(currentNode) &&
            Editor.isBlock(editor, currentNode) &&
            discussions.some(n => n.node_key === currentNode.key)
        ) {
            discussions.forEach(discussion => {
                if (discussion.node_key === currentNode.key) {
                    let range;
                    let matchText;
                    if (discussion.node_key === discussion.lowest_node_key && currentNode.children[discussion.range.anchor.path[0]]) {
                        range = {
                            anchor: {
                                path: [...path, ...discussion.range.anchor.path],
                                offset: discussion.range.anchor.offset
                            },
                            focus: {
                                path: [...path, ...discussion.range.focus.path],
                                offset: discussion.range.focus.offset
                            }
                        };
                    } else {
                        let lowstBlockEntry;
                        for (const node of Node.descendants(currentNode)) {
                            if (Element.isElement(node[0]) && node[0].key === discussion.lowest_node_key) {
                                lowstBlockEntry = node;
                                break;
                            }
                        }
                        if (lowstBlockEntry && lowstBlockEntry[0].children[discussion.range.anchor.path[0]]) {
                            range = {
                                anchor: {
                                    path: [...path, ...lowstBlockEntry[1], ...discussion.range.anchor.path],
                                    offset: discussion.range.anchor.offset
                                },
                                focus: {
                                    path: [...path, ...lowstBlockEntry[1], ...discussion.range.focus.path],
                                    offset: discussion.range.focus.offset
                                }
                            };
                        } else {
                            return;
                        }
                    }

                    try {
                        matchText = Editor.string(editor, range);
                    } catch (error) {}

                    if (matchText === discussion.quote) {
                        decorations.push({
                            ...discussion,
                            type: HoveringToolbarActionTypes.discussion,
                            ...range
                        });
                    }
                }
            });
        }

        return decorations;
    }
};
