import { Component, HostBinding, HostListener, inject, OnInit, Renderer2 } from '@angular/core';
import { Discussion } from '@wiki/common/custom-types';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { Node } from 'slate';
import { AngularEditor, BaseLeafComponent } from 'slate-angular';

@Component({
    selector: 'wiki-common-discussion-leaf, [wikiCommonDiscussionLeaf]',
    template: `<span slateString [context]="context" [viewContext]="viewContext"><span></span></span>`,
    standalone: false
})
export class WikiCommonDiscussionLeafComponent extends BaseLeafComponent implements OnInit {
    rootNode: Node;

    rootDOM: HTMLElement;

    @HostBinding('class') class = 'the-discussion-leaf';

    @HostBinding('attr.discussion-id') get discussionId() {
        return this.leaf?._id;
    }

    @HostListener('mouseenter', ['$event'])
    mouseover(event) {
        this.renderer.addClass(this.nativeElement, 'highlight');
    }

    @HostListener('mouseleave', ['$event']) mouseleave(event) {
        this.renderer.removeClass(this.nativeElement, 'highlight');
    }

    @HostListener('click', ['$event']) onClick(event) {
        const native = window.getSelection();
        if (native.type !== 'Range') {
            this.wikiPluginContext.openDiscussionList(event.currentTarget, this.leaf as Discussion);
        }
    }

    public renderer = inject(Renderer2);
    public wikiPluginContext = inject(WikiPluginContext);

    changeBackground(color: string) {
        this.renderer.setStyle(this.nativeElement, 'backgroundColor', color);
    }

    ngOnInit() {
        const textPath = AngularEditor.findPath(this.editor, this.text);
        this.rootNode = Node.get(this.editor as Node, [textPath[0]]);
        this.rootDOM = AngularEditor.toDOMNode(this.editor, this.rootNode);
    }
}
