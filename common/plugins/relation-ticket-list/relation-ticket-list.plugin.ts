import { createPluginFactory, TheEditor, ThePluginMenuItem, ThePluginMenuItemType } from '@worktile/theia';
import { WikiPluginTypes } from '../../types/editor.types';
import { WikiPluginMenuIcons } from '../../types/plugin-menu';
import { CustomPluginKeys } from '../../types/plugins.types';
import { RelationTicketListEditor } from './relation-ticket-list.editor';
import { Element } from 'slate';
import { CommonRelationListComponent } from '../../components/relation-list/relation-list.component';
import { verifyAvailableAppAndPermission } from '../../util/common';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

const withRelationTicketList = <T extends TheEditor>(editor: T): T => {
    const { renderElement, isVoid, isBlockCard } = editor;

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationTicketList) {
            return CommonRelationListComponent;
        }
        return renderElement(element);
    };

    editor.isVoid = (element: Element) => {
        return element.type === WikiPluginTypes.relationTicketList ? true : isVoid(element);
    };

    editor.isBlockCard = (element: Element) => {
        if (element && element.type === WikiPluginTypes.relationTicketList) {
            return true;
        }
        return isBlockCard(element);
    };
    return editor;
};

export const relationTicketListMenuItem = (translate: StyxTranslateService): ThePluginMenuItem => {
    return {
        key: WikiPluginTypes.relationTicketList,
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationTicketList'),
        type: ThePluginMenuItemType.group,
        keywords: `gongdanliebiao,gdlb,ticketlist,ticket,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationTicketList')}`,
        menuIcon: WikiPluginMenuIcons.relationTicketList,
        displayKey: '/gdlb',
        execute: (editor: TheEditor) => RelationTicketListEditor.selectItems(editor, data => RelationTicketListEditor.insert(editor, data)),
        isDisabled: (editor: TheEditor) => {
            return !verifyAvailableAppAndPermission(translate, editor, WikiPluginTypes.relationTicket);
        }
    };
};

export const createRelationTicketListPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        withOverrides: withRelationTicketList,
        menuItems: [relationTicketListMenuItem(translate)],
        key: CustomPluginKeys.relationTicketList
    })();
