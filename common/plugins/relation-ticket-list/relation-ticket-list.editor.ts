import { ShipBroadObjectTypes } from '@atinc/ngx-styx';
import { PageExtensionInfo, PageExtensionReferences, PageExtensionType, TicketListExtension } from '@wiki/app/entities/page-extendsion';
import { PageApiService } from '@wiki/app/services';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { THE_MODE_TOKEN, TheEditor, TheTransforms, idCreator } from '@worktile/theia';
import { of } from 'rxjs';
import { Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { DefaultPropertyColumns } from '../../custom-constants';
import { RelationTicketListElement } from '../../custom-types';
import { CommonBroadObjectService } from '../../services/broad-object.service';
import { PageStore } from '../../stores/page.store';
import { RelationListEditor } from '../../types';
import { WikiPluginTypes } from '../../types/editor.types';

export const RelationTicketListEditor: RelationListEditor<TicketListExtension> = {
    insert(editor: TheEditor, data: PageExtensionInfo) {
        const element: RelationTicketListElement = {
            extension_id: data._id,
            type: WikiPluginTypes.relationTicketList,
            data: {
                columns: DefaultPropertyColumns[WikiPluginTypes.relationTicketList]
            },
            children: [{ text: '' }]
        };
        TheTransforms.insertElements(editor, element);
    },

    selectItems<RelationTicketInfo>(
        editor: TheEditor,
        handle?: (data: PageExtensionInfo<TicketListExtension>, references?: PageExtensionReferences) => void,
        elementKey?: string,
        selectedItems?: RelationTicketInfo[]
    ) {
        const pageService = editor.injector.get(PageApiService);
        const pageStore = editor.injector.get(PageStore);
        const broadObjectService = editor.injector.get(CommonBroadObjectService);
        const pageId = pageStore.snapshot.page._id;
        const modeConfig = editor.injector.get(THE_MODE_TOKEN);
        const mode = modeConfig.mode as TheExtensionMode;

        broadObjectService.openShipSelection(
            ShipBroadObjectTypes.ticket,
            selectedItems,
            relationItems => {
                pageService
                    .savePageExtension<TicketListExtension>(
                        pageId,
                        {
                            key: elementKey || idCreator(),
                            type: PageExtensionType.ticketList,
                            data: { ticket_ids: relationItems?.map(x => x._id) || [] }
                        },
                        mode
                    )
                    .subscribe(result => result && handle(result.value, result.references));
                return of(true);
            },
            { principalId: pageId }
        );
    },

    setAttribute(editor: TheEditor, element: RelationTicketListElement, value: { [key: string]: unknown }) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.setNodes(editor, value, { at });
    }
};
