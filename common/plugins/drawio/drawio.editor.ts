import { PLViewportMode } from '@plait-editor/types/editor';
import { BaseViewport } from '@plait/core';
import { DrawioElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { TheEditor, TheTransforms } from '@worktile/theia';
import { Editor, Element, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';

interface DrawioAttribute {
    data: any;
    viewport: BaseViewport;
    height: number;
    viewportMode?: PLViewportMode;
}

export const DrawioEditor = {
    insert(editor: Editor, type: WikiPluginTypes) {
        const elements: DrawioElement = {
            type: WikiPluginTypes.drawio,
            data: {},
            children: [{ text: '' }]
        };
        TheTransforms.insertElements(editor, elements);
    },
    setAttribute(editor: Editor, element: DrawioElement, data: Partial<DrawioAttribute>) {
        Transforms.setNodes(editor, data, {
            at: AngularEditor.findPath(editor, element)
        });
    },
    removeNode(editor: Editor, element: Element) {
        Transforms.move(editor, { reverse: true });
        const selection = editor.selection;
        const path = TheEditor.findPath(editor, element);
        Transforms.removeNodes(editor, { at: path });
        Transforms.select(editor, selection);
        TheEditor.focus(editor);
    }
};
