import { TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { NestedKey } from '@atinc/ngx-styx';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { createPluginFactory } from '@worktile/theia';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { DrawioEditor } from './drawio.editor';
import { DrawioElement } from '@wiki/common/custom-types';
import { WikiCommonDrawioComponent } from './drawio.component';

export const withDrawio = <T extends TheEditor>(editor: T): T => {
    const { renderElement, isBlockCard, isVoid } = editor;

    editor.isVoid = (element: DrawioElement) => {
        return element.type === WikiPluginTypes.drawio ? true : isVoid(element);
    };

    editor.isBlockCard = (element: DrawioElement) => {
        return element.type === WikiPluginTypes.drawio ? true : isBlockCard(element);
    };

    editor.renderElement = (element: DrawioElement) => {
        if (element.type === WikiPluginTypes.drawio) {
            return WikiCommonDrawioComponent;
        }
        return renderElement(element);
    };

    return editor;
};

export const createDrawioPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.drawio,
        withOverrides: withDrawio,
        menuItems: [
            {
                key: WikiPluginTypes.drawio,
                keywords: `drawio,draw.io,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.drawio')}`,
                execute: editor => DrawioEditor.insert(editor, WikiPluginTypes.drawio),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.drawio'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.drawio,
                displayKey: '/drawio'
            }
        ]
    })();
