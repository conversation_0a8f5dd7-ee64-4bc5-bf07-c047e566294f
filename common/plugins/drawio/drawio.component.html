<div class="common-plugin-card-element">
  @if (frameXml) {
    @if (!loadingDone()) {
      <thy-loading class="loading-frame" [thyDone]="loadingDone()"></thy-loading>
    }
    <iframe
      class="drawio-frame"
      #drawioFrame
      [style.opacity]="loadingDone() ? '1' : '0'"
      [src]="drawioUrl"
      width="100%"
      height="100%"
      frameborder="0"
    ></iframe>
  } @else {
    <thy-empty [thyContainer]="elementRef" thySize="lg" [thyMessage]="'wiki.plugin.noGraphics' | translate"></thy-empty>
  }
</div>

<ng-template #toolbar>
  <thy-actions contenteditable="false" thySize="xxs" thePreventDefault>
    @if (page?.permissions | hasPermission: 'page_edit') {
      <a
        href="javascript:;"
        thyAction
        thyActionIcon="edit"
        styxI18nTracking
        [thyTooltip]="'common.edit' | translate"
        thyTooltipPlacement="top"
        (click)="openGraphEditor($event)"
      ></a>
    }
    <a
      href="javascript:;"
      thyAction
      thyActionIcon="arrows-alt"
      styxI18nTracking
      [thyTooltip]="'common.fullScreen' | translate"
      thyTooltipPlacement="top"
      (pointerup)="toggleFullscreen($event)"
    ></a>
    <a
      href="javascript:;"
      thyAction
      thyActionIcon="copy"
      styxI18nTracking
      [thyTooltip]="'common.copy' | translate"
      thyTooltipPlacement="top"
      (click)="copyGraphics($event)"
    ></a>
    @if (page?.permissions | hasPermission: 'page_export') {
      <a
        href="javascript:;"
        thyAction
        thyActionIcon="file-export"
        styxI18nTracking
        [thyTooltip]="'wiki.plugins.diagramBoard.exportImage' | translate"
        thyTooltipPlacement="top"
        (click)="downloadGraphics($event)"
      ></a>
    }
    <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      thyTooltipPlacement="top"
      (click)="deleteGraphics($event)"
    ></a>
  </thy-actions>
</ng-template>
