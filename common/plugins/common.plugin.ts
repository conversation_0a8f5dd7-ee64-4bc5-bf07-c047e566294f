import { GlobalPublicPathPipe, Nested<PERSON>ey, StyxTranslateService } from '@atinc/ngx-styx';
import {
    CONTROL_KEY,
    createPluginFactory,
    DropdownMode,
    ElementKinds,
    FontSizes,
    FontSizeTypes,
    getPluginOptions,
    Indents,
    MarkTypes,
    PluginKeys,
    TableElement,
    THE_MODE_TOKEN,
    TheEditor,
    ThePlugin,
    ThePluginMenuItemType,
    TheQueries,
    ToolbarActionTypes,
    ToolbarItemType
} from '@worktile/theia';
import { Editor } from 'slate';
import { WikiCommonGroupToolbarItemComponent } from '../components/group-toolbar/group-toolbar.component';
import { TheExtensionMode } from '../interface/extension-mode';
import { FullscreenService } from '../services/fullscreen.service';
import { WikiPluginTypes } from '../types/editor.types';
import { WikiCommonMenuItemKey, WikiPluginMenuIcons } from '../types/plugin-menu';
import { CustomPluginKeys } from '../types/plugins.types';
import { GlobalExtraAutoFormatRules } from '../types/toolbar';
import { ClientEventEmitter } from '../util/bridge-for-mobile';
import { verifyAvailableAppByPluginType } from '../util/common';
import { isMobileMode } from '../util/extension-mode';
import { outlineMenu } from './outline/outline.plugin';
import { relationIdeaListMenuItem } from './relation-idea-list/relation-idea-list.plugin';
import { relationIdeaMenuItem } from './relation-idea/relation-idea.plugin';
import { relationItemOverrideByKey } from './relation-item/relation-item';
import { relationObjectiveListMenuItem } from './relation-objective-list/relation-objective-list.plugin';
import { relationObjectiveMenuItem } from './relation-objective/relation-objective.plugin';
import { relationPageTreeMenu } from './relation-page-tree/relation-page-tree.plugin';
import { relationPageMenu } from './relation-page/relation-page.plugin';
import { relationTestCaseListMenuItem } from './relation-test-case-list/relation-test-case-list.plugin';
import { relationTestCaseMenuItem } from './relation-test-case/relation-test-case.plugin';
import { relationTicketListMenuItem } from './relation-ticket-list/relation-ticket-list.plugin';
import { relationTicketMenuItem } from './relation-ticket/relation-ticket.plugin';
import { relationWorkItemListMenu } from './relation-work-item-list/relation-work-item-list.plugin';
import { relationWorkItemMenuItem } from './relation-work-item/relation-work-item.plugin';
import { SaveEditor } from './save/save.editor';
import { isFirstNodeInToggleList } from './toggle-list/queries/is-first-node-in-toggle-list';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';
import { relationReportMenu } from './relation-report/relation-report.plugin';

const OVERRIDE_KEYS = [
    PluginKeys.heading,
    PluginKeys.fontSize,
    PluginKeys.inlineCode,
    PluginKeys.color,
    PluginKeys.align,
    PluginKeys.link,
    PluginKeys.mark
];
// 聚焦文本绘图时，禁用工具栏其他元素
const overrideByKey = {};
OVERRIDE_KEYS.forEach(x => {
    overrideByKey[x] = {
        options: {
            disabledOperateTypes: [WikiPluginTypes.textDiagram, WikiPluginTypes.diagramBoard, WikiPluginTypes.audio, WikiPluginTypes.video]
        }
    };
});

export const createCommonPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.common,
        overrideByKey: {
            ...overrideByKey,
            ...relationItemOverrideByKey,
            [PluginKeys.quickInsert]: {
                options: {
                    disabledPlus: false,
                    allowHotkeyInTypes: [
                        ElementKinds.tableCell,
                        ElementKinds.blockquote,
                        WikiPluginTypes.alert,
                        WikiPluginTypes.layoutColumn,
                        WikiPluginTypes.toggleListItem
                    ]
                }
            },
            [PluginKeys.autoFormat]: {
                options: {
                    autoFormatRules: GlobalExtraAutoFormatRules,
                    disabledOperateTypes: [WikiPluginTypes.textDiagram, WikiPluginTypes.diagramBoard]
                }
            },
            [PluginKeys.table]: {
                options: {
                    showFullscreen: true,
                    freezeColumnHeader: true,
                    freezeRowHeader: true,
                    setFullscreen: (editor: Editor, event: MouseEvent, element: TableElement) => {
                        const fullscreenService = editor.injector.get(FullscreenService);
                        fullscreenService.setFullscreen(editor, event, element);
                    },
                    fullscreenAction: (editor: Editor) => {
                        const fullscreenService = editor.injector.get(FullscreenService);
                        return fullscreenService.fullscreenChange$;
                    },
                    rowSelect: (editor, data) => {
                        const modeConfig = editor.injector.get(THE_MODE_TOKEN);
                        if (isMobileMode(modeConfig.mode as TheExtensionMode)) {
                            ClientEventEmitter.onTableRowSelect(editor, data);
                        }
                    },
                    colSelect: (editor, data) => {
                        const modeConfig = editor.injector.get(THE_MODE_TOKEN);
                        if (isMobileMode(modeConfig.mode as TheExtensionMode)) {
                            ClientEventEmitter.onTableColSelect(editor, data);
                        }
                    },
                    tableSelect: (editor, data) => {
                        const modeConfig = editor.injector.get(THE_MODE_TOKEN);
                        if (isMobileMode(modeConfig.mode as TheExtensionMode)) {
                            ClientEventEmitter.onTableSelect(editor, data);
                        }
                    },
                    deselect: editor => {
                        const modeConfig = editor.injector.get(THE_MODE_TOKEN);
                        if (isMobileMode(modeConfig.mode as TheExtensionMode)) {
                            ClientEventEmitter.onTableDeselect();
                        }
                    }
                }
            },
            [PluginKeys.image]: {
                options: {
                    disablePreview: editor => {
                        const modeConfig = editor.injector.get(THE_MODE_TOKEN);
                        if (isMobileMode(modeConfig.mode as TheExtensionMode)) {
                            return true;
                        }
                        return false;
                    },
                    preview: data => {
                        const { editor, previewImageInfo } = data;
                        const modeConfig = editor.injector.get(THE_MODE_TOKEN);
                        if (isMobileMode(modeConfig.mode as TheExtensionMode)) {
                            ClientEventEmitter.previewImage(previewImageInfo);
                        }
                    },
                    errorImageUrl: (editor: TheEditor) => {
                        const globalPublicPathPipe = editor.injector.get(GlobalPublicPathPipe);
                        return globalPublicPathPipe.transform('/assets/images/editor/image-error.svg');
                    }
                }
            }
        },
        toolbarItems: [
            {
                key: WikiPluginTypes.save,
                icon: 'save',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.saveDraft'),
                shortcutKey: `${CONTROL_KEY}+S`,
                execute: (editor: TheEditor) => SaveEditor.onSave(editor)
            },
            {
                key: MarkTypes.fontSize,
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.fontSize'),
                type: ToolbarItemType.dropdown,
                dropdownItemKey: FontSizes.fontSize15,
                includes: FontSizeTypes
            },
            {
                key: ElementKinds.indent,
                includes: [Indents.indentRight, Indents.indentLeft],
                icon: 'float-left',
                type: ToolbarItemType.dropdown,
                dropdownMode: DropdownMode.icon,
                dropdownItemKey: Indents.indentRight,
                dropdownFixedIcon: true,
                disable: (editor: TheEditor) => {
                    if (editor.selection) {
                        const allowedTypes = getPluginOptions(editor, PluginKeys.indent)?.allowedTypes ?? [];
                        const isFirstInToggleList = isFirstNodeInToggleList(editor);
                        const anchorBlock = TheQueries.anchorBlock(editor);
                        return (anchorBlock && !allowedTypes.includes(anchorBlock?.type)) || isFirstInToggleList;
                    }
                    return false;
                }
            },
            {
                key: WikiPluginTypes.group,
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.insert'),
                iconComponent: WikiCommonGroupToolbarItemComponent,
                includes: [
                    WikiPluginTypes.layout,
                    WikiPluginTypes.date,
                    WikiPluginTypes.label,
                    WikiPluginTypes.alert,
                    ElementKinds.code,
                    WikiPluginTypes.formula,
                    WikiPluginTypes.textDiagram,
                    WikiPluginTypes.diagramBoard,
                    ToolbarActionTypes.split,
                    WikiPluginTypes.relationWorkItem,
                    WikiPluginTypes.relationWorkItemList,
                    WikiPluginTypes.relationPage,
                    WikiPluginTypes.attachmentType,
                    WikiPluginTypes.relationPageTree,
                    WikiPluginTypes.relationTestCase,
                    WikiPluginTypes.relationTicket,
                    WikiPluginTypes.relationObjective
                ]
            }
        ],
        menuItems: [
            {
                key: WikiCommonMenuItemKey.productManage,
                keywords: '',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.productManage'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.productManage,
                children: [
                    relationIdeaMenuItem(translate),
                    relationIdeaListMenuItem(translate),
                    'divider',
                    relationTicketMenuItem(translate),
                    relationTicketListMenuItem(translate)
                ],
                isHidden: (editor: TheEditor) => {
                    return !verifyAvailableAppByPluginType(translate, editor, WikiPluginTypes.relationIdea);
                }
            },
            {
                key: WikiCommonMenuItemKey.projectManage,
                keywords: '',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.projectManage'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.projectManage,
                children: [relationWorkItemMenuItem(translate), relationWorkItemListMenu(translate)],
                isHidden: (editor: TheEditor) => {
                    return !verifyAvailableAppByPluginType(translate, editor, WikiPluginTypes.relationWorkItem);
                }
            },
            {
                key: WikiCommonMenuItemKey.testManage,
                keywords: '',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.testManage'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.testManage,
                children: [relationTestCaseMenuItem(translate), relationTestCaseListMenuItem(translate)],
                isHidden: (editor: TheEditor) => {
                    return !verifyAvailableAppByPluginType(translate, editor, WikiPluginTypes.relationTestCase);
                }
            },
            {
                key: WikiCommonMenuItemKey.pageManage,
                keywords: '',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.wikiManage'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.pageManage,
                displayKey: '',
                children: [relationPageMenu(translate), ...outlineMenu(translate), ...relationPageTreeMenu(translate)]
            },
            {
                key: WikiCommonMenuItemKey.insight,
                keywords: '',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.insight'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.insight,
                displayKey: '',
                children: [relationReportMenu(translate)],
                isHidden: (editor: TheEditor) => {
                    return !verifyAvailableAppByPluginType(translate, editor, WikiPluginTypes.relationReport);
                }
            },
            {
                key: WikiCommonMenuItemKey.collaborativeSpace,
                keywords: '',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.collaborativeSpace'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.collaborativeSpace,
                children: [relationObjectiveMenuItem(translate), relationObjectiveListMenuItem(translate)],
                isHidden: (editor: TheEditor) => {
                    return !verifyAvailableAppByPluginType(translate, editor, WikiPluginTypes.relationObjective);
                }
            }
        ]
    })();

export function editableForCheckItem(customPlugins: ThePlugin[]) {
    customPlugins.forEach(value => {
        if (value.key === CustomPluginKeys.common) {
            value.overrideByKey[PluginKeys.checkItem] = {
                options: {
                    editableWithReadonly: true
                }
            };
        }
    });
}

// 用于历史版本覆写表格冻结行列参数
export const createPageHistoryPlugin = createPluginFactory({
    key: CustomPluginKeys.common,
    overrideByKey: {
        [PluginKeys.table]: {
            options: {
                freezeColumnHeader: false,
                freezeRowHeader: false
            }
        }
    }
});
