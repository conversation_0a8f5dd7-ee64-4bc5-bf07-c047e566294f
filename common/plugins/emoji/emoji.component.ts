import { ChangeDetectionStrategy, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { TheBaseElement, TheEditor } from '@worktile/theia';
import { EmojiElement } from '../../custom-types';

@Component({
    selector: 'wiki-common-emoji [theWikiCommonEmoji]',
    template: `<styx-emoji
        [class.focused]="selection"
        [class.readonly]="readonly"
        [styxCode]="element?.code"
        [styxSize]="18"
    ></styx-emoji>`,
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'wiki-common-emoji',
        '[attr.shortname]': 'element?.code',
        '[attr.aria-label]': 'element?.code'
    },
    standalone: false
})
export class WikiCommonEmojiComponent extends TheBaseElement<EmojiElement, TheEditor> implements OnInit, OnDestroy {
    ngOnInit(): void {
        super.ngOnInit();
    }

    ngOnDestroy(): void {
        super.ngOnDestroy();
    }
}
