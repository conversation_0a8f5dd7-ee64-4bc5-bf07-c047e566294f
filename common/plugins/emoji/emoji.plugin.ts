import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { createPluginFactory, MentionEditor, PluginKeys, TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { WikiPluginTypes } from '../../types/editor.types';
import { WikiCommonEmojiComponent } from './emoji.component';
import { WikiCommonEmojiSuggestionComponent } from './suggestion.component';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const createEmojiPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.emoji,
        overrideByKey: {
            [PluginKeys.mention]: {
                options: {
                    mentions: [
                        {
                            type: WikiPluginTypes.emoji,
                            trigger: ':',
                            suggestion: WikiCommonEmojiSuggestionComponent,
                            render: WikiCommonEmojiComponent
                        }
                    ]
                }
            }
        },
        toolbarItems: [
            {
                key: WikiPluginTypes.emoji,
                icon: 'smile-plus',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.emotion'),
                execute: (editor: TheEditor) => MentionEditor.insertTrigger(editor, WikiPluginTypes.emoji),
                active: (editor: TheEditor) => MentionEditor.isActive(editor, WikiPluginTypes.emoji)
            }
        ],
        menuItems: [
            {
                key: WikiPluginTypes.emoji,
                keywords: `biaoqing,bq,emoji,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.emotion')}`,
                execute: (editor: TheEditor) => MentionEditor.insertTrigger(editor, WikiPluginTypes.emoji),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.emotion'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.emoji,
                displayKey: '/bq'
            }
        ],
        options: {
            isInline: true
        }
    })();
