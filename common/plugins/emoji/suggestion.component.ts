import { Component, ElementRef, OnInit } from '@angular/core';
import { MentionEditor, TheEditor, refocus } from '@worktile/theia';

@Component({
    selector: 'wiki-common-emoji-suggestion',
    template: ` <styx-emoji-picker
        #emojiPicker
        [styxKeyword]="keywords"
        [styxBindKeyEventContainer]="container"
        (styxSelect)="selectMention($event)"
        (styxRandom)="selectMention($event)"
    ></styx-emoji-picker>`,
    host: {
        class: 'wiki-common-emoji-suggestions'
    },
    standalone: false
})
export class WikiCommonEmojiSuggestionComponent implements OnInit {
    editor: TheEditor;

    type: string;

    keywords: string;

    container: HTMLElement | ElementRef | string;

    constructor(public elementRef: ElementRef) {}

    ngOnInit() {
        this.getSearchResult(this.keywords);
        this.container = TheEditor.toDOMNode(this.editor, this.editor);
    }

    getSearchResult(keywords: string) {
        this.keywords = keywords || '';
    }

    selectMention(event: any) {
        if (!this.editor.selection) {
            refocus(this.editor);
        }
        MentionEditor.deleteTriggerText(this.editor);
        MentionEditor.insertMention<{ code: string }>(this.editor, this.type, {
            code: typeof event === 'string' ? event : ''
        });
        MentionEditor.close(this.editor);
    }
}
