@use 'ngx-tethys/styles/variables.scss';

.wiki-common-emoji {
    display: inline;
    text-indent: initial;
    ::selection {
        background: transparent;
    }

    .styx-emoji {
        display: inline;
        cursor: pointer;
        margin: 1px;

        &.focused {
            border-radius: 2px;
            box-shadow: 0 0 0 1px variables.$primary;
            background-color: rgba(variables.$primary, 0.15);
            user-select: text;
        }
        &.readonly {
            cursor: default;
        }
    }
}
