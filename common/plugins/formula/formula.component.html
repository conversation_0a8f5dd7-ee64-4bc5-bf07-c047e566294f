<div
  class="wiki-common-formula-container d-inline-flex flex-wrap"
  [debounce]="50"
  [attr.data-image-mode]="katexError && !errorTeX"
  (cdkObserveContent)="updateEditPopoverPosition()"
>
  @if (!element?.content) {
    <span class="formula-placeholder px-2 bg-lighter text-muted" translate="wiki.plugins.formula.input"></span>
  } @else if (element?.content && katexError && errorTeX) {
    <span class="formula-placeholder-error px-2 bg-lighter text-danger" translate="wiki.plugins.formula.invalid"></span>
  } @else if (katexError && !errorTeX) {
    <img class="formula-image" [src]="imageUrl" />
  }
  <span #formulaCode class="formula" [class.hide]="!element?.content || katexError || errorTeX"></span>
</div>

<ng-template #formulaToolbar>
  <thy-actions thySize="xxs">
    <a
      href="javascript:;"
      thyAction
      thyActionIcon="edit"
      styxI18nTracking
      [thyTooltip]="'common.edit' | translate"
      thyTooltipPlacement="top"
      (mousedown)="preventDefault($event)"
      (click)="openEdit($event)"
    ></a>
    <a
      href="javascript:;"
      thyAction
      thyActionIcon="copy"
      styxI18nTracking
      [thyTooltip]="'common.copy' | translate"
      thyTooltipPlacement="top"
      (click)="onCopy($event)"
    ></a>
    <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      thyTooltipPlacement="top"
      (click)="onDelete($event)"
    ></a>
  </thy-actions>
</ng-template>
