export const FormulaList = [
    {
        key: 'greek',
        icon: 'greek',
        children: [
            {
                key: 'alpha',
                icon: 'alpha',
                value: `\\alpha`
            },
            {
                key: 'beta',
                icon: 'beta',
                value: `\\beta`
            },
            {
                key: 'gamma',
                icon: 'gamma',
                value: `\\gamma`
            },
            {
                key: 'delta',
                icon: 'delta',
                value: `\\delta`
            },
            {
                key: 'varepsilon',
                icon: 'varepsilon',
                value: `\\varepsilon`
            },
            {
                key: 'zeta',
                icon: 'zeta',
                value: `\\zeta`
            },
            {
                key: 'eta',
                icon: 'eta',
                value: `\\eta`
            },
            {
                key: 'theta',
                icon: 'theta',
                value: `\\theta`
            },
            {
                key: 'vartheta',
                icon: 'vartheta',
                value: `\\vartheta`
            },
            {
                key: 'iota',
                icon: 'iota',
                value: `\\iota`
            },
            {
                key: 'kappa',
                icon: 'kappa',
                value: `\\kappa`
            },
            {
                key: 'lambda',
                icon: 'lambda',
                value: `\\lambda`
            },
            {
                key: 'mu',
                icon: 'mu',
                value: `\\mu`
            },
            {
                key: 'nu',
                icon: 'nu',
                value: `\\nu`
            },
            {
                key: 'xi',
                icon: 'xi',
                value: `\\xi`
            },
            {
                key: 'pi',
                icon: 'pi',
                value: `\\pi`
            },
            {
                key: 'varpi',
                icon: 'varpi',
                value: `\\varpi`
            },
            {
                key: 'rho',
                icon: 'rho',
                value: `\\rho`
            },
            {
                key: 'varrho',
                icon: 'varrho',
                value: `\\varrho`
            },
            {
                key: 'sigma',
                icon: 'sigma',
                value: `\\sigma`
            },
            {
                key: 'varsigma',
                icon: 'varsigma',
                value: `\\varsigma`
            },
            {
                key: 'tau',
                icon: 'tau',
                value: `\\tau`
            },
            {
                key: 'vartheta',
                icon: 'vartheta',
                value: `\\vartheta`
            },
            {
                key: 'upsilon',
                icon: 'upsilon',
                value: `\\upsilon`
            },
            {
                key: 'phi',
                icon: 'phi',
                value: `\\phi`
            },
            {
                key: 'varphi',
                icon: 'varphi',
                value: `\\varphi`
            },
            {
                key: 'chi',
                icon: 'chi',
                value: `\\chi`
            },
            {
                key: 'psi',
                icon: 'psi',
                value: `\\psi`
            },
            {
                key: 'omega',
                icon: 'omega',
                value: `\\omega`
            },
            {
                key: 'Gamma',
                icon: 'gamma-l',
                value: `\\Gamma`
            },
            {
                key: 'Delta',
                icon: 'delta-l',
                value: `\\Delta`
            },
            {
                key: 'Theta',
                icon: 'theta-l',
                value: `\\Theta`
            },
            {
                key: 'Lambda',
                icon: 'lambda-l',
                value: `\\Lambda`
            },
            {
                key: 'Xi',
                icon: 'xi-l',
                value: `\\Xi`
            },
            {
                key: 'Pi',
                icon: 'pi-l',
                value: `\\Pi`
            },
            {
                key: 'Sigma',
                icon: 'sigma-l',
                value: `\\Sigma`
            },
            {
                key: 'Upsilon',
                icon: 'upsilon-l',
                value: `\\Upsilon`
            },
            {
                key: 'Phi',
                icon: 'phi-l',
                value: `\\Phi`
            },
            {
                key: 'Psi',
                icon: 'psi-l',
                value: `\\Psi`
            },
            {
                key: 'Omega',
                icon: 'omega-l',
                value: `\\Omega`
            }
        ]
    },
    {
        key: 'symbol',
        icon: 'symbol',
        children: [
            {
                key: 'times',
                icon: 'times',
                value: `\\times`
            },
            {
                key: 'div',
                icon: 'div',
                value: `\\div`
            },
            {
                key: 'cdot',
                icon: 'cdot',
                value: `\\cdot`
            },
            {
                key: 'pm',
                icon: 'pm',
                value: `\\pm`
            },
            {
                key: 'mp',
                icon: 'mp',
                value: `\\mp`
            },
            {
                key: 'ast',
                icon: 'ast',
                value: `\\ast`
            },
            {
                key: 'star',
                icon: 'star',
                value: `\\star`
            },
            {
                key: 'circ',
                icon: 'circ',
                value: `\\circ`
            },
            {
                key: 'bullet',
                icon: 'bullet',
                value: `\\bullet`
            },
            {
                key: 'oplus',
                icon: 'oplus',
                value: `\\oplus`
            },
            {
                key: 'ominus',
                icon: 'ominus',
                value: `\\ominus`
            },
            {
                key: 'oslash',
                icon: 'oslash',
                value: `\\oslash`
            },
            {
                key: 'otimes',
                icon: 'otimes',
                value: `\\otimes`
            },
            {
                key: 'odot',
                icon: 'odot',
                value: `\\odot`
            },
            {
                key: 'dagger',
                icon: 'dagger',
                value: `\\dagger`
            },
            {
                key: 'ddagger',
                icon: 'ddagger',
                value: `\\ddagger`
            },
            {
                key: 'vee',
                icon: 'vee',
                value: `\\vee`
            },
            {
                key: 'wedge',
                icon: 'wedge',
                value: `\\wedge`
            },
            {
                key: 'cap',
                icon: 'cap',
                value: `\\cap`
            },
            {
                key: 'cup',
                icon: 'cup',
                value: `\\cup`
            },
            {
                key: 'aleph',
                icon: 'aleph',
                value: `\\aleph`
            },
            {
                key: 're',
                icon: 're',
                value: `\\Re`
            },
            {
                key: 'im',
                icon: 'im',
                value: `\\Im`
            },
            {
                key: 'top',
                icon: 'top',
                value: `\\top`
            },
            {
                key: 'bot',
                icon: 'bot',
                value: `\\bot`
            },
            {
                key: 'infty',
                icon: 'infty',
                value: `\\infty`
            },
            {
                key: 'partial',
                icon: 'partial',
                value: `\\partial`
            },
            {
                key: 'forall',
                icon: 'forall',
                value: `\\forall`
            },
            {
                key: 'exists',
                icon: 'exists',
                value: `\\exists`
            },
            {
                key: 'neg',
                icon: 'neg',
                value: `\\neg`
            },
            {
                key: 'angle',
                icon: 'angle',
                value: `\\angle`
            },
            {
                key: 'triangle',
                icon: 'triangle',
                value: `\\triangle`
            },
            {
                key: 'diamond',
                icon: 'diamond',
                value: `\\diamond`
            }
        ]
    },
    {
        key: 'comparison',
        icon: 'comparison',
        children: [
            {
                key: 'leq',
                icon: 'leq',
                value: `\\leq`
            },
            {
                key: 'geq',
                icon: 'geq',
                value: `\\geq`
            },
            {
                key: 'prec',
                icon: 'prec',
                value: `\\prec`
            },
            {
                key: 'succ',
                icon: 'succ',
                value: `\\succ`
            },
            {
                key: 'preceq',
                icon: 'preceq',
                value: `\\preceq`
            },
            {
                key: 'succeq',
                icon: 'succeq',
                value: `\\succeq`
            },
            {
                key: 'll',
                icon: 'll',
                value: `\\ll`
            },
            {
                key: 'gg',
                icon: 'gg',
                value: `\\gg`
            },
            {
                key: 'equiv',
                icon: 'equiv',
                value: `\\equiv`
            },
            {
                key: 'sim',
                icon: 'sim',
                value: `\\sim`
            },
            {
                key: 'simeq',
                icon: 'simeq',
                value: `\\simeq`
            },
            {
                key: 'asymp',
                icon: 'asymp',
                value: `\\asymp`
            },
            {
                key: 'approx',
                icon: 'approx',
                value: `\\approx`
            },
            {
                key: 'ne',
                icon: 'ne',
                value: `\\ne`
            },
            {
                key: 'subset',
                icon: 'subset',
                value: `\\subset`
            },
            {
                key: 'supset',
                icon: 'supset',
                value: `\\supset`
            },
            {
                key: 'subseteq',
                icon: 'subseteq',
                value: `\\subseteq`
            },
            {
                key: 'supseteq',
                icon: 'supseteq',
                value: `\\supseteq`
            },
            {
                key: 'in',
                icon: 'in',
                value: `\\in`
            },
            {
                key: 'ni',
                icon: 'ni',
                value: `\\ni`
            },
            {
                key: 'notin',
                icon: 'notin',
                value: `\\notin`
            }
        ]
    },
    {
        key: 'math',
        icon: 'math',
        children: [
            {
                key: 'x_a',
                icon: 'x_a',
                value: `x_{a}`
            },
            {
                key: 'xb',
                icon: 'xb',
                value: `x^{b}`
            },
            {
                key: 'x_ab',
                icon: 'x_ab',
                value: `x_{a}^{b}`
            },
            {
                key: 'barx',
                icon: 'barx',
                value: `\\bar{x}`
            },
            {
                key: 'tildex',
                icon: 'tildex',
                value: `\\tilde{x}`
            },
            {
                key: 'fracab',
                icon: 'fracab',
                value: `\\frac{a}{b}`
            },
            {
                key: 'sqrtx',
                icon: 'sqrtx',
                value: `\\sqrt{x}`
            },
            {
                key: 'sqrtnx',
                icon: 'sqrtnx',
                value: `\\sqrt[n]{x}`
            },
            {
                key: 'bigcap_ab',
                icon: 'bigcap_ab',
                value: `\\bigcap_{a}^{b}`
            },
            {
                key: 'bigcup_ab',
                icon: 'bigcup_ab',
                value: `\\bigcup_{a}^{b}`
            },
            {
                key: 'prod_ab',
                icon: 'prod_ab',
                value: `\\prod_{a}^{b}`
            },
            {
                key: 'croprod_ab',
                icon: 'croprod_ab',
                value: `\\coprod_{a}^{b}`
            },
            {
                key: 'leftxright-s',
                icon: 'leftxright-s',
                value: `\\left( x \\right)`
            },
            {
                key: 'leftxright-m',
                icon: 'leftxright-m',
                value: `\\left[ x \\right]`
            },
            {
                key: 'leftxright-l',
                icon: 'leftxright-l',
                value: `\\left\\{ x \\right\\}`
            },
            {
                key: 'leftxright',
                icon: 'leftxright',
                value: `\\left| x \\right|`
            },
            {
                key: 'int_ab',
                icon: 'int_ab',
                value: `\\int_{a}^{b}`
            },
            {
                key: 'oint_ab',
                icon: 'oint_ab',
                value: `\\oint_{a}^{b}`
            },
            {
                key: 'sum_abx',
                icon: 'sum_abx',
                value: `\\sum_{a}^{b}{x}`
            },
            {
                key: 'lim_arghtarrowbx',
                icon: 'lim_arghtarrowbx',
                value: `\\lim_{a \\rightarrow b}{x}`
            }
        ]
    },
    {
        key: 'arrow',
        icon: 'arrow-list',
        children: [
            {
                key: 'leftarrow',
                icon: 'leftarrow',
                value: `\\leftarrow`
            },
            {
                key: 'rightarrow',
                icon: 'rightarrow',
                value: `\\rightarrow`
            },
            {
                key: 'leftrightarrow',
                icon: 'leftrightarrow',
                value: `\\leftrightarrow`
            },
            {
                key: 'leftarrow-d',
                icon: 'leftarrow-d',
                value: `\\Leftarrow`
            },
            {
                key: 'rightarrow-d',
                icon: 'rightarrow-d',
                value: `\\Rightarrow`
            },
            {
                key: 'leftrightarrow-d',
                icon: 'leftrightarrow-d',
                value: `\\Leftrightarrow`
            },
            {
                key: 'uparrow',
                icon: 'uparrow',
                value: `\\uparrow`
            },
            {
                key: 'downarrow',
                icon: 'downarrow',
                value: `\\downarrow`
            },
            {
                key: 'updownarrow',
                icon: 'updownarrow',
                value: `\\updownarrow`
            },
            {
                key: 'uparrow-d',
                icon: 'uparrow-d',
                value: `\\Uparrow`
            },
            {
                key: 'downarrow-d',
                icon: 'downarrow-d',
                value: `\\Downarrow`
            },
            {
                key: 'updownarrow-d',
                icon: 'updownarrow-d',
                value: `\\Updownarrow`
            }
        ]
    }
];
