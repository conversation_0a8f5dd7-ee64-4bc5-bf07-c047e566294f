import { Overlay } from '@angular/cdk/overlay';
import { HttpClient } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, inject, OnDestroy, OnInit, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { AppRootContext } from '@atinc/ngx-styx';
import { FormulaElement } from '@wiki/common/custom-types';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { WikiEditorOptions, WikiPluginTypes } from '@wiki/common/types/editor.types';
import { isMobileMode } from '@wiki/common/util/extension-mode';
import { getDrawingApi } from '@wiki/common/util/get-drawing-api';
import { copyNode, getMode, TheBaseElement, TheEditor, TheQueries, topLeftPosition, updatePopoverPosition } from '@worktile/theia';
import katex from 'katex';
import { ThyNotifyService } from 'ngx-tethys/notify';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { Subscription } from 'rxjs';
import { BeforeContextChange, SlateElementContext } from 'slate-angular';
import { FormulaEditor } from './formula.editor';

@Component({
    selector: 'wiki-common-formula',
    templateUrl: './formula.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'text-body',
        '[class.focused]': 'isCollapsedAndNonReadonly',
        '[class.readonly]': 'readonly'
    },
    standalone: false
})
export class WikiCommonFormulaComponent
    extends TheBaseElement<any, TheEditor>
    implements OnInit, OnDestroy, BeforeContextChange<SlateElementContext>
{
    errorTeX = false;

    katexError = false;

    imageUrl: SafeResourceUrl;

    toolbarPopoverRef: ThyPopoverRef<any>;

    subscription: Subscription;

    mode: string;

    get isEditOpen() {
        return this.elementRef.nativeElement.querySelector('.formula-edit-origin-active');
    }

    get isToolbarOpen() {
        return this.toolbarPopoverRef && this.toolbarPopoverRef.getOverlayRef() && this.toolbarPopoverRef.getOverlayRef().hasAttached();
    }

    @ViewChild('formulaCode', { read: ViewContainerRef, static: true })
    formulaCode: ViewContainerRef;

    @ViewChild('formulaToolbar', { read: TemplateRef, static: true })
    formulaToolbar: TemplateRef<any>;

    public appRootContext = inject(AppRootContext);
    public viewContainerRef = inject(ViewContainerRef);
    private http = inject(HttpClient);
    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);
    private wikiPluginContext = inject(WikiPluginContext);
    private activatedRoute = inject(ActivatedRoute);
    private thyNotifyService = inject(ThyNotifyService);

    beforeContextChange = (value: SlateElementContext<FormulaElement>) => {
        if (value.element !== this.element) {
            this.contentRender(value.element);
        }
    };

    onContextChange() {
        super.onContextChange();
        if (!this.mode) {
            this.mode = getMode(this.editor);
        }
        if (isMobileMode(this.mode)) {
            return;
        }
        if (this.isCollapsedAndNonReadonly) {
            this.openToolbar();
        } else {
            this.closeToolbar();
        }
    }

    ngOnInit() {
        super.ngOnInit();
    }

    ngOnDestroy() {
        super.ngOnDestroy();
        this.subscription?.unsubscribe();
    }

    preventDefault(event: Event) {
        event.preventDefault();
        event.stopPropagation();
    }

    contentRender(element: FormulaElement) {
        const options = this.editor?.options as WikiEditorOptions;
        const content = element?.content || '';
        if (options?.form === 'history') {
            this.katexError = true;
            this.renderImage(content);
        } else {
            this.renderKaTeX(content);
        }
    }

    renderKaTeX(content: string) {
        try {
            katex.render(content, this.formulaCode.element.nativeElement, {
                throwOnError: true,
                displayMode: true,
                output: 'html'
            });
            this.katexError = false;
            this.errorTeX = false;
        } catch (e) {
            if (e instanceof (katex as any).ParseError) {
                this.katexError = true;
                this.renderImage(content);
            } else {
                throw e;
            }
        }
    }

    getDrawingUrl() {
        let pageId = this.wikiPluginContext.getPageId();
        let spaceId = this.wikiPluginContext.getSpaceId();
        // 外部共享的取路由短 id
        if (/^outside-(space|page)/.test(this.mode)) {
            pageId = this.activatedRoute.snapshot.paramMap.get('pid') || '';
            spaceId = this.activatedRoute.parent.snapshot.paramMap.get('sid') || '';
        }
        return getDrawingApi({
            appRootContext: this.appRootContext,
            mode: this.mode,
            type: WikiPluginTypes.formula,
            spaceId,
            pageId
        });
    }

    renderImage(content: string) {
        const img = new Image();
        const apiUrl = this.getDrawingUrl();
        this.http.post(apiUrl, { code: content }, { responseType: 'text' }).subscribe({
            next: data => {
                img.src = `data:image/svg+xml,${encodeURIComponent(data)}`;
            },
            error: data => {
                img.src = `data:image/svg+xml,${encodeURIComponent(data.error)}`;
            }
        });
        img.onload = () => {
            this.errorTeX = false;
            this.imageUrl = img.src;
            this.cdr.detectChanges();
        };
        img.onerror = () => {
            this.errorTeX = true;
            this.cdr.detectChanges();
        };
    }

    openToolbar() {
        if (!TheQueries.isGlobalCollapsed(this.editor) || this.isToolbarOpen || this.isEditOpen) {
            return;
        }
        this.closeEditPopover();

        const origin = this.elementRef.nativeElement as HTMLElement;
        this.toolbarPopoverRef = this.thyPopover.open(this.formulaToolbar, {
            origin,
            viewContainerRef: this.viewContainerRef,
            minWidth: 0,
            panelClass: 'the-plugin-toolbar-popover',
            hasBackdrop: false,
            insideClosable: true,
            outsideClosable: false,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });

        const overlayRef = this.toolbarPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    closeToolbar() {
        if (this.isToolbarOpen) {
            this.toolbarPopoverRef.close();
        }
    }

    openEdit() {
        if (this.isEditOpen || this.readonly) {
            return;
        }
        FormulaEditor.openEditPopover(this.editor, this.element, this.elementRef.nativeElement);
        this.closeToolbar();
    }

    closeEditPopover() {
        this.thyPopover.getOpenedPopovers().forEach(popOver => {
            if (popOver.id === 'formula-edit-popover') {
                popOver.close();
            }
        });
    }

    onCopy(e: Event) {
        e?.preventDefault();
        copyNode(this.editor, this.element, this.thyNotifyService);
    }

    onDelete(event: Event) {
        event.preventDefault();
        FormulaEditor.removeNode(this.editor, this.element);
    }

    updateEditPopoverPosition() {
        if (this.isEditOpen) {
            this.thyPopover.getPopoverById('formula-edit-popover')?.updatePosition();
        }
    }
}
