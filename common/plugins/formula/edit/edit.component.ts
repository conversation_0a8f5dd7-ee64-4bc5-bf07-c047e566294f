import { Overlay } from '@angular/cdk/overlay';
import { Component, ElementRef, inject, Input, OnInit, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { TheEditor, TheQueries } from '@worktile/theia';
import * as _lodash from 'lodash';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { timer } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { Editor, Node, Transforms } from 'slate';
import { FormulaList } from '../formulaList';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { StyxTranslateService } from '@atinc/ngx-styx';

@Component({
    selector: 'wiki-common-formula-edit, [wiki-common-formula-edit]',
    templateUrl: './edit.component.html',
    standalone: false
})
export class WikiCommonFormulaEditComponent implements OnInit {
    translate = inject(StyxTranslateService);

    @Input() editor: TheEditor;

    @Input() element: Node;

    content = '';

    formulaList = FormulaList;

    currentSymbols = [];

    validatorConfig = {
        validationMessages: {
            content: {
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.plugins.formula.empty')
            }
        }
    };

    saving = false;

    private popoverRef: ThyPopoverRef<any>;

    activeType: string = '';

    get isOpen() {
        return this.popoverRef && this.popoverRef.getOverlayRef() && this.popoverRef.getOverlayRef().hasAttached();
    }

    @ViewChild('textArea')
    textArea: ElementRef<any>;

    constructor(
        private thyPopover: ThyPopover,
        private overlay: Overlay,
        private elementRef: ElementRef,
        private viewContainerRef: ViewContainerRef
    ) {}

    ngOnInit() {}

    preventDefault(event: Event) {
        event?.preventDefault();
        event?.stopPropagation();
    }

    openSymbolList(e: Event, symbols: any, symbolList: TemplateRef<any>) {
        e.preventDefault();
        e.stopPropagation();

        this.textArea.nativeElement.focus();
        this.activeType = symbols.key;
        this.currentSymbols = symbols.children;

        const origin = (e.target as HTMLElement).closest('.thy-action') as HTMLElement;
        this.popoverRef = this.thyPopover.open(symbolList, {
            origin,
            viewContainerRef: this.viewContainerRef,
            width: this.activeType === 'math' ? '398px' : '402px',
            hasBackdrop: false,
            outsideClosable: true,
            autoAdaptive: true,
            placement: 'bottom'
        });
    }

    selectSymbol(event: Event, value: string) {
        this.preventDefault(event);

        const startIndex = this.textArea.nativeElement.selectionStart;
        const endIndex = this.textArea.nativeElement.selectionEnd;

        this.content = this.content.slice(0, startIndex) + value + this.content.slice(endIndex);

        this.contentChange(this.content);

        this.activeType = '';
        this.popoverRef.close();

        setTimeout(() => {
            this.textArea.nativeElement.setSelectionRange(startIndex + value.length, startIndex + value.length);
            this.textArea.nativeElement.focus();
        });
    }

    contentChange = _lodash.debounce((content: string) => {
        const at = TheQueries.findPath(this.editor, this.element);
        Transforms.setNodes(this.editor, { content: content.trim() }, { at });
    }, 300);

    submit() {
        this.saving = true;
        timer(100)
            .pipe(
                finalize(() => {
                    this.saving = false;
                })
            )
            .subscribe(() => {
                this.close();
            });
    }

    close() {
        this.thyPopover.close();

        const path = TheQueries.findPath(this.editor, this.element);
        TheEditor.focus(this.editor);
        Transforms.select(this.editor, Editor.after(this.editor, path));
    }
}
