<thy-layout class="formula-edit">
  <thy-header class="pl-3">
    <ng-template #headerTitle>
      <thy-actions thySize="xxs">
        @for (item of formulaList; track item.key) {
          <a
            thyAction
            class="action-primary py-1"
            href="javascript:;"
            [thyActive]="item.key === activeType && isOpen"
            thyType="primary"
            (mousedown)="preventDefault($event)"
            (click)="openSymbolList($event, item, symbolListTemplate)"
          >
            <thy-icon class="symbol-type-icon mr-1" [thyIconName]="'wiki:' + item.icon"></thy-icon>
            <thy-icon class="link-down-icon font-size-sm text-desc" thyIconName="caret-down"></thy-icon>
          </a>
        }
      </thy-actions>
    </ng-template>
  </thy-header>
  <thy-content class="px-5">
    <form thyForm name="modalForm" [thyFormValidatorConfig]="validatorConfig">
      <thy-form-group class="pt-2" thyRowFill>
        <ng-template #formGroup>
          <textarea
            #textArea
            class="form-control border-0 p-0"
            name="content"
            rows="3"
            styxI18nTracking
            [placeholder]="'wiki.plugins.formula.input' | translate"
            thyAutofocus
            [(ngModel)]="content"
            (ngModelChange)="contentChange($event)"
          ></textarea>
        </ng-template>
      </thy-form-group>
      <thy-form-group-footer class="mb-4" thyAlign="right">
        <button
          thyButton="primary"
          thySize="sm"
          [disabled]="!content"
          [thyLoading]="saving"
          (thyFormSubmit)="submit()"
          translate="common.ok"
        ></button>
      </thy-form-group-footer>
    </form>
  </thy-content>
</thy-layout>

<ng-template #symbolListTemplate>
  <div class="symbols-container m-2" [ngClass]="activeType">
    @for (symbol of currentSymbols; track $index) {
      <a thyAction href="javascript:;" class="justify-content-center" (click)="selectSymbol($event, symbol.value)">
        <thy-icon class="symbol-icon" [thyIconName]="'wiki:' + symbol.icon"></thy-icon>
      </a>
    }
  </div>
</ng-template>
