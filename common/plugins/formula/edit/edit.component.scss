@use 'ngx-tethys/styles/variables.scss';

.formula-edit {
    border-radius: variables.$border-radius;
    overflow: hidden;

    .thy-layout-header,
    .thy-layout-content {
        background: variables.$bg-panel;
    }

    .thy-action:not(.active):not(:hover) {
        .symbol-type-icon {
            color: variables.$gray-700;
        }
    }

    .form-control {
        resize: none;
    }
}

.symbols-container {
    display: flex;
    flex-wrap: wrap;

    .thy-action {
        width: 32px;
        height: 32px;

        &:not(:hover) {
            color: variables.$gray-800;
        }

        .symbol-icon {
            font-size: variables.$font-size-md;
            &.thy-icon-wiki-cdot {
                font-size: 5px;
            }
            &.thy-icon-wiki-ast,
            &.thy-icon-wiki-star,
            &.thy-icon-wiki-circ,
            &.thy-icon-wiki-bullet,
            &.thy-icon-wiki-diamond {
                font-size: variables.$font-size-xs;
            }
        }
    }

    &.greek .symbol-icon {
        font-size: variables.$font-size-sm;
    }

    &.symbol .symbol-icon,
    &.comparison .symbol-icon {
        font-size: variables.$font-size-base;
    }

    &.math {
        .thy-action {
            width: 38px;
            height: 60px;
        }

        .thy-icon-wiki-x_a svg {
            width: 19.6px;
            height: 11.4px;
        }
        .thy-icon-wiki-xb svg,
        .thy-icon-wiki-x_ab svg {
            width: 18.3px;
            height: 20.6px;
        }
        .thy-icon-wiki-barx svg,
        .thy-icon-wiki-tildex svg {
            width: 10.9px;
            height: 18.4px;
        }
        .thy-icon-wiki-fracab svg {
            width: 18.5px;
            height: 34.6px;
        }
        .thy-icon-wiki-sqrtx svg,
        .thy-icon-wiki-sqrtnx svg {
            width: 26.1px;
            height: 19.5px;
        }
        .thy-icon-wiki-bigcap_ab svg,
        .thy-icon-wiki-bigcup_ab svg,
        .thy-icon-wiki-prod_ab svg,
        .thy-icon-wiki-croprod_ab svg {
            width: 19.3px;
            height: 50.2px;
        }
        .thy-icon-wiki-leftxright-s svg,
        .thy-icon-wiki-leftxright-m svg,
        .thy-icon-wiki-leftxright-l svg,
        .thy-icon-wiki-leftxright svg {
            width: 25px;
            height: 20px;
        }
        .thy-icon-wiki-int_ab svg,
        .thy-icon-wiki-oint_ab svg {
            width: 25.7px;
            height: 45.6px;
        }
        .thy-icon-wiki-sum_abx svg {
            width: 25.7px;
            height: 34.2px;
        }
        .thy-icon-wiki-lim_arghtarrowbx svg {
            width: 25.7px;
            height: 17.3px;
        }
    }
}
