import { Overlay } from '@angular/cdk/overlay';
import { ViewContainerRef } from '@angular/core';
import { FormulaElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import {
    Element<PERSON><PERSON><PERSON>,
    TheEditor,
    TheQueries,
    TheTransforms,
    bottomLeftPosition,
    topLeftPosition,
    updatePopoverPosition
} from '@worktile/theia';
import { ThyPopover } from 'ngx-tethys/popover';
import { Editor, Element, Node, Range, Transforms } from 'slate';
import { isFirstNodeInToggleList } from '../toggle-list/queries/is-first-node-in-toggle-list';
import { WikiCommonFormulaEditComponent } from './edit/edit.component';

export const FormulaEditor = {
    insert(editor: Editor) {
        const formulaElement: FormulaElement = {
            type: WikiPluginTypes.formula,
            content: '',
            children: [{ text: '' }]
        };
        let selectText = '';

        Promise.resolve()
            .then(() => {
                if (Range.isCollapsed(editor.selection)) {
                    TheTransforms.insertInlineElement(editor, formulaElement);
                } else {
                    const fragment = Node.fragment(editor, editor.selection)[0];
                    const selectNode = Node.get(fragment, []);
                    selectText = Node.string(selectNode);
                    formulaElement.content = selectText;
                    Transforms.delete(editor);
                    Transforms.insertNodes(editor, formulaElement);
                }
            })
            .then(() => {
                const node = Node.parent(editor, editor.selection.anchor.path);
                const element = TheEditor.toDOMNode(editor, node);
                FormulaEditor.openEditPopover(editor, node as FormulaElement, element);
            });
    },
    openEditPopover(editor: TheEditor, element: FormulaElement, origin: HTMLElement) {
        const container = origin.querySelector('.wiki-common-formula-container');
        const overlay = editor.injector.get(Overlay);
        const thyPopover = editor.injector.get(ThyPopover);
        const viewContainerRef = editor.injector.get(ViewContainerRef);

        const popoverRef = thyPopover.open(WikiCommonFormulaEditComponent, {
            initialState: {
                editor: editor,
                element,
                content: element?.content
            },
            id: 'formula-edit-popover',
            origin: container as HTMLElement,
            viewContainerRef: viewContainerRef,
            width: '600px',
            originActiveClass: 'formula-edit-origin-active',
            hasBackdrop: false,
            insideClosable: false,
            outsideClosable: true,
            manualClosure: true,
            autoAdaptive: true,
            scrollStrategy: overlay.scrollStrategies.reposition()
        });
        const overlayRef = popoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, container as HTMLElement, [bottomLeftPosition, topLeftPosition]);
    },
    removeNode(editor: Editor, element: Element) {
        Transforms.move(editor, { reverse: true });

        const selection = editor.selection;
        const path = TheEditor.findPath(editor, element);

        Transforms.removeNodes(editor, { at: path });
        Transforms.select(editor, selection);
        TheEditor.focus(editor);
    },
    isActive(editor: Editor) {
        const [formula] = Editor.nodes(editor, { match: n => Element.isElement(n) && n.type === WikiPluginTypes.formula });
        return !!formula;
    },
    isDisabled(editor: TheEditor) {
        if (editor.selection) {
            const disableTypes = [
                ElementKinds.code,
                ElementKinds.inlineCode,
                ElementKinds.image,
                WikiPluginTypes.label,
                WikiPluginTypes.date,
                WikiPluginTypes.textDiagram,
                WikiPluginTypes.formula,
                WikiPluginTypes.diagramBoard
            ];
            return (
                isFirstNodeInToggleList(editor, editor.selection.anchor) ||
                TheQueries.isNodeTypeIn(editor, disableTypes, { at: editor.selection })
            );
        }
        return false;
    }
};
