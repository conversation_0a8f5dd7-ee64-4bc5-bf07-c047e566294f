@use 'ngx-tethys/styles/variables.scss';
@use '../../styles/variables.scss' as commonVariables;

@mixin activeStyle {
    background-color: commonVariables.$bg-transparent-primary;
    color: variables.$primary;
    border-radius: variables.$border-radius;
    cursor: pointer;
}

.slate-element-formula {
    display: inline-flex;
    max-width: calc(100% - 2px);
    vertical-align: unset;
    border: 2px solid transparent;

    &.slate-focus-element {
        .wiki-common-formula-container {
            @include activeStyle;
        }
    }
    &.readonly {
        cursor: default;
    }

    &:not(.readonly) .wiki-common-formula-container:hover {
        @include activeStyle;
    }

    .formula-placeholder,
    .formula-placeholder-error {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        line-height: 28px;
        height: auto;
        font-size: variables.$font-size-base;
        font-weight: 400;
        border-radius: variables.$border-radius;
    }
}

.wiki-common-formula-container {
    width: 100%;
    height: auto;
    min-height: 28px;
    vertical-align: middle;

    &.active,
    &.formula-edit-origin-active {
        @include activeStyle;
    }

    .formula {
        display: inline-block;
        font-size: initial;
        overflow-y: hidden;
        overflow-x: auto;

        &.hide {
            display: none;
        }

        .katex-display {
            margin: 2px 8px;
        }
        .katex {
            font-size: 1.15em;
            text-align: initial;
            white-space: normal;
        }
    }

    &[data-image-mode='true'] {
        overflow-y: hidden;
        overflow-x: auto;
    }
}
