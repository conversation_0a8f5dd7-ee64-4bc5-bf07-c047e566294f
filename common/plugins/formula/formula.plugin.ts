import { FormulaElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { ThePluginMenuItemType, createPluginFactory } from '@worktile/theia';
import { Editor, Element } from 'slate';
import { WikiCommonFormulaComponent } from './formula.component';
import { FormulaEditor } from './formula.editor';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withFormula = (editor: Editor) => {
    const { isVoid, isInline, isBlockCard, renderElement } = editor;

    editor.isVoid = (element: FormulaElement) => {
        return element.type === WikiPluginTypes.formula ? true : isVoid(element);
    };

    editor.isInline = (element: FormulaElement) => {
        return element.type === WikiPluginTypes.formula ? true : isInline(element);
    };

    editor.isBlockCard = (element: Element) => {
        return isBlockCard(element);
    };

    editor.renderElement = (element: FormulaElement) => {
        if (element.type === WikiPluginTypes.formula) {
            return WikiCommonFormulaComponent;
        }
        return renderElement(element);
    };

    return editor;
};

export const createFormulaPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.formula,
        withOverrides: withFormula,
        toolbarItems: [
            {
                key: WikiPluginTypes.formula,
                icon: 'sigma',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.formula'),
                execute: editor => FormulaEditor.insert(editor),
                active: editor => FormulaEditor.isActive(editor),
                disable: editor => FormulaEditor.isDisabled(editor)
            }
        ],
        menuItems: [
            {
                key: WikiPluginTypes.formula,
                keywords: `gongshi,gs,equation,formula,公式,latex,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.formula')}`,
                execute: editor => FormulaEditor.insert(editor),
                active: editor => FormulaEditor.isActive(editor),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.formula'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.formula,
                displayKey: '/gs'
            }
        ],
        options: {
            isInline: true
        }
    })();
