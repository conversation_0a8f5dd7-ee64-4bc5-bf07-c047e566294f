@use 'ngx-tethys/styles/variables.scss';
@use 'ngx-tethys/styles/bootstrap/mixins/gradients';

$styx-theme-colors: (
    'info': variables.$info,
    'success': variables.$success,
    'warning': variables.$warning,
    'danger': variables.$danger
);

@mixin thy-alert-variant-wiki($color) {
    border-width: 0;
}

@each $color, $value in $styx-theme-colors {
    .the-alert.thy-alert-#{$color} {
        border-width: 0;

        .thy-alert-icon {
            color: #{$value};
        }
    }

    .wiki-alert-popover-toolbar .thy-popover-container .thy-actions .thy-action.#{$color} {
        &,
        &:hover {
            color: #{$value};
        }
    }
}

.emoji-action {
    width: auto;
    height: auto;
    &.is-active {
        color: variables.$primary !important;
        background: variables.$item-active-bg-color;
    }
}

.alert-action-toolbar {
    .thy-default-picker-active {
        background-color: rgba(variables.$primary, 0.1);
        border-radius: 0.25rem;
        .thy-icon {
            color: variables.$primary;
        }
    }
}

.slate-element-alert {
    display: block;

    .the-alert {
        display: flow-root;
        border: 0;
        margin: 0;
        padding-left: 2.625rem;

        .thy-alert-icon {
            position: absolute;
            top: 0.875rem;
            left: variables.$alert-padding-x;
        }

        &.no-icon {
            padding-left: 18px;
        }
    }
}
