<div
  class="thy-alert thy-alert-{{ element?.alertType }} the-alert"
  [ngClass]="{ 'no-icon': element?.alertType === thyAlertType.none }"
  [ngStyle]="{
    backgroundColor: element | elementAttribute: 'color'
  }"
>
  <div #outletParent></div>
  @if (element?.alertType !== thyAlertType.none) {
    @if (element?.alertType === thyAlertType.emoji) {
      <styx-emoji class="thy-alert-icon emoji-icon" [styxCode]="element.icon" [styxSize]="'sm'"></styx-emoji>
    } @else {
      <thy-icon class="thy-alert-icon" contenteditable="false" [thyIconName]="element | elementAttribute: 'icon'"></thy-icon>
    }
    <ng-template #icon>
      <thy-icon class="thy-alert-icon" contenteditable="false" [thyIconName]="element | elementAttribute: 'icon'"></thy-icon>
    </ng-template>
  }
</div>

<ng-template #toolbar>
  <thy-actions thySize="xxs" class="alert-action-toolbar">
    @for (item of alertTypes(); track $index) {
      <a
        href="javascript:;"
        [ngClass]="[item.alertType]"
        thyAction
        [thyActionIcon]="item.icon"
        [thyActionActive]="activeType === item.alertType"
        [thyTooltip]="item.tip"
        (mousedown)="switchType($event, item)"
      ></a>
    }
    <a
      #emojiPicker
      href="javascript:;"
      class="emoji-action"
      [ngClass]="{ emoji: thyAlertType.emoji, 'is-active': activeType === thyAlertType.emoji }"
      thyAction
      thyActionIcon="smile-plus"
      styxEmojiPicker
      [thyActionActive]="activeType === thyAlertType.emoji"
      styxI18nTracking
      [thyTooltip]="'wiki.document.emoji.add' | translate"
      (styxEmojiSelect)="emojiSelect($event)"
      (styxEmojiRandom)="emojiSelect($event)"
      (mousedown)="clickEmoji($event)"
    ></a>
    <a
      href="javascript:;"
      [ngClass]="thyAlertType.none"
      thyAction
      thyActionIcon="wiki:ban"
      [thyActionActive]="activeType === thyAlertType.none"
      styxI18nTracking
      [thyTooltip]="'wiki.plugins.alert.deleteEmoji' | translate"
      (mousedown)="switchType($event, { alertType: thyAlertType.none, icon: '' })"
    ></a>
    <thy-divider class="mr-2 ml-1 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
    <a
      href="javascript:;"
      thyAction
      styxI18nTracking
      [thyTooltip]="'wiki.plugins.alert.backgroundColor' | translate"
      thyColorPicker
      [(ngModel)]="color"
      (ngModelChange)="setBgColor($event)"
      (thyPanelClose)="closePanel()"
    >
      <thy-icon thyIconName="background-tt" thyIconType="twotone" [thyTwotoneColor]="color"></thy-icon>
    </a>
    <thy-divider class="mr-2 ml-1 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      (mousedown)="deleteAlert($event)"
    ></a>
  </thy-actions>
</ng-template>
