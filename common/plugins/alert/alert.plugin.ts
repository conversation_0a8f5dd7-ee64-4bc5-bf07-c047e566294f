import { Editor, Element, Transforms } from 'slate';
import {
    createEmptyParagraph,
    createPluginFactory,
    TheEditor,
    TheQueries,
    TheTransforms,
    ElementKinds,
    ThePluginMenuItemType
} from '@worktile/theia';
import { WikiPluginTypes } from '../../types/editor.types';
import { WikiCommonAlertComponent } from './alert.component';
import { AlertEditor } from './alert.editor';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { computeOverrideByKey } from '@wiki/common/util/compute-override-by-key';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withAlert = <T extends TheEditor>(editor: T): T => {
    const { renderElement, isContainer, insertBreak, deleteBackward } = editor;

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.alert) {
            return WikiCommonAlertComponent;
        }
        return renderElement(element);
    };

    editor.deleteBackward = unit => {
        const aboveResult = Editor.above<Element>(editor, {
            match: n => Element.isElement(n) && Editor.isBlock(editor, n) && n.type === WikiPluginTypes.alert
        });
        if (!aboveResult) {
            return deleteBackward(unit);
        }
        const hasHandled = TheTransforms.handleContinualDeleteBackward(editor, aboveResult, WikiPluginTypes.alert);
        if (hasHandled) {
            return;
        }
        return deleteBackward(unit);
    };

    editor.insertBreak = () => {
        const lowestBlock = TheQueries.anchorBlock(editor);

        if (!lowestBlock) {
            insertBreak();
            return;
        }

        const hasHandled = TheTransforms.handleContinualInsertBreak(editor, lowestBlock, WikiPluginTypes.alert);
        if (hasHandled) {
            return;
        }
        const voidEntry = Editor.above<Element>(editor, {
            match: n => Element.isElement(n) && Editor.isBlock(editor, n) && Editor.isVoid(editor, n)
        });
        if (voidEntry && voidEntry[0]) {
            Transforms.insertNodes(editor, createEmptyParagraph());
            return;
        }

        return insertBreak();
    };

    editor.isContainer = (element: Element) => {
        return element.type === WikiPluginTypes.alert ? true : isContainer(element);
    };

    return editor;
};

const overrideByKey = computeOverrideByKey([], [WikiPluginTypes.alert]);

export const createAlertPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.alert,
        withOverrides: withAlert,
        overrideByKey,
        toolbarItems: [
            {
                key: WikiPluginTypes.alert,
                icon: 'info-circle',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.alert'),
                execute: (editor: TheEditor) => AlertEditor.toggleAlert(editor)
            }
        ],
        menuItems: [
            {
                key: WikiPluginTypes.alert,
                keywords: `tishikuang,tsk,callout,alert,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.alert')}`,
                execute: editor => AlertEditor.toggleAlert(editor),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.alert'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.alert,
                displayKey: '/tsk'
            }
        ],
        options: {
            allowParentTypes: [ElementKinds.tableCell, WikiPluginTypes.layoutColumn, WikiPluginTypes.toggleListItem]
        }
    })();
