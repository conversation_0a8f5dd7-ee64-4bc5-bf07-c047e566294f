import { Overlay } from '@angular/cdk/overlay';
import { Component, computed, ElementRef, HostBinding, inject, NgZone, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { StyxEmojiPickerDirective, StyxTranslateService } from '@atinc/ngx-styx';
import { isMobileMode } from '@wiki/common/util/extension-mode';
import { getMode, TheBaseElement, TheEditor, TheQueries, topLeftPosition, updatePopoverPosition } from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { Range, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { ThyAlertType } from '../../custom-constants';
import { AlertElement } from '../../custom-types';
import { AlertEditor } from './alert.editor';
import { getAlertTypes } from './entities';

@Component({
    selector: 'wiki-common-alert, [wikiCommonAlert]',
    templateUrl: './alert.component.html',
    standalone: false
})
export class WikiCommonAlertComponent extends TheBaseElement<AlertElement, TheEditor> {
    translate = inject(StyxTranslateService);

    activeType = ThyAlertType.success;

    thyAlertType = ThyAlertType;

    alertTypes = computed(() => getAlertTypes(this.translate));

    color: string;

    isOpenEmojiPicker = false;

    previewSelection: any;

    @ViewChild('outletParent', { read: ElementRef, static: true })
    outletParent: ElementRef;

    @ViewChild('emojiPicker', { read: StyxEmojiPickerDirective, static: false })
    emojiPicker: StyxEmojiPickerDirective;

    getOutletParent = () => {
        return this.outletParent.nativeElement;
    };

    toolbarPopoverRef: ThyPopoverRef<any>;

    get isToolbarOpen() {
        return this.toolbarPopoverRef && this.toolbarPopoverRef.getOverlayRef() && this.toolbarPopoverRef.getOverlayRef().hasAttached();
    }

    get isGlobalCollapsed() {
        return this.selection && Range.isCollapsed(this.editor.selection) && this.isCollapsedAndNonReadonly;
    }

    @HostBinding('style.position') position = `relative`;

    @ViewChild('toolbar', { read: TemplateRef, static: true })
    toolbar: TemplateRef<any>;

    public zone = inject(NgZone);
    public viewContainerRef = inject(ViewContainerRef);
    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);

    onContextChange() {
        super.onContextChange();
        this.activeType = this.element.alertType;
        this.color = this.element.color ?? this.getAlertItem().color;
        if (isMobileMode(getMode(this.editor))) {
            return;
        }
        if (this.isCollapsedAndNonReadonly || this.isOpenEmojiPicker) {
            this.openToolbar();
        } else {
            this.closeToolbar();
        }
        this.emojiPicker?.thyPopoverRef?.afterClosed().subscribe(() => {
            if (!this.isCollapsedAndNonReadonly) {
                this.isOpenEmojiPicker = false;
                this.closeToolbar();
            }
        });
    }

    clickEmoji(event: Event) {
        event.preventDefault();
        this.previewSelection = this.selection;
        this.isOpenEmojiPicker = !this.isOpenEmojiPicker;
    }

    openToolbar() {
        if (!TheQueries.isGlobalCollapsed(this.editor) || this.isToolbarOpen) {
            return;
        }

        const origin = this.elementRef.nativeElement as HTMLElement;
        this.toolbarPopoverRef = this.thyPopover.open(this.toolbar, {
            origin,
            viewContainerRef: this.viewContainerRef,
            minWidth: 0,
            panelClass: ['the-plugin-toolbar-popover', 'wiki-alert-popover-toolbar'],
            manualClosure: true,
            hasBackdrop: false,
            insideClosable: false,
            outsideClosable: false,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });

        const overlayRef = this.toolbarPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    closeToolbar() {
        if (this.isToolbarOpen) {
            this.toolbarPopoverRef.close();
            this.isOpenEmojiPicker = false;
        }
    }

    getAlertItem() {
        return this.alertTypes().find(item => item.alertType === this.element.alertType);
    }

    setAlertElement(alert: Partial<AlertElement>) {
        const icon = alert.icon ?? this.element.icon ?? this.getAlertItem().icon;
        const color = alert.color ?? this.element.color ?? this.getAlertItem().color;
        AlertEditor.setAlertType(this.editor, { alertType: alert.alertType, icon: icon, color: color }, this.element);
    }

    emojiSelect(value: string) {
        this.setAlertElement({ alertType: ThyAlertType.emoji, icon: value, color: this.element.color ?? this.getAlertItem().color });
        this.reSection(true);
    }

    switchType(event: Event, alert: Partial<AlertElement>) {
        event.stopPropagation();
        event.preventDefault();
        this.setAlertElement(alert);
    }

    setBgColor(value: string) {
        this.setAlertElement({ alertType: this.element.alertType, icon: this.element.icon ?? this.getAlertItem().icon, color: value });
        this.previewSelection = this.selection;
    }

    reSection(isEmojiSelect = false) {
        const selection = this.selection ?? this.previewSelection;
        setTimeout(() => {
            Transforms.select(this.editor, selection);
            AngularEditor.focus(this.editor);
            if (isEmojiSelect) {
                this.isOpenEmojiPicker = false;
            }
        }, 0);
    }

    closePanel() {
        this.reSection();
    }

    deleteAlert(event: Event) {
        event.preventDefault();
        AlertEditor.removeAlert(this.editor, this.element);
    }
}
