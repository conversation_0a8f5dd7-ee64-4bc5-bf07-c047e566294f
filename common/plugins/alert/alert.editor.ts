import { TheEditor, TheQueries, TheTransforms, createEmptyParagraph } from '@worktile/theia';
import { Element, Range, Transforms } from 'slate';
import { ThyAlertType } from '../../custom-constants';
import { AlertElement } from '../../custom-types';
import { WikiPluginTypes } from '../../types/editor.types';

export const AlertEditor = {
    toggleAlert(editor: TheEditor) {
        const isActive = TheQueries.isBlockActive(editor, WikiPluginTypes.alert);
        if (!isActive) {
            if (Range.isCollapsed(editor.selection)) {
                const alertElement: any = {
                    type: WikiPluginTypes.alert,
                    alertType: ThyAlertType.success,
                    children: [createEmptyParagraph()]
                };
                TheTransforms.insertElements(editor, alertElement);
                return;
            }
            const element: AlertElement = { type: WikiPluginTypes.alert, alertType: ThyAlertType.success, children: [] };
            Transforms.wrapNodes(editor, element);
        } else {
            Transforms.unwrapNodes(editor, { match: (n: Element) => n.type === WikiPluginTypes.alert });
        }
    },

    setAlertType(editor: TheEditor, payload: Partial<AlertElement>, element?: AlertElement) {
        if (!element) {
            TheTransforms.setNodeByType(editor, payload, WikiPluginTypes.alert);
        } else {
            TheTransforms.setNode(editor, payload, element);
        }
    },

    removeAlert(editor: TheEditor, element?: AlertElement) {
        if (!element) {
            TheTransforms.deleteNodeByType(editor, WikiPluginTypes.alert);
        } else {
            TheTransforms.deleteElement(editor, element);
        }
    }
};
