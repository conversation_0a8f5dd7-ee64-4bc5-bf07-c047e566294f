import { StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { ThyAlertType } from '@wiki/common/custom-constants';

export const getAlertTypes = (translate: StyxTranslateService) => {
    return [
        {
            alertType: ThyAlertType.success,
            icon: 'check-circle-fill',
            tip: translate.instant<I18nSourceDefinitionType>('common.success'),
            color: 'rgba(115, 216 ,151 ,0.2)'
        },
        {
            alertType: ThyAlertType.danger,
            icon: 'close-circle-fill',
            tip: translate.instant<I18nSourceDefinitionType>('wiki.plugins.alert.danger'),
            color: 'rgba(255,117 ,117, 0.2)'
        },
        {
            alertType: ThyAlertType.warning,
            icon: 'waring-fill',
            tip: translate.instant<I18nSourceDefinitionType>('common.warning'),
            color: 'rgba(255, 205, 93 ,0.2)'
        },
        {
            alertType: ThyAlertType.info,
            icon: 'info-circle-fill',
            tip: translate.instant<I18nSourceDefinitionType>('common.tip'),
            color: 'rgba(93, 207 ,255 ,0.2)'
        }
    ];
};
