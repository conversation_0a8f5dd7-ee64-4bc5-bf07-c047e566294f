import { createPluginFactory, TheEditor, ThePluginMenuItem, ThePluginMenuItemType } from '@worktile/theia';
import { WikiPluginTypes } from '../../types/editor.types';
import { WikiPluginMenuIcons } from '../../types/plugin-menu';
import { CustomPluginKeys } from '../../types/plugins.types';
import { Element } from 'slate';
import { CommonRelationListComponent } from '../../components/relation-list/relation-list.component';
import { verifyAvailableAppAndPermission } from '../../util/common';
import { RelationObjectiveListEditor } from './relation-objective-list.editor';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withRelationObjectiveList = <T extends TheEditor>(editor: T): T => {
    const { renderElement, isBlockCard, isVoid } = editor;

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationObjectiveList) {
            return CommonRelationListComponent;
        }
        return renderElement(element);
    };

    editor.isVoid = (element: Element) => {
        return element.type === WikiPluginTypes.relationObjectiveList || isVoid(element);
    };

    editor.isBlockCard = (element: Element) => {
        if (element && element.type === WikiPluginTypes.relationObjectiveList) {
            return true;
        }
        return isBlockCard(element);
    };
    return editor;
};

export const relationObjectiveListMenuItem = (translate: StyxTranslateService): ThePluginMenuItem => {
    return {
        key: WikiPluginTypes.relationObjectiveList,
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationObjectiveList'),
        type: ThePluginMenuItemType.group,
        keywords: `mubiaoliebiao, mblb, objectivelist, targetlist, goallist, ${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationObjectiveList')}`,
        menuIcon: WikiPluginMenuIcons.relationObjectiveList,
        displayKey: '/mblb',
        execute: (editor: TheEditor) =>
            RelationObjectiveListEditor.selectItems(editor, data => RelationObjectiveListEditor.insert(editor, data)),
        isDisabled: (editor: TheEditor) => {
            return !verifyAvailableAppAndPermission(translate, editor, WikiPluginTypes.relationObjectiveList);
        }
    };
};

export const createRelationObjectiveListPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.relationObjectiveList,
        withOverrides: withRelationObjectiveList,
        menuItems: [relationObjectiveListMenuItem(translate)]
    })();
