import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { createPluginFactory, TheEditor, ThePluginMenuItem, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';
import { RelationWorkItemListEditor } from './relation-work-item-list.editor';
import { verifyAvailableAppAndPermission } from '../../util/common';
import { CommonRelationListComponent } from '../../components/relation-list/relation-list.component';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withRelationWorkItemList = <T extends TheEditor>(editor: T): T => {
    const { renderElement, isBlockCard, isVoid } = editor;

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationWorkItemList) {
            return CommonRelationListComponent;
        }
        return renderElement(element);
    };

    editor.isVoid = (element: Element) => {
        return element.type === WikiPluginTypes.relationWorkItemList ? true : isVoid(element);
    };

    editor.isBlockCard = (element: Element) => {
        if (element && element.type === WikiPluginTypes.relationWorkItemList) {
            return true;
        }
        return isBlockCard(element);
    };
    return editor;
};

export const relationWorkItemListMenu = (translate: StyxTranslateService): ThePluginMenuItem => {
    return {
        key: WikiPluginTypes.relationWorkItemList,
        keywords: `gongzuoxiangliebiao,gzxlb,worklist,project,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationWorkItemList')}`,
        execute: (editor: TheEditor) =>
            RelationWorkItemListEditor.selectItems(editor, data => RelationWorkItemListEditor.insert(editor, data)),
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationWorkItemList'),
        type: ThePluginMenuItemType.group,
        menuIcon: WikiPluginMenuIcons.relationWorkItemList,
        displayKey: '/gzxlb',
        isDisabled: (editor: TheEditor) => {
            return !verifyAvailableAppAndPermission(translate, editor, WikiPluginTypes.relationWorkItemList);
        }
    };
};

export const createRelationWorkItemListPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.relationWorkItemList,
        withOverrides: withRelationWorkItemList,
        menuItems: [relationWorkItemListMenu(translate)]
    })();
