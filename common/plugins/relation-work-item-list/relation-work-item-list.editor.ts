import { PageExtensionInfo, PageExtensionReferences, PageExtensionType, WorkItemListExtension } from '@wiki/app/entities/page-extendsion';
import { PageApiService } from '@wiki/app/services';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { THE_MODE_TOKEN, TheEditor, TheTransforms, createEmptyParagraph, idCreator } from '@worktile/theia';
import { of } from 'rxjs';
import { Editor, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { DefaultPropertyColumns } from '../../custom-constants';
import { RelationWorkItemListElement } from '../../custom-types';
import { CommonBroadObjectService } from '../../services/broad-object.service';
import { WikiPluginContext } from '../../services/context/plugin.context';
import { RelationWorkItemContext } from '../../services/context/relation-work-item.context';
import { WikiPluginTypes } from '../../types/editor.types';
import { RelationListEditor, WorkItemInfo } from '../../types/relation-types';

export const RelationWorkItemListEditor: RelationListEditor<WorkItemListExtension> = {
    insert(editor: TheEditor, data: PageExtensionInfo<WorkItemListExtension>) {
        const element: RelationWorkItemListElement = {
            extension_id: data._id,
            type: WikiPluginTypes.relationWorkItemList,
            data: {
                columns: DefaultPropertyColumns[WikiPluginTypes.relationWorkItemList]
            },
            children: [{ text: '' }]
        };
        TheTransforms.insertElements(editor, element);
    },

    selectItems(
        editor: TheEditor,
        handle: (data: PageExtensionInfo<WorkItemListExtension>, references?: PageExtensionReferences) => void,
        elementKey?: string,
        selectedWorkItems?: WorkItemInfo[]
    ) {
        const wikiPluginContext = editor.injector.get(WikiPluginContext) as WikiPluginContext & RelationWorkItemContext;
        const broadObjectService = editor.injector.get(CommonBroadObjectService);
        broadObjectService.openWorkItemSelection(selectedWorkItems, items => {
            const pageService = editor.injector.get(PageApiService);
            const pageId = wikiPluginContext.getPageId();
            const modeConfig = editor.injector.get(THE_MODE_TOKEN);
            const mode = modeConfig.mode as TheExtensionMode;

            pageService
                .savePageExtension(
                    pageId,
                    {
                        key: elementKey || idCreator(),
                        type: PageExtensionType.workItemList,
                        data: { work_item_ids: items.map(x => x._id) }
                    },
                    mode
                )
                .subscribe(result => result && handle(result.value, result.references));
            return of(true);
        });
    },

    setAttribute(editor: Editor, element: RelationWorkItemListElement, value: { [key: string]: unknown }) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.setNodes(editor, value, { at });
    },

    removeNode(editor: Editor, element: RelationWorkItemListElement) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.removeNodes(editor, { at });
        Transforms.insertNodes(editor, createEmptyParagraph(), { at });
        AngularEditor.focus(editor);
    }
};
