import { Component, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { getDrivePreviewUrl } from '@atinc/ngx-styx';
import { AudioElement } from '@wiki/common/custom-types';
import { WIKI_ATTACHMENT_CONTEXT } from '@wiki/common/services/context/attachment-context.token';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { TheQueries } from '@worktile/theia';
import { BeforeContextChange, SlateElementContext } from 'slate-angular';
import { WikiCardEditor } from '../common/card-editor';
import { WikiBaseFileElementComponent } from '../file/base';
import { BaseFileEditor } from '../file/base-file.editor';

@Component({
    selector: 'wiki-common-audio',
    templateUrl: './audio.component.html',
    host: {
        '[class.text-mode]': 'isTextMode',
        '[class.danger-mode]': 'isDanger && isTextMode'
    },
    providers: [
        {
            provide: WIKI_ATTACHMENT_CONTEXT,
            useExisting: WikiPluginContext
        }
    ],
    standalone: false
})
export class WikiCommonAudioComponent extends WikiBaseFileElementComponent implements OnDestroy, BeforeContextChange<SlateElementContext> {
    onContextChange() {
        super.onContextChange();
        if ((TheQueries.isGlobalCollapsed(this.editor) && this.isCollapsedAndNonReadonly) || this.isPlaceholder) {
            this.openToolbar();
        } else {
            this.closeToolbar();
        }
    }

    readonlyPreviewHandle(event: MouseEvent) {
        const previewUrl = getDrivePreviewUrl(this.fileEntity);
        if (this.readonly) {
            event.preventDefault();
            window.open(previewUrl);
        }
    }

    changeAudioMode() {
        if ((this.element as AudioElement).mode === 'card') {
            BaseFileEditor.wrapText(this.editor, this.element);
        } else {
            WikiCardEditor.switchCard(this.editor, this.element as AudioElement);
        }
    }

    downloadAudio() {
        window.open(this.fileSrc);
    }
}
