<div #contentContainer class="common-plugin-card-element content-container">
  @if (isPlaceholder || (!isUploading && !fileEntity)) {
    <div class="common-placeholder-loading">
      <img class="audio-image mx-2" src="{{ '/assets/images/icons/mp3.svg' | globalPublicPath }}" />
    </div>
  } @else {
    @if (isUploading) {
      <styx-file-uploading-items
        thePreventDefault
        [styxScopeId]="scopeId"
        (styxUploadingAttachmentRemove)="deleteFile($event)"
      ></styx-file-uploading-items>
    }
    @if (!isUploading) {
      @if (!isTextMode) {
        <thy-audio-player
          [thySrc]="fileSrc"
          [thyFileName]="fileEntity.title"
          [thyFileSize]="fileEntity.addition.size | fileSizeDisplay"
        ></thy-audio-player>
      } @else {
        <span class="common-inline-text-mode">
          <img src="{{ '/assets/images/icons/mp3.svg' | globalPublicPath }}" />
          <a
            class="text-block-title"
            thyFlexibleText
            [thyTooltipContent]="fileEntity.title"
            (click)="readonlyPreviewHandle($event)"
            href="javascript:;"
            >{{ fileEntity.title }}</a
          >
        </span>
      }
    }
  }
</div>

<ng-template #toolbar>
  <thy-actions thySize="xxs" thePreventDefault>
    @if (!isUploading && !disabled && !isPlaceholder) {
      <a
        href="javascript:;"
        thyAction
        thePreventDefault
        [thyActionIcon]="isTextMode ? 'float-center' : 'inline'"
        styxI18nTracking
        [thyTooltip]="isTextMode ? ('styx.card' | translate) : ('styx.text' | translate)"
        (click)="changeAudioMode()"
      ></a>
      <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
      @if (fileEntity | driveCanPreview) {
        <a
          thyAction
          thyActionIcon="preview"
          href="javascript:;"
          styxI18nTracking
          [thyTooltip]="'styx.preview' | translate"
          thyTooltipPlacement="top"
          [styxFilePreview]="fileEntity"
        ></a>
      }
      <a
        href="javascript:;"
        thyAction
        thyActionIcon="download"
        styxI18nTracking
        [thyTooltip]="'common.download' | translate"
        thyTooltipPlacement="top"
        (click)="downloadAudio($event)"
      ></a>
      <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
    }
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      thyTooltipPlacement="top"
      (click)="deleteFile($event)"
      (mouseenter)="onEnterDelete()"
      (mouseleave)="onLeaveDelete()"
    ></a>
  </thy-actions>
</ng-template>
