import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { createPluginFactory, CustomElementKinds, ElementKinds, getPluginOptions, TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';
import { WikiCommonAudioComponent } from './audio.component';
import { AudioElement } from '@wiki/common/custom-types';
import { BaseFileEditor } from '../file/base-file.editor';
import { AUDIO_PREVIEW_EXT_NAMES, NestedKey, StyxPricingService, StyxTranslateService } from '@atinc/ngx-styx';
import { includesTextSuffixFormat } from '@wiki/common/util/plugin-help';
import { checkRejectFolderAndHtmlElement } from '@wiki/common/util/file';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export interface AudioPluginOptions {
    allowParentTypes?: CustomElementKinds[];
    audioTypes?: string[];
}

export const withAudio = <T extends TheEditor>(editor: T): T => {
    const { isVoid, isInline, insertData, isBlockCard, renderElement } = editor;

    editor.isInline = (element: AudioElement) => {
        return element.type === WikiPluginTypes.audio && !BaseFileEditor.isCard(element) ? true : isInline(element);
    };

    editor.isVoid = (element: AudioElement) => {
        return element.type === WikiPluginTypes.audio ? true : isVoid(element);
    };

    editor.insertData = data => {
        if (data.files.length) {
            const audioFiles: File[] = [];
            // 拖拽上传时，不允许上传mp4和webm格式的音频，优先为视频格式
            const disabledTypes = ['mp4', 'webm'];
            const audioTypes = getPluginOptions<AudioPluginOptions>(editor, CustomPluginKeys.audio)?.audioTypes.filter(
                type => !disabledTypes.includes(type)
            );
            const pricingService = editor.injector.get(StyxPricingService);
            if (checkRejectFolderAndHtmlElement(data)) {
                if (pricingService.isEnabledCheckpoint()) {
                    for (const file of data.files as FileList) {
                        if (includesTextSuffixFormat(audioTypes, file.name)) {
                            audioFiles.push(file);
                        }
                    }
                }
                if (audioFiles && audioFiles.length > 0) {
                    BaseFileEditor.addFile(editor, audioFiles, WikiPluginTypes.audio);
                }
            }
        }

        insertData(data);
    };

    editor.isBlockCard = (element: AudioElement) => {
        if (element.type && element.type === WikiPluginTypes.audio && BaseFileEditor.isCard(element)) {
            return true;
        }
        return isBlockCard(element);
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.audio) {
            return WikiCommonAudioComponent;
        }
        return renderElement(element);
    };

    return editor;
};

export const createAudioPlugin = (translate: StyxTranslateService) =>
    createPluginFactory<AudioPluginOptions>({
        key: CustomPluginKeys.audio,
        withOverrides: withAudio,
        menuItems: [
            {
                key: WikiPluginTypes.audio,
                keywords: `yinpin, yp, audio, 音频, bdyp, bendiyinpin, local audio, ${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.audio')}`,
                execute: (editor: TheEditor) => {
                    const pricingService = editor.injector.get(StyxPricingService);
                    if (pricingService.ensureCheckpointEnabled('embedment')) {
                        BaseFileEditor.openUpload(editor, WikiPluginTypes.audio);
                    }
                },
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.audio'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.audio,
                displayKey: '/yp'
            }
        ],
        options: {
            allowParentTypes: [
                ElementKinds.tableCell,
                ElementKinds.blockquote,
                WikiPluginTypes.alert,
                WikiPluginTypes.layoutColumn,
                WikiPluginTypes.toggleListItem
            ],
            audioTypes: AUDIO_PREVIEW_EXT_NAMES
        }
    })();
