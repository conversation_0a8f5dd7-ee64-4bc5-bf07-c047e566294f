@let hasFullscreen = !isEmbedment && !isAttachment;
@let hasEdit = !page?.is_lock && isAttachmentText && (fileEntity | isOfficeEditable) && fileEntity?.permissions?.update;
@let hasEmbedment = isEmbedment && (page?.permissions | hasPermission: 'page_edit');
@let hasAttachmentText = isAttachmentText && ((page?.permissions | hasPermission: 'page_download_attachment') || isSharedMode);
@let hasDownload = hasEmbedment || hasAttachmentText;
@let hasExport = page && isBoard && page?.permissions | hasPermission: 'page_export';
@let hasExportDrawio = page && isDrawio && page?.permissions | hasPermission: 'page_export';

@if (hasFullscreen || hasEdit || hasDownload) {
  <div class="p-1 thy-popover-container">
    <thy-actions thySize="xxs">
      @if (hasFullscreen) {
        <a href="javascript:;" thyAction styxI18nTracking [thyTooltip]="'common.fullScreen' | translate" (mouseup)="setFullscreen($event)">
          <thy-icon thyIconName="arrows-alt"></thy-icon>
        </a>
      }

      @if (isOpenNewWindow) {
        <a
          href="javascript:;"
          thyAction
          thyActionIcon="publish"
          styxI18nTracking
          [thyTooltip]="'styx.openNew' | translate"
          (mousedown)="openNewWindow($event)"
        ></a>
      }

      @if (hasEdit) {
        <a
          thyAction
          thyActionIcon="edit"
          thySize="sm"
          styxI18nTracking
          [thyTooltip]="'common.edit' | translate"
          thyStopPropagation
          [styxFileEditTrigger]="fileEntity"
        ></a>
      }

      @if (hasDownload) {
        <a
          href="javascript:;"
          thyAction
          thyActionIcon="download"
          styxI18nTracking
          [thyTooltip]="'common.download' | translate"
          thyTooltipPlacement="top"
          (mousedown)="downloadFile()"
        ></a>
      }

      @if (hasExport) {
        <a
          href="javascript:;"
          thyAction
          thyActionIcon="file-export"
          styxI18nTracking
          [thyTooltip]="'wiki.plugins.diagramBoard.exportImage' | translate"
          thyTooltipPlacement="top"
          (mousedown)="downloadBoardImage()"
        ></a>
      }

      @if (hasExportDrawio) {
        <a
          href="javascript:;"
          thyAction
          thyActionIcon="file-export"
          styxI18nTracking
          [thyTooltip]="'wiki.plugins.diagramBoard.exportImage' | translate"
          thyTooltipPlacement="top"
          (mousedown)="downloadDrawioImage($event)"
        ></a>
      }
    </thy-actions>
  </div>
}
