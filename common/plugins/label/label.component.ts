import { Overlay } from '@angular/cdk/overlay';
import { ChangeDetectionStrategy, Component, inject, ViewContainerRef } from '@angular/core';
import { isMobileMode } from '@wiki/common/util/extension-mode';
import { getMode, TheBaseElement, TheEditor } from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { BeforeContextChange, SlateElementContext } from 'slate-angular';
import { LabelElement } from '../../custom-types';
import { WikiCommonLabelEditComponent } from './edit/label-edit.component';
import { LABEL_DEFAULT_COLOR } from './label.info';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-common-label, [wikiCommonLabel]',
    templateUrl: './label.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        '[class.readonly]': 'readonly',
        '(click)': 'handleClick($event)'
    },
    standalone: false
})
export class WikiCommonLabelComponent extends TheBaseElement<LabelElement, TheEditor> implements BeforeContextChange<SlateElementContext> {
    translate = inject(StyxTranslateService);

    labelDefaultText = this.translate.instant<I18nSourceDefinitionType>('wiki.plugins.label.set');

    thyPopoverEditRef: ThyPopoverRef<any>;

    label: string;

    color: string;

    get labelEditOpened(): boolean {
        return this.thyPopoverEditRef && this.thyPopoverEditRef.componentInstance;
    }

    get labelBackgroundColorOpacity() {
        return this.color === LABEL_DEFAULT_COLOR ? 0.15 : 0.2;
    }

    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);
    public viewContainerRef = inject(ViewContainerRef);

    beforeContextChange = (value: SlateElementContext<LabelElement>) => {
        Promise.resolve().then(() => {
            if ((!value.selection && this.labelEditOpened && this.editor && this.editor.selection) || value.readonly) {
                this.closePopover();
            }
        });
        if (value.element?.label !== this.element?.label) {
            this.label = value.element.label;
        }
        if (value.element?.color !== this.element?.color) {
            this.color = value.element.color;
        }
    };

    handleClick(event: MouseEvent) {
        event.preventDefault();
        event.stopPropagation();

        if (this.readonly || isMobileMode(getMode(this.editor))) {
            return;
        }

        this.openLabelEdit();
    }

    openLabelEdit() {
        this.thyPopoverEditRef = this.thyPopover.open(WikiCommonLabelEditComponent, {
            initialState: {
                editor: this.editor,
                element: this.element,
                label: this.element.label,
                color: this.element.color
            },
            origin: this.elementRef.nativeElement,
            backdropClosable: true,
            outsideClosable: true,
            placement: 'bottomLeft',
            hasBackdrop: false,
            minWidth: 0,
            originActiveClass: 'wiki-common-label-focus',
            viewContainerRef: this.viewContainerRef,
            scrollStrategy: this.overlay.scrollStrategies.reposition(),
            panelClass: ['wiki-common-label-edit']
        });
        if (this.thyPopoverEditRef) {
            const componentInstance = this.thyPopoverEditRef.componentInstance;
            componentInstance.labelChange.subscribe((label: string) => {
                this.label = label;
                this.cdr.markForCheck();
            });
        }
    }

    closePopover() {
        if (this.labelEditOpened) {
            this.thyPopoverEditRef.close();
        }
    }
}
