import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { createPluginFactory, TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';
import { WikiElement } from '../../custom-types';
import { WikiPluginTypes } from '../../types/editor.types';
import { WikiCommonLabelComponent } from './label.component';
import { LabelEditor } from './label.editor';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withLabel = (editor: TheEditor) => {
    const { isVoid, isInline, renderElement } = editor;

    editor.isInline = (element: WikiElement) => (element.type === WikiPluginTypes.label ? true : isInline(element));

    editor.isVoid = (element: WikiElement) => (element.type === WikiPluginTypes.label ? true : isVoid(element));

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.label) {
            return WikiCommonLabelComponent;
        }
        return renderElement(element);
    };

    return editor;
};

export const createLabelPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.label,
        withOverrides: withLabel,
        toolbarItems: [
            {
                key: WikiPluginTypes.label,
                icon: 'tag',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugins.label.pluginName'),
                execute: (editor: TheEditor) => LabelEditor.insertLabel(editor)
            }
        ],
        menuItems: [
            {
                key: WikiPluginTypes.label,
                keywords: `biaoqian,bq,tag,label,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugins.label.pluginName')}`,
                execute: (editor: TheEditor) => LabelEditor.insertLabel(editor),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugins.label.pluginName'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.label,
                displayKey: '/bq'
            }
        ],
        options: {
            isInline: true
        }
    })();
