import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import { MixinBase, mixinUnsubscribe } from 'ngx-tethys/core';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { Path, Transforms } from 'slate';
import { TheEditor } from '@worktile/theia';
import { ColorItem, COLOR_NAMES } from '../label.info';
import { LabelElement } from '../../../custom-types';
import { LabelEditor } from '../label.editor';

@Component({
    selector: 'wiki-common-label-edit',
    templateUrl: './label-edit.component.html',
    standalone: false
})
export class WikiCommonLabelEditComponent extends mixinUnsubscribe(MixinBase) implements OnInit, OnDestroy {
    editor: TheEditor;

    element: LabelElement;

    label = '';

    color = COLOR_NAMES[0].color;

    colorNames = COLOR_NAMES;

    @Output() labelChange: EventEmitter<string> = new EventEmitter();

    constructor(
        private thyPopover: ThyPopover,
        public thyPopoverRef: ThyPopoverRef<any>
    ) {
        super();
    }

    ngOnInit() {
        this.thyPopoverRef
            .beforeClosed()
            .pipe(takeUntil(this.ngUnsubscribe$))
            .subscribe(() => {
                if (this.label === '') {
                    const path = TheEditor.findPath(this.editor, this.element);
                    const previousPath = Path.previous(path);
                    Transforms.removeNodes(this.editor, { at: path });
                    TheEditor.focus(this.editor);
                    Transforms.select(this.editor, previousPath);
                    Transforms.collapse(this.editor, { edge: 'end' });
                }
                if (this.label !== '') {
                    this.updateLabelInfo();
                }
            });
    }

    ngOnDestroy() {
        super.ngOnDestroy();
    }

    enter(event: Event) {
        event.preventDefault();
        event.stopPropagation();

        const path = TheEditor.findPath(this.editor, this.element);
        const nextPath = Path.next(path);
        Transforms.select(this.editor, nextPath);
        TheEditor.focus(this.editor);
        Transforms.collapse(this.editor, { edge: 'start' });
        this.close();
    }

    selectColor(item: ColorItem) {
        this.color = item.color;
        LabelEditor.setLabelColor(this.editor, this.color, this.element);
    }

    updateLabel(label: string) {
        this.label = label;
        this.labelChange.emit(this.label);
    }

    updateLabelInfo() {
        LabelEditor.setLabelValue(this.editor, this.label, this.element);
    }

    close() {
        this.thyPopover.close();
    }
}
