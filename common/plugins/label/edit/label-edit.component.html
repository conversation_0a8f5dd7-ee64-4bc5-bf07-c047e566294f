<form class="wiki-common-label-edit" thyForm name="labelForm" #labelForm="thyForm">
  <div class="wiki-common-label-edit-container">
    <div>
      <input
        name="labelName"
        thyInput
        thySize="md"
        [thyAutofocus]="!label"
        styxI18nTracking
        [placeholder]="'wiki.plugins.label.inputName' | translate"
        [ngModel]="label"
        (ngModelChange)="updateLabel($event)"
        (thyEnter)="enter($event)"
      />
    </div>
    <div class="wiki-common-label-color-labels">
      @for (item of colorNames; track $index) {
        <span
          class="color-label color-label-{{ item.name }} color-label-xs"
          [ngClass]="{ selected: item.color === color }"
          [ngStyle]="{ background: item.color }"
          (click)="selectColor(item)"
        >
          <thy-icon class="font-size-sm" thyIconName="check-thick"></thy-icon>
        </span>
      }
    </div>
  </div>
</form>
