import { TheEditor, TheTransforms } from '@worktile/theia';
import { Node, Range, Transforms } from 'slate';
import { HistoryEditor } from 'slate-history';
import { LabelElement, WikiElement } from '../../custom-types';
import { WikiPluginTypes } from '../../types/editor.types';
import { LABEL_DEFAULT_COLOR } from './label.info';

export const LabelEditor = {
    insertLabel(editor: TheEditor) {
        const labelElement: LabelElement = {
            type: WikiPluginTypes.label,
            label: '',
            color: LABEL_DEFAULT_COLOR,
            children: [{ text: '' }]
        };
        let selectText = '';

        Promise.resolve()
            .then(() => {
                if (Range.isCollapsed(editor.selection)) {
                    TheTransforms.insertInlineElement(editor, labelElement);
                } else {
                    const fragment = Node.fragment(editor, editor.selection)[0];
                    const selectNode = Node.get(fragment, []);
                    selectText = Node.string(selectNode);
                    labelElement.label = selectText;
                    Transforms.delete(editor);
                    Transforms.insertNodes(editor, labelElement);
                }
            })
            .then(() => {
                const labelNode = Node.parent(editor, editor.selection.anchor.path);
                const element = TheEditor.toDOMNode(editor, labelNode);
                element.click();
            });
    },

    setLabelColor(editor: TheEditor, color: string, element: LabelElement) {
        Transforms.setNodes(
            editor,
            { color },
            {
                at: TheEditor.findPath(editor, element),
                match: (n: WikiElement) => n.type === WikiPluginTypes.label
            }
        );
    },

    setLabelValue(editor: TheEditor, label: string, element: LabelElement) {
        HistoryEditor.withoutMerging(editor as any, () => {
            Transforms.setNodes(editor, { label } as LabelElement, {
                at: TheEditor.findPath(editor, element),
                match: (n: WikiElement) => n.type === WikiPluginTypes.label
            });
        });
    }
};
