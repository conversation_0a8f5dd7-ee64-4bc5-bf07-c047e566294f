@use 'ngx-tethys/styles/variables.scss';

.wiki-common-label {
    max-width: 180px;
    background: variables.$gray-200;

    &:hover {
        background: rgba(variables.$primary, 0.2);
    }
}

.wiki-common-label-edit {
    .thy-popover-container {
        min-width: 212px;
    }
    .wiki-common-label-edit-container {
        width: 212px;
        padding: 20px;
    }

    .wiki-common-label-color-labels {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-top: 15px;
    }
    .color-label {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 22px;
        height: 22px;
        text-align: center;
        border-radius: 50%;
        cursor: pointer;

        &.selected {
            .thy-icon-check-thick {
                visibility: visible;
            }
            &.color-label-1 {
                .thy-icon-check-thick {
                    color: variables.$gray-400;
                }
            }
        }

        .thy-icon-check-thick {
            margin-top: 1px;
            color: variables.$white;
            visibility: hidden;
        }
        &-1 {
            background: rgba(102, 102, 102, 0.1) !important;
        }
    }
}

.slate-element-table-cell {
    .slate-element-label,
    .wiki-common-label {
        max-width: 100%;
    }
}
