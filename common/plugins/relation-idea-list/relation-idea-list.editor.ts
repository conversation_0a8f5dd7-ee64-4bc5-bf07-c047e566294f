import { ShipBroadObjectTypes, StyxRelationItemInfo } from '@atinc/ngx-styx';
import { IdeaListExtension, PageExtensionInfo, PageExtensionReferences, PageExtensionType } from '@wiki/app/entities/page-extendsion';
import { PageApiService } from '@wiki/app/services';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { CommonBroadObjectService } from '@wiki/common/services/broad-object.service';
import { PageStore } from '@wiki/common/stores/page.store';
import { THE_MODE_TOKEN, TheEditor, TheTransforms, idCreator } from '@worktile/theia';
import { of } from 'rxjs';
import { Editor, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { DefaultPropertyColumns } from '../../custom-constants';
import { RelationIdeaListElement } from '../../custom-types';
import { RelationListEditor } from '../../types';
import { WikiPluginTypes } from '../../types/editor.types';
import { RelationIdeaInfo } from '../relation-idea/type';

export const RelationIdeaListEditor: RelationListEditor<IdeaListExtension> = {
    insert(editor: TheEditor, data: PageExtensionInfo) {
        const element: RelationIdeaListElement = {
            extension_id: data._id,
            type: WikiPluginTypes.relationIdeaList,
            data: {
                columns: DefaultPropertyColumns[WikiPluginTypes.relationIdeaList]
            },
            children: [{ text: '' }]
        };
        TheTransforms.insertElements(editor, element);
    },

    selectItems(
        editor: TheEditor,
        handle: (data: PageExtensionInfo<IdeaListExtension>, references?: PageExtensionReferences) => void,
        elementKey?: string,
        selectedIdeas?: RelationIdeaInfo[]
    ) {
        const pageService = editor.injector.get(PageApiService);
        const pageStore = editor.injector.get(PageStore);
        const broadObjectService = editor.injector.get(CommonBroadObjectService);
        const pageId = pageStore.snapshot.page._id;
        const modeConfig = editor.injector.get(THE_MODE_TOKEN);
        const mode = modeConfig.mode as TheExtensionMode;

        broadObjectService.openShipSelection(
            ShipBroadObjectTypes.idea,
            selectedIdeas,
            (relationItems: StyxRelationItemInfo[]) => {
                pageService
                    .savePageExtension<IdeaListExtension>(
                        pageId,
                        {
                            key: elementKey || idCreator(),
                            type: PageExtensionType.ideaList,
                            data: { idea_ids: relationItems?.map(x => x._id) || [] }
                        },
                        mode
                    )
                    .subscribe(result => result && handle(result.value, result.references));
                return of(true);
            },
            { principalId: pageId }
        );
    },

    setAttribute(editor: Editor, element: RelationIdeaListElement, value: { [key: string]: unknown }) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.setNodes(editor, value, { at });
    }
};
