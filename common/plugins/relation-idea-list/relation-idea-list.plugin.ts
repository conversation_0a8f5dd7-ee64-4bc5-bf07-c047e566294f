import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { createPluginFactory, TheEditor, ThePluginMenuItem, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';
import { verifyAvailableAppAndPermission } from '../../util/common';
import { RelationIdeaListEditor } from './relation-idea-list.editor';
import { CommonRelationListComponent } from '@wiki/common/components/relation-list/relation-list.component';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withRelationIdeaList = <T extends TheEditor>(editor: T): T => {
    const { renderElement, isBlockCard, isVoid } = editor;

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationIdeaList) {
            return CommonRelationListComponent;
        }
        return renderElement(element);
    };

    editor.isVoid = (element: Element) => {
        return element.type === WikiPluginTypes.relationIdeaList ? true : isVoid(element);
    };

    editor.isBlockCard = (element: Element) => {
        if (element && element.type === WikiPluginTypes.relationIdeaList) {
            return true;
        }
        return isBlockCard(element);
    };
    return editor;
};

export const relationIdeaListMenuItem = (translate: StyxTranslateService): ThePluginMenuItem => {
    return {
        key: WikiPluginTypes.relationIdeaList,
        keywords: `chanpinxuqiuliebiao,cpxqlb,productdemandlist,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationIdeaList')}`,
        execute: (editor: TheEditor) => RelationIdeaListEditor.selectItems(editor, data => RelationIdeaListEditor.insert(editor, data)),
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationIdeaList'),
        type: ThePluginMenuItemType.group,
        menuIcon: WikiPluginMenuIcons.relationIdeaList,
        displayKey: '/cpxqlb',
        isDisabled: (editor: TheEditor) => {
            return !verifyAvailableAppAndPermission(translate, editor, WikiPluginTypes.relationIdeaList);
        }
    };
};

export const createRelationIdeaListPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.relationIdeaList,
        withOverrides: withRelationIdeaList,
        menuItems: [relationIdeaListMenuItem(translate)]
    })();
