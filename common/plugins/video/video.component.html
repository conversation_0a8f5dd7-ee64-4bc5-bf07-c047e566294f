<div #contentContainer class="common-plugin-card-element content-container">
  @if (isPlaceholder || (!isUploading && !fileEntity)) {
    <div class="common-placeholder-loading">
      <img class="video-image mx-2" src="{{ '/assets/images/icons/video.svg' | globalPublicPath }}" />
    </div>
  } @else {
    @if (isUploading) {
      <styx-file-uploading-items
        thePreventDefault
        [styxScopeId]="scopeId"
        (styxUploadingAttachmentRemove)="deleteFile($event)"
      ></styx-file-uploading-items>
    }
    @if (!isUploading) {
      <div class="video-content video-card border-radius-covering">
        @if (!isTextMode) {
          @if (!isToolbarOpen && !readonly) {
            <div class="video-card-back-drop w-100" [style.height]="backDropHeight"></div>
          }
          <thy-video-player #videoPlayerRef [thySrc]="fileSrc"></thy-video-player>
          <div class="video-card-title w-100 d-flex align-items-center justify-content-between px-5">
            <span thyFlexibleText [thyTooltipContent]="fileEntity.title">{{ fileEntity.title }}</span
            ><span class="video-card-title-duration font-size-sm text-muted ml-4">{{ duration | duration }}</span>
          </div>
        } @else {
          <span class="common-inline-text-mode">
            <img src="{{ '/assets/images/icons/video.svg' | globalPublicPath }}" />
            <a
              class="text-block-title"
              thyFlexibleText
              [thyTooltipContent]="fileEntity?.title"
              (click)="readonlyPreviewHandle($event)"
              href="javascript:;"
              >{{ fileEntity?.title }}</a
            >
          </span>
        }
      </div>
    }
  }
</div>

<ng-template #toolbar>
  <thy-actions thySize="xxs" thePreventDefault>
    @if (!isUploading && !disabled && !isPlaceholder) {
      <a
        href="javascript:;"
        thyAction
        thePreventDefault
        [thyActionIcon]="isTextMode ? 'float-center' : 'inline'"
        styxI18nTracking
        [thyTooltip]="isTextMode ? ('styx.card' | translate) : ('styx.text' | translate)"
        (click)="changeVideoMode()"
      ></a>
      <thy-divider class="ml-1 mr-2 align-self-center" thyColor="light" [thyVertical]="true"></thy-divider>
      @if (canPreview && fileEntity) {
        <a
          thyAction
          thyActionIcon="preview"
          href="javascript:;"
          styxI18nTracking
          [thyTooltip]="'styx.preview' | translate"
          thyTooltipPlacement="top"
          [styxFilePreview]="fileEntity"
        ></a>
      }
      <a
        href="javascript:;"
        thyAction
        thyActionIcon="download"
        styxI18nTracking
        [thyTooltip]="'common.download' | translate"
        thyTooltipPlacement="top"
        (click)="downloadVideo($event)"
      ></a>
      <thy-divider class="ml-1 mr-2 align-self-center" thyColor="light" [thyVertical]="true"></thy-divider>
    }
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      thyTooltipPlacement="top"
      (click)="deleteFile($event)"
      (mouseenter)="onEnterDelete()"
      (mouseleave)="onLeaveDelete()"
    ></a>
  </thy-actions>
</ng-template>
