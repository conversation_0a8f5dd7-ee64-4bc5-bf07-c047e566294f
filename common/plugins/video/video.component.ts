import { AfterViewInit, Component, ElementRef, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { getDrivePreviewUrl } from '@atinc/ngx-styx';
import { VideoElement } from '@wiki/common/custom-types';
import { WIKI_ATTACHMENT_CONTEXT } from '@wiki/common/services/context/attachment-context.token';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { TheQueries } from '@worktile/theia';
import { BeforeContextChange, SlateElementContext } from 'slate-angular';
import { WikiCardEditor } from '../common/card-editor';
import { WikiBaseFileElementComponent } from '../file/base';
import { BaseFileEditor } from '../file/base-file.editor';

@Component({
    selector: 'wiki-common-video',
    templateUrl: './video.component.html',
    host: {
        '[class.text-mode]': 'isTextMode',
        '[class.danger-mode]': 'isDanger && isTextMode'
    },
    providers: [
        {
            provide: WIKI_ATTACHMENT_CONTEXT,
            useExisting: WikiPluginContext
        }
    ],
    standalone: false
})
export class WikiCommonVideoComponent
    extends WikiBaseFileElementComponent
    implements AfterViewInit, OnDestroy, BeforeContextChange<SlateElementContext>
{
    @ViewChild('videoPlayerRef', { static: false, read: ElementRef })
    videoRef: ElementRef<any>;

    video: HTMLMediaElement;

    protected canPreview: boolean;

    protected get duration(): number {
        return this.video?.duration;
    }

    protected get backDropHeight(): string {
        const height: number = this.video?.clientHeight;
        return height ? `${height - 70}px` : '0px';
    }

    ngAfterViewInit() {
        super.ngAfterViewInit();
        this.video = this.videoRef?.nativeElement.getElementsByTagName('video')[0];
    }

    onContextChange() {
        super.onContextChange();
        if ((TheQueries.isGlobalCollapsed(this.editor) && this.isCollapsedAndNonReadonly) || this.isPlaceholder) {
            this.canPreview = !this.videoRef?.nativeElement.querySelector('.error-tip');
            this.openToolbar();
        } else {
            this.closeToolbar();
        }
    }

    readonlyPreviewHandle(event: MouseEvent) {
        const previewUrl = getDrivePreviewUrl(this.fileEntity);
        if (this.readonly) {
            event.preventDefault();
            window.open(previewUrl);
        }
    }

    changeVideoMode() {
        if ((this.element as VideoElement).mode === 'card') {
            BaseFileEditor.wrapText(this.editor, this.element);
        } else {
            WikiCardEditor.switchCard(this.editor, this.element as VideoElement);
        }
    }

    downloadVideo() {
        window.open(this.fileSrc);
    }

    ngOnDestroy() {
        super.ngOnDestroy();
        this.video = null;
        this.videoRef = null;
    }
}
