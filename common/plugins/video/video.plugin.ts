import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { createPluginFactory, CustomElementKinds, ElementKinds, getPluginOptions, TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';
import { WikiCommonVideoComponent } from './video.component';
import { VideoElement } from '@wiki/common/custom-types';
import { BaseFileEditor } from '../file/base-file.editor';
import { NestedKey, StyxPricingService, StyxTranslateService, VIDEO_PREVIEW_EXT_NAMES } from '@atinc/ngx-styx';
import { includesTextSuffixFormat } from '@wiki/common/util/plugin-help';
import { checkRejectFolderAndHtmlElement } from '@wiki/common/util/file';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export interface VideoPluginOptions {
    allowParentTypes?: CustomElementKinds[];
    videoTypes?: string[];
}

export const withVideo = <T extends TheEditor>(editor: T): T => {
    const { isVoid, isInline, insertData, isBlockCard, renderElement } = editor;

    editor.isInline = (element: VideoElement) => {
        return element.type === WikiPluginTypes.video && !BaseFileEditor.isCard(element) ? true : isInline(element);
    };

    editor.isVoid = (element: Element) => {
        return element.type === WikiPluginTypes.video ? true : isVoid(element);
    };

    editor.insertData = data => {
        if (data.files.length) {
            const videoFiles: File[] = [];
            const videoTypes = getPluginOptions<VideoPluginOptions>(editor, CustomPluginKeys.video)?.videoTypes;
            const pricingService = editor.injector.get(StyxPricingService);
            if (checkRejectFolderAndHtmlElement(data)) {
                if (pricingService.isEnabledCheckpoint()) {
                    for (const file of data.files as FileList) {
                        if (includesTextSuffixFormat(videoTypes, file.name)) {
                            videoFiles.push(file);
                        }
                    }
                }
                if (videoFiles && videoFiles.length > 0) {
                    BaseFileEditor.addFile(editor, videoFiles, WikiPluginTypes.video);
                }
            }
        }
        insertData(data);
    };

    editor.isBlockCard = (element: Element) => {
        if (element && element.type === WikiPluginTypes.video && BaseFileEditor.isCard(element)) {
            return true;
        }
        return isBlockCard(element);
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.video) {
            return WikiCommonVideoComponent;
        }
        return renderElement(element);
    };

    return editor;
};

export const createVideoPlugin = (translate: StyxTranslateService) =>
    createPluginFactory<VideoPluginOptions>({
        key: CustomPluginKeys.video,
        withOverrides: withVideo,
        menuItems: [
            {
                key: WikiPluginTypes.video,
                keywords: `shipin, sp, video, bdsp, bendishipin, local video, ${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.video')},${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.localVideo')}`,
                execute: (editor: TheEditor) => {
                    const pricingService = editor.injector.get(StyxPricingService);
                    if (pricingService.ensureCheckpointEnabled('embedment')) {
                        BaseFileEditor.openUpload(editor, WikiPluginTypes.video);
                    }
                },
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.localVideo'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.video,
                displayKey: '/sp'
            }
        ],
        options: {
            allowParentTypes: [
                ElementKinds.tableCell,
                ElementKinds.blockquote,
                WikiPluginTypes.alert,
                WikiPluginTypes.layoutColumn,
                WikiPluginTypes.toggleListItem
            ],
            videoTypes: VIDEO_PREVIEW_EXT_NAMES
        }
    })();
