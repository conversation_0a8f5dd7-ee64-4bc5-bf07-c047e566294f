@use 'ngx-tethys/styles/variables.scss';
@use '../../styles/variables.scss' as commonVariables;

.slate-element-video:not(.text-mode) {
    .content-container {
        .video-card {
            background: variables.$gray-200;
            .thy-video-player {
                background: variables.$black;
                border-top-right-radius: commonVariables.$border-radius-covering;
                border-top-left-radius: commonVariables.$border-radius-covering;
                video {
                    display: block;
                    max-height: 448px;
                    border-top-right-radius: commonVariables.$border-radius-covering;
                    border-top-left-radius: commonVariables.$border-radius-covering;
                }
            }
            &-back-drop {
                position: absolute;
                z-index: 10;
            }
            &-title {
                height: 40px;
                &-duration {
                    white-space: nowrap;
                }
            }
        }
    }
}
