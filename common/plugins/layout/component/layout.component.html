<ng-template #toolbar>
  <thy-actions thySize="xxs">
    @for (item of layoutTypes; track $index) {
      <a
        href="javascript:;"
        [ngClass]="['thy-layout-nav-' + item.type, activeColumn.type === item.type ? 'thy-layout-nav-active' : '']"
        thyAction
        [thyActionIcon]="item.icon"
        [thyTooltip]="item.tip"
        (mousedown)="switchType($event, item.type, item.flexBasis)"
      ></a>
    }
    <thy-divider class="mr-2 ml-1 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      (mouseenter)="onEnterDelete()"
      (mouseleave)="onLeaveDelete()"
      (mousedown)="removeNode($event)"
    ></a>
  </thy-actions>
</ng-template>
