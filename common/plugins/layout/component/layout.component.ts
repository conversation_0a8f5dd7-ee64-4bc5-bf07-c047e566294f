import { Overlay } from '@angular/cdk/overlay';
import { Component, HostListener, inject, OnD<PERSON>roy, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { isMobileMode } from '@wiki/common/util/extension-mode';
import { getMode, TheBaseElement, TheContextService, TheEditor, TheQueries, topLeftPosition, updatePopoverPosition } from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
import { LayoutElement } from '../../../custom-types';
import { getLayoutTypes } from '../constants';
import { LayoutEditor } from '../layout.editor';
import { WikiLayoutActiveColumn, WikiLayoutType } from '../layout.types';
import { StyxTranslateService } from '@atinc/ngx-styx';

@Component({
    selector: 'wiki-common-layout',
    templateUrl: `layout.component.html`,
    host: {
        '[class.danger-mode]': 'dangerous',
        '[class.active]': 'isActive'
    },
    standalone: false
})
export class WikiCommonLayoutComponent extends TheBaseElement<LayoutElement, TheEditor> implements OnDestroy {
    translate = inject(StyxTranslateService);

    dangerous: boolean;

    isActive: boolean;

    layoutTypes = getLayoutTypes(this.translate);

    activeColumn: WikiLayoutActiveColumn = {
        type: WikiLayoutType.TWO_COLUMNS,
        flexBasis: []
    };

    mousePosition = {
        x: 0,
        y: 0
    };

    private mousedownSubscription: Subscription;

    toolbarPopoverRef: ThyPopoverRef<any>;

    get layoutType() {
        let layoutItem: { type?: WikiLayoutType; icon?: string; tip?: string } = {};
        if (this.element) {
            layoutItem = this.layoutTypes.find(item => item.type === this.element.layoutType);
        }
        return layoutItem;
    }

    get isToolbarOpen() {
        return this.toolbarPopoverRef && this.toolbarPopoverRef.getOverlayRef() && this.toolbarPopoverRef.getOverlayRef().hasAttached();
    }

    @ViewChild('toolbar', { read: TemplateRef, static: true })
    toolbar: TemplateRef<any>;

    @HostListener('mousedown', ['$event'])
    handleMousedown(event) {
        this.mousePosition.x = event.pageX;
        this.mousePosition.y = event.pageY;
    }

    @HostListener('mouseup', ['$event'])
    handleMouseUp(event: MouseEvent) {
        if (this.isLayoutElement(event)) {
            this.isActive = true;
            this.handleWindowMouseDown();
            if (this.readonly || isMobileMode(getMode(this.editor))) {
                return;
            }
            this.openToolbar();
        }
    }

    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);
    public mouseEvent = inject(TheContextService);
    public viewContainerRef = inject(ViewContainerRef);

    onContextChange() {
        super.onContextChange();
        this.transformColumnsWidth();
    }

    openToolbar() {
        if (!TheQueries.isGlobalCollapsed(this.editor) || this.isToolbarOpen || !this.isActive) {
            return;
        }

        const origin = this.elementRef.nativeElement as HTMLElement;
        this.toolbarPopoverRef = this.thyPopover.open(this.toolbar, {
            origin,
            viewContainerRef: this.viewContainerRef,
            minWidth: 0,
            panelClass: 'the-plugin-toolbar-popover',
            manualClosure: true,
            hasBackdrop: false,
            insideClosable: false,
            outsideClosable: false,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });

        const overlayRef = this.toolbarPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    closeToolbar() {
        if (this.isToolbarOpen) {
            this.toolbarPopoverRef.close();
        }
    }

    isLayoutElement(event: MouseEvent) {
        const clickDomClass = (event.target as HTMLElement).classList;
        const { pageX, pageY } = event;

        return (
            this.mousePosition &&
            this.mousePosition.x === pageX &&
            this.mousePosition.y === pageY &&
            !this.readonly &&
            (clickDomClass.contains('slate-element-layout') || clickDomClass.contains('slate-element-layout-column'))
        );
    }

    switchType(event: Event, layoutType: WikiLayoutType, layoutWidth: number[]) {
        event.stopPropagation();
        event.preventDefault();
        LayoutEditor.setLayoutTypeAndWidths(this.editor, layoutType, layoutWidth, this.element);
    }

    transformColumnsWidth() {
        this.layoutTypes.forEach(item => {
            if (item.type === this.element.layoutType) {
                this.activeColumn = {
                    type: item.type,
                    flexBasis: item.flexBasis
                };
            }
        });
        setTimeout(() => {
            const columns = [...this.elementRef.nativeElement.querySelectorAll('.slate-element-layout-column')];
            columns.map((column, index) => {
                column.style.flexBasis = (this.activeColumn.flexBasis[index] as number) * 100 + '%';
            });
        });
    }

    removeNode(event: Event) {
        event.stopPropagation();
        event.preventDefault();
        LayoutEditor.removeLayout(this.editor, this.element);
    }

    handleWindowMouseDown() {
        this.mousedownSubscription = this.mouseEvent.onMouseDown$.pipe(take(1)).subscribe((event: MouseEvent) => {
            if (!this.isLayoutElement(event)) {
                this.isActive = false;
                this.closeToolbar();
            }
        });
    }

    onEnterDelete() {
        this.dangerous = true;
    }

    onLeaveDelete() {
        this.dangerous = false;
    }

    ngOnDestroy() {
        super.ngOnDestroy();
        if (this.mousedownSubscription) {
            this.mousedownSubscription.unsubscribe();
        }
    }
}
