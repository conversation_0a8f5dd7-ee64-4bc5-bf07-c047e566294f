import { Component, ElementRef, HostBinding, ViewChild } from '@angular/core';
import { TheBaseElement } from '@worktile/theia';

@Component({
    selector: 'wiki-common-layout-column',
    templateUrl: './column.component.html',
    standalone: false
})
export class WikiCommonLayoutColumnComponent extends TheBaseElement {
    @HostBinding('class')
    class = 'common-plugin-card-element';

    @ViewChild('outletParent', { read: ElementRef, static: true })
    outletParent: ElementRef;

    getOutletParent = () => {
        return this.outletParent.nativeElement;
    };
}
