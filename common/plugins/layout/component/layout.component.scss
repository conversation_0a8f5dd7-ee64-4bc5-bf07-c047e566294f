@use 'ngx-tethys/styles/variables.scss';
@use '../../../../common/styles/variables.scss' as commonVariables;

$layout-column-padding: 15px;

.slate-element-layout {
    position: relative;
    display: flex;
    min-height: 50px;
    cursor: pointer;

    &[contenteditable='false'] {
        margin-left: $layout-column-padding;
        margin-right: $layout-column-padding;
        word-break: break-word;
        .slate-element-layout-column {
            border-width: 0;
        }
    }
    .slate-element-layout-column {
        min-height: 50px;
        flex: 1 1 0%;
        min-width: 0px;
        border-radius: 3px;
        padding: 11px $layout-column-padding;
        box-sizing: border-box;
        margin-right: 10px;
        margin-bottom: 0px !important;
        &:last-child {
            margin-right: 0;
        }
    }
    .thy-layout-nav-active {
        color: variables.$primary;
        border-radius: 0.25rem;
        background-color: commonVariables.$bg-transparent-primary;
    }
    .wiki-common-layout-column-content {
        cursor: text;
        height: 100%;
    }
}

.the-editor-readonly {
    .slate-element-layout {
        min-height: auto;
        cursor: default;
        .slate-element-layout-column {
            border: none;
            padding: 0;
            min-height: auto;
        }
    }
}

// 复写编辑页样式， hover 选中不高亮
.the-editor:not(.the-editor-readonly) {
    .slate-focus-element {
        .slate-element-layout-column {
            &.common-plugin-card-element {
                border-color: variables.$gray-200;
            }
        }
    }
    .slate-element-layout-column {
        &.common-plugin-card-element {
            &:hover {
                border-color: variables.$gray-200;
            }
        }
    }
    .slate-element-layout.active {
        // layout 聚焦高亮 + 背景
        .slate-element-layout-column.common-plugin-card-element {
            border-color: variables.$primary;
            background-color: commonVariables.$bg-transparent-primary;
        }
    }
}
