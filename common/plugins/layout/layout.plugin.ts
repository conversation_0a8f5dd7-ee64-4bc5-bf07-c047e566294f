import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { computeOverrideByKey } from '@wiki/common/util/compute-override-by-key';
import { PluginKeys, TheEditor, ThePluginMenuItemType, TheQueries, TheTransforms, createPluginFactory } from '@worktile/theia';
import { Editor, Element, Node, NodeEntry, Transforms } from 'slate';
import { ELEMENT_TO_COMPONENT, OriginEvent, createClipboardData, getPlainText, setClipboardData } from 'slate-angular';
import { LayoutElement } from '../../custom-types';
import { WikiCommonLayoutColumnComponent } from './component/column/column.component';
import { WikiCommonLayoutComponent } from './component/layout.component';
import { getLayoutTypes } from './constants';
import { LayoutEditor } from './layout.editor';
import { LayoutPosition } from './layout.positon';
import { WikiLayoutOptions } from './layout.types';
import { isSelectionInLayout } from './queries/is-selection-in-layout';
import { createColumn } from './transforms/creat-column';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withLayout = (editor: TheEditor) => {
    const { isBlockCard, deleteBackward, setFragmentData, normalizeNode, deleteCutData, renderElement, isContainer } = editor;
    editor.isBlockCard = (element: Element) => {
        if (element && element.type === WikiPluginTypes.layout) {
            return true;
        }
        return isBlockCard(element);
    };

    editor.deleteBackward = unit => {
        const opts = new WikiLayoutOptions();
        if (!isSelectionInLayout(opts, editor)) {
            deleteBackward(unit);
            return;
        }

        const { selection } = editor;
        const [, startBlockPath] = Editor.above<Element>(editor, {
            match: n => Element.isElement(n) && Editor.isBlock(editor, n)
        });
        const startPosition = LayoutPosition.create(opts, editor, selection.anchor.path);
        if (
            startBlockPath.length <= startPosition.columnEntry[1].length + 1 &&
            startBlockPath.pop() === 0 &&
            selection.anchor.offset === 0 &&
            Editor.isStart(editor, selection.anchor, startBlockPath) &&
            !Editor.isVoid(editor, TheQueries.anchorBlock(editor))
        ) {
            return;
        }
        deleteBackward(unit);
    };

    editor.setFragmentData = async (data: DataTransfer, originEvent?: OriginEvent) => {
        const opts = new WikiLayoutOptions();
        if (!isSelectionInLayout(opts, editor)) {
            setFragmentData(data, originEvent);
            return;
        }
        const { selection } = editor;
        const element = LayoutPosition.create(opts, editor, selection.anchor.path);
        const layoutComponent = ELEMENT_TO_COMPONENT.get(element.layout) as unknown as WikiCommonLayoutComponent;
        const columns = layoutComponent.children;
        let contents: DocumentFragment;
        let fragment: Node[] = [];
        if (layoutComponent.isActive) {
            for (let i = 0; i < columns.length; i++) {
                const columnPath = [...element.layoutEntry[1], i];
                const columnRange = Editor.range(editor, columnPath);
                const domRange = TheEditor.toDOMRange(editor, columnRange);
                if (!contents) {
                    contents = domRange.cloneContents();
                } else {
                    contents.append(domRange.cloneContents());
                }
            }
            fragment = [
                TheQueries.getBlockAbove(editor, {
                    mode: 'highest'
                })[0]
            ];
        } else {
            const layoutNode = editor.getFragment() as Element[];
            if (layoutNode.length === 1 && layoutNode[0].type === WikiPluginTypes.layout) {
                layoutNode[0].children.forEach(node => {
                    fragment.push(...node.children);
                });
                const columnRange = Editor.range(editor, selection);
                const domRange = TheEditor.toDOMRange(editor, columnRange);
                contents = domRange.cloneContents();
            } else {
                return setFragmentData(data, originEvent);
            }
        }

        const div = document.createElement('div');
        const child = document.createElement('div');
        child.appendChild(contents);
        div.appendChild(child);
        child.setAttribute('hidden', 'true');
        div.setAttribute('hidden', 'true');
        document.body.appendChild(div);

        const attach = div.childNodes[0] as HTMLElement;
        const plainText = getPlainText(child);
        const htmlString = div.innerHTML;
        const clipboardData = createClipboardData(htmlString, fragment as Element[], plainText, []);
        await setClipboardData(clipboardData, div, attach, data);
        document.body.removeChild(div);
        return;
    };

    editor.normalizeNode = ([layout, currentPath]: NodeEntry<LayoutElement>) => {
        if (layout.type === WikiPluginTypes.layout) {
            const translate = editor.injector.get(StyxTranslateService);
            const layoutProperty = getLayoutTypes(translate).find(item => item.type === layout.layoutType);
            const invalidCount = layoutProperty && layoutProperty.flexBasis.length - layout.children.length;
            if (invalidCount > 0) {
                const opts = new WikiLayoutOptions();
                Editor.withoutNormalizing(editor, () => {
                    for (let i = 0; i < invalidCount; i++) {
                        const newColumn = createColumn(opts);
                        Transforms.insertNodes(editor, [newColumn], {
                            at: [currentPath[0], layout.children.length]
                        });
                    }
                });
                return;
            }
        }
        normalizeNode([layout, currentPath]);
    };

    editor.deleteCutData = () => {
        const opts = new WikiLayoutOptions();
        if (!isSelectionInLayout(opts, editor)) {
            deleteCutData();
            return;
        }
        const { selection } = editor;
        const element = LayoutPosition.create(opts, editor, selection.anchor.path);
        const layoutComponent = ELEMENT_TO_COMPONENT.get(element.layout) as unknown as WikiCommonLayoutComponent;
        const isActive = layoutComponent.isActive;
        if (isActive) {
            TheTransforms.deleteElement(editor, layoutComponent.element);
            return;
        }
        deleteCutData();
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.layout) {
            return WikiCommonLayoutComponent;
        }
        if (element.type === WikiPluginTypes.layoutColumn) {
            return WikiCommonLayoutColumnComponent;
        }

        return renderElement(element);
    };

    editor.isContainer = (element: Element) => {
        return element.type === WikiPluginTypes.layoutColumn ? true : isContainer(element);
    };

    return editor;
};

const pluginKeys = [PluginKeys.code, PluginKeys.table, PluginKeys.hr];
const overrideByKey = computeOverrideByKey(pluginKeys, [WikiPluginTypes.layoutColumn]);

export const createLayoutPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.layout,
        withOverrides: withLayout,
        overrideByKey,
        nestedStructureByKey: {
            [WikiPluginTypes.layout]: WikiPluginTypes.layoutColumn
        },
        toolbarItems: [
            {
                key: WikiPluginTypes.layout,
                icon: 'two-columns',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.layout'),
                execute: (editor: TheEditor) => LayoutEditor.toggleLayout(editor)
            }
        ],
        menuItems: [
            {
                key: WikiPluginTypes.layout,
                keywords: `buju,bj,columns,layout,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.layout')}`,
                execute: (editor: TheEditor) => LayoutEditor.toggleLayout(editor),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.layout'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.layout,
                displayKey: '/bj'
            }
        ],
        options: {
            allowParentTypes: [WikiPluginTypes.toggleListItem]
        }
    })();
