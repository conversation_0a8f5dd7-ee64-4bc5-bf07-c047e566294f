import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { ElementKinds } from '@worktile/theia';

export enum WikiLayoutType {
    TWO_COLUMNS = 'two_columns',
    THREE_COLUMNS = 'three_columns',
    RIGHT_SIDEBAR = 'right_sidebar',
    LEFT_SIDEBAR = 'left_sidebar',
    BETWEEN_SIDEBAR = 'between_sidebar'
}

export interface WikiLayoutActiveColumn {
    type: WikiLayoutType;
    flexBasis: number[];
}

export interface LayoutOptionsFormat {
    typeLayout?: WikiPluginTypes;
    typeColumn?: WikiPluginTypes;
    exitBlockType?: string;
    columnNumber?: number;
}

export class WikiLayoutOptions {
    layout: LayoutOptionsFormat = {
        typeLayout: WikiPluginTypes.layout,
        typeColumn: WikiPluginTypes.layoutColumn,
        exitBlockType: ElementKinds.paragraph,
        columnNumber: 2
    };
    constructor(options?: LayoutOptionsFormat) {
        if (!options) {
            return;
        }
        options = { ...this.layout, ...options };
        const { typeLayout, typeColumn, columnNumber } = options;
        this.layout.typeLayout = typeLayout;
        this.layout.typeColumn = typeColumn;
        this.layout.columnNumber = columnNumber;
    }
}
