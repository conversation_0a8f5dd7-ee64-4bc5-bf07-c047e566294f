import { Text, Element } from 'slate';
import { WikiLayoutOptions } from '../layout.types';
import { LayoutColumnElement } from '../../../custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { ElementKinds } from '@worktile/theia';

/**
 * Create a new column
 */
export function createColumn(opts: WikiLayoutOptions, nodes?: Text[]): LayoutColumnElement {
    return {
        type: WikiPluginTypes.layoutColumn,
        children: nodes || [createEmptyContent(opts)]
    };
}

/**
 * Create a new default content block
 */
export function createEmptyContent(opts: WikiLayoutOptions): Element {
    return {
        type: ElementKinds.default,
        children: [{ text: '' }]
    };
}
