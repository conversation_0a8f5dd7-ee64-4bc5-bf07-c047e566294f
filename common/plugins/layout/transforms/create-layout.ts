import { Text } from 'slate';
import { createColumn } from './creat-column';
import { LayoutElement } from '../../../custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiLayoutOptions, WikiLayoutType } from '../layout.types';

/**
 * Create a layout
 */
export function createLayout(
    opts: WikiLayoutOptions,
    layoutType: WikiLayoutType,
    getColumnContent?: (columnNumber: number) => Text[]
): LayoutElement {
    const columnsNodes = new Array(opts.layout.columnNumber)
        .fill(1)
        .map(i => createColumn(opts, getColumnContent ? getColumnContent(i) : undefined));
    return {
        type: WikiPluginTypes.layout,
        layoutType,
        children: columnsNodes
    };
}
