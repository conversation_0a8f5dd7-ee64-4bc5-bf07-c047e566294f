import { Editor, Transforms, Path, Node, Element, NodeEntry } from 'slate';
import { createColumn } from './creat-column';
import { WikiLayoutType, WikiLayoutOptions } from '../layout.types';
import { LayoutElement } from '../../../custom-types';
import { ElementKinds, TheEditor } from '@worktile/theia';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { LayoutPosition } from '../layout.positon';

export function updateLayout(editor: TheEditor, opts: WikiLayoutOptions, element: LayoutElement, type?: WikiLayoutType) {
    const path = TheEditor.findPath(editor, element.children[0] as Node);
    const position = LayoutPosition.create(opts, editor, path);
    const { layout } = position;
    if (layout.layoutType === type) {
        return;
    }
    const addCount = opts.layout.columnNumber - layout.children.length;
    switch (addCount) {
        case 1:
            return insertLayoutColumn(opts, editor, type, layout);
        case -1:
            return removeLayoutColumn(opts, editor, type, layout);
        default:
            return switchLayoutColumn(opts, editor, type, layout);
    }
}

function switchLayoutColumn(opts: WikiLayoutOptions, editor: TheEditor, type?: WikiLayoutType, layout?: LayoutElement) {
    Transforms.setNodes(editor, { layoutType: type } as LayoutElement, { at: TheEditor.findPath(editor, layout) });
}

function insertLayoutColumn(opts: WikiLayoutOptions, editor: TheEditor, type?: WikiLayoutType, layout?: LayoutElement) {
    Editor.withoutNormalizing(editor, () => {
        const newColumn = createColumn(opts);
        const endPath = TheEditor.findPath(editor, layout.children[1]);
        Transforms.insertNodes(editor, [newColumn], {
            at: Path.next(endPath)
        });
        Transforms.setNodes(editor, { layoutType: type } as LayoutElement, { at: TheEditor.findPath(editor, layout) });
    });
}

function removeLayoutColumn(opts: WikiLayoutOptions, editor: TheEditor, type?: WikiLayoutType, layout?: LayoutElement) {
    Editor.withoutNormalizing(editor, () => {
        const endPath = TheEditor.findPath(editor, layout.children[layout.children.length - 1]);
        const { path } = Editor.end(editor, Path.previous(endPath));
        const previousEnd = Path.next(path.slice(0, 3));
        const endRange = Editor.range(editor, Editor.start(editor, endPath), Editor.end(editor, endPath));
        Transforms.moveNodes(editor, {
            at: endRange,
            to: previousEnd,
            match: (node: Element) => {
                if (node.type) {
                    const parentNode = Editor.parent(editor, TheEditor.findPath(editor, node)) as NodeEntry<Element>;
                    const isSingleEmptyParagraph =
                        parentNode[0].children.length === 1 &&
                        Editor.isEmpty(editor, node as Element) &&
                        node.type === ElementKinds.default;
                    return parentNode[0].type === WikiPluginTypes.layoutColumn && !isSingleEmptyParagraph;
                }
            }
        });
        Transforms.removeNodes(editor, { at: endPath });
        Transforms.setNodes(editor, { layoutType: type } as LayoutElement, { at: TheEditor.findPath(editor, layout) });
    });
}
