import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiLayoutType } from './layout.types';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { StyxTranslateService } from '@atinc/ngx-styx';

export const getLayoutTypes = (translate?: StyxTranslateService) => {
    return [
        {
            type: WikiLayoutType.TWO_COLUMNS,
            icon: 'two-columns',
            tip: translate?.instant<I18nSourceDefinitionType>('wiki.plugins.layout.twoColumns'),
            flexBasis: [0.5, 0.5]
        },
        {
            type: WikiLayoutType.THREE_COLUMNS,
            icon: 'three-columns',
            tip: translate?.instant<I18nSourceDefinitionType>('wiki.plugins.layout.threeColumns'),
            flexBasis: [0.33333, 0.33333, 0.33333]
        },
        {
            type: WikiLayoutType.LEFT_SIDEBAR,
            icon: 'left-sidebar',
            tip: translate?.instant<I18nSourceDefinitionType>('wiki.plugins.layout.leftSidebar'),
            flexBasis: [0.25, 0.75]
        },
        {
            type: WikiLayoutType.RIGHT_SIDEBAR,
            icon: 'right-sidebar',
            tip: translate?.instant<I18nSourceDefinitionType>('wiki.plugins.layout.rightSidebar'),
            flexBasis: [0.75, 0.25]
        },
        {
            type: WikiLayoutType.BETWEEN_SIDEBAR,
            icon: 'holy-grail',
            tip: translate?.instant<I18nSourceDefinitionType>('wiki.plugins.layout.bothSidebars'),
            flexBasis: [0.25, 0.5, 0.25]
        }
    ];
};

export const LAYOUT_TYPES = [WikiPluginTypes.layout];
