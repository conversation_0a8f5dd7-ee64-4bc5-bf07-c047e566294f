import { LayoutElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types';
import { TheEditor, TheQueries, TheTransforms } from '@worktile/theia';
import { WikiLayoutOptions, WikiLayoutType } from './layout.types';
import { createLayout } from './transforms/create-layout';
import { updateLayout } from './transforms/update-layout';

export const LayoutEditor = {
    toggleLayout(editor: TheEditor) {
        const opts = new WikiLayoutOptions();
        const layoutElement = createLayout(opts, WikiLayoutType.TWO_COLUMNS);
        TheTransforms.insertElements(editor, layoutElement, false);
    },

    setLayoutTypeAndWidths(editor: TheEditor, layoutType: WikiLayoutType, layoutWidths: number[], element?: LayoutElement) {
        let layoutElement = element;
        if (!layoutElement!) {
            const layoutNode = TheQueries.getAboveByType(editor, WikiPluginTypes.layout);
            layoutElement = layoutNode && (layoutNode[0] as LayoutElement);
        }
        const opts = new WikiLayoutOptions({ columnNumber: layoutWidths.length });
        updateLayout(editor, opts, layoutElement, layoutType);
    },

    removeLayout(editor: TheEditor, element?: LayoutElement) {
        if (!element) {
            TheTransforms.deleteNodeByType(editor, WikiPluginTypes.layout);
        } else {
            TheTransforms.deleteElement(editor, element);
        }
    }
};
