import { TheEditor } from '@worktile/theia';
import { LayoutPosition } from '../layout.positon';
import { WikiLayoutOptions } from '../layout.types';

export function isSelectionInLayout(opts: WikiLayoutOptions, editor: TheEditor) {
    if (!editor.selection) {
        return false;
    }

    const { anchor, focus } = editor.selection;
    const startPosition = LayoutPosition.create(opts, editor, anchor.path);
    const endPosition = LayoutPosition.create(opts, editor, focus.path);

    // Only handle events in layout
    if (!startPosition.isInLayout() || !endPosition.isInLayout()) {
        return false;
    }

    // Inside the same layout
    return startPosition.layout === endPosition.layout;
}
