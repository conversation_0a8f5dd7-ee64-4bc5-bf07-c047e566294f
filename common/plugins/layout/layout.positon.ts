import { LayoutElement, LayoutColumnElement } from '@wiki/common/custom-types';
import { Path, NodeEntry, Node, Element } from 'slate';
import { WikiLayoutOptions } from './layout.types';

export class LayoutPosition {
    layoutEntry?: NodeEntry<LayoutElement>;
    columnEntry?: NodeEntry<LayoutColumnElement>;

    constructor(
        options = {
            layoutEntry: null,
            columnEntry: null
        }
    ) {
        const { layoutEntry, columnEntry } = options;
        this.layoutEntry = layoutEntry;
        this.columnEntry = columnEntry;
    }

    static create(opts: WikiLayoutOptions, containerNode: Node, path: Path): LayoutPosition {
        const ancestors = Array.from(Node.ancestors(containerNode, path)) as NodeEntry<Element>[];
        const layoutEntry = ancestors.find(([p]) => p.type === opts.layout.typeLayout) || [];
        const columnEntry = ancestors.find(([p]) => p.type === opts.layout.typeColumn) || [];

        return new LayoutPosition({
            layoutEntry,
            columnEntry
        });
    }

    get layout(): LayoutElement {
        if (!this.layoutEntry) {
            throw new Error('Not in a layout');
        }
        return this.layoutEntry[0];
    }

    get column(): LayoutColumnElement {
        if (!this.columnEntry) {
            throw new Error('Not in a layout');
        }
        return this.columnEntry[0];
    }

    isInLayout() {
        return this.layoutEntry.length > 0;
    }
}
