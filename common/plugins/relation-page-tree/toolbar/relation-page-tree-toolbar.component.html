<thy-actions class="relation-page-tree-toolbar" contenteditable="false" thySize="xxs">
  <a
    thyAction
    thyActionIcon="edit"
    thyActiveClass="active"
    styxI18nTracking
    [thyTooltip]="'common.edit' | translate"
    href="javascript:;"
    (click)="openPageSelection()"
  >
  </a>
  <thy-divider class="ml-1 mr-2 align-self-center" thyColor="light" [thyVertical]="true"></thy-divider>
  <a
    #levelTpl
    thyAction
    thyActiveClass="active"
    styxI18nTracking
    [thyTooltip]="'wiki.space.settings.directory.levelTooltip' | translate"
    href="javascript:;"
    (click)="openLevelMenus($event)"
  >
    {{ 'wiki.space.settings.directory.level' | translate }}
    <thy-icon thyIconName="caret-down" class="font-size-sm ml-1 text-desc"></thy-icon>
  </a>
  <thy-divider class="ml-1 mr-2 align-self-center" thyColor="light" [thyVertical]="true"></thy-divider>
  <a
    href="javascript:;"
    thyAction
    thyType="danger"
    thyActionIcon="trash"
    styxI18nTracking
    [thyTooltip]="'common.delete' | translate"
    thyTooltipPlacement="top"
    (mousedown)="removeNode($event)"
  ></a>
</thy-actions>

<ng-template #dropdownMenuTpl>
  <div class="thy-dropdown-menu">
    @for (level of levelMenus(); track $index) {
      <a
        thyDropdownMenuItem
        href="javascript:;"
        (mousedown)="preventDefault($event)"
        (click)="_changeLevel(level.value)"
        [thyDropdownMenuItemActive]="level.value === getLevel()"
      >
        <span thyDropdownMenuItemName>{{ level.name }}</span>
      </a>
    }
  </div>
</ng-template>
