import { Overlay } from '@angular/cdk/overlay';
import { Component, computed, inject, Input, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { bottomLeftPosition, updatePopoverPosition } from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { Editor } from 'slate';
import { RelationPageTreeLevel, getRelationPageTreeLevelMenus } from '../../../custom-constants';
import { RelationPageTreeElement } from '../../../custom-types';
import { RelationPageTreeEditor } from '../relation-page-tree.editor';
import { StyxTranslateService } from '@atinc/ngx-styx';

@Component({
    selector: 'wiki-common-page-tree-toolbar',
    templateUrl: './relation-page-tree-toolbar.component.html',
    standalone: false
})
export class WikiCommonRelationPageTreeToolbarComponent {
    translate = inject(StyxTranslateService);

    @Input() editor: Editor;

    @Input() element: RelationPageTreeElement;

    @Input() pageSelection: () => void;

    @Input() changeLevel: (value: RelationPageTreeLevel) => void;

    @Input() getLevel: () => RelationPageTreeLevel;

    @ViewChild('levelTpl') levelTpl: any;

    @ViewChild('dropdownMenuTpl', { static: true }) dropdownMenu: TemplateRef<any>;

    dropdownPopoverRef: ThyPopoverRef<any>;

    levelMenus = computed(() => getRelationPageTreeLevelMenus(this.translate));

    constructor(
        private overlay: Overlay,
        private thyPopover: ThyPopover,
        private viewContainerRef: ViewContainerRef
    ) {}

    removeNode(event: Event) {
        this.preventDefault(event);
        RelationPageTreeEditor.removeNode(this.editor, this.element);
    }

    preventDefault(event: Event) {
        event.stopPropagation();
        event.preventDefault();
    }

    _changeLevel(value: RelationPageTreeLevel) {
        this.changeLevel(value);
    }

    openLevelMenus(event: Event) {
        this.dropdownPopoverRef = this.thyPopover.open(this.dropdownMenu, {
            origin: event.currentTarget as HTMLElement,
            originActiveClass: 'active',
            placement: 'bottom',
            autoAdaptive: false,
            insideClosable: true,
            outsideClosable: true,
            backdropClosable: true,
            hasBackdrop: false,
            offset: 10,
            viewContainerRef: this.viewContainerRef,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });

        const origin = this.levelTpl.elementRef.nativeElement;
        const overlayRef = this.dropdownPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [bottomLeftPosition]);
    }

    openPageSelection() {
        this.pageSelection();
    }
}
