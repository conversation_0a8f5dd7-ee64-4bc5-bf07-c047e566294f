import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { createEmptyParagraph, TheEditor, TheTransforms } from '@worktile/theia';
import { Editor, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { RelationPageTreeElement } from '../../custom-types';

export const RelationPageTreeEditor = {
    insert(editor: TheEditor) {
        const element: RelationPageTreeElement = {
            extension_id: null,
            type: WikiPluginTypes.relationPageTree,
            data: null,
            children: [{ text: '' }]
        };
        TheTransforms.insertElements(editor, element);
    },
    setAttribute(editor: Editor, element: RelationPageTreeElement, value: Partial<RelationPageTreeElement>) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.setNodes(editor, value, { at });
    },
    removeNode(editor: Editor, element: RelationPageTreeElement) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.removeNodes(editor, { at });
        Transforms.insertNodes(editor, createEmptyParagraph(), { at });
        AngularEditor.focus(editor);
    }
};
