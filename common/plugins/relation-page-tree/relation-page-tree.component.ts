import { Overlay } from '@angular/cdk/overlay';
import { AfterViewInit, ChangeDetectionStrategy, Component, inject, OnInit, ViewContainerRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Is, ResponseData } from '@atinc/ngx-styx';
import { PageSystems, PageTypes } from '@wiki/app/constants/page';
import { PageExtensionData, PageExtensionInfo, PageExtensionType, PageTreeExtension } from '@wiki/app/entities/page-extendsion';
import { PageInfo } from '@wiki/app/entities/page-info';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { buildPageTree } from '@wiki/app/util/tree';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { WikiPluginTypes } from '@wiki/common/types';
import { ClientEventEmitter } from '@wiki/common/util/bridge-for-mobile';
import {
    getMode,
    TheBaseElement,
    TheContextService,
    TheModeType,
    TheQueries,
    topLeftPosition,
    updatePopoverPosition
} from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { ThyResizeEvent } from 'ngx-tethys/resizable';
import { ThyTreeEmitEvent, ThyTreeNodeData } from 'ngx-tethys/tree';
import { finalize } from 'rxjs/operators';
import { Editor, Transforms } from 'slate';
import { AngularEditor, SlateElementContext } from 'slate-angular';
import { PageModeOption, RelationPageTreeLevel } from '../../custom-constants';
import { RelationPageTreeElement } from '../../custom-types';
import { TheExtensionMode } from '../../interface/extension-mode';
import { getOutsideOptions } from '../../util/common';
import { isMobileMode } from '../../util/extension-mode';
import { RelationPageTreeEditor } from './relation-page-tree.editor';
import { WikiCommonRelationPageTreeToolbarComponent } from './toolbar/relation-page-tree-toolbar.component';

const DEFAULT_HEIGHT = 320;
@Component({
    selector: 'wiki-common-relation-page-tree',
    templateUrl: './relation-page-tree.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiCommonRelationPageTreeComponent extends TheBaseElement<RelationPageTreeElement, Editor> implements OnInit, AfterViewInit {
    public thyIcons = { expand: 'angle-down', collapse: 'angle-right' };

    public pageNodes: ThyTreeNodeData<PageInfo>[] = [];

    // 选中的页面
    private selectedPages: PageInfo[] = [];

    // 过滤后的页面
    private displayPages: PageInfo[] = [];

    // 禁用的页面
    private disabledPageIds: string[] = [];

    public loadingDone = false;

    private popoverRef: ThyPopoverRef<any>;

    resizeHeight: number;

    spaceId: string;

    spaceIdentifier: string;

    isEmpty = false;

    mode: TheModeType;

    resizeBounds = null;

    isDisabledJump = false;

    includePageHome = true;

    outsideOptions: PageModeOption = {};

    get height() {
        return this.resizeHeight || this.element.height || DEFAULT_HEIGHT;
    }

    get pageId() {
        return this.wikiPluginContext.getPageId();
    }

    get extensionId() {
        return this.element.extension_id;
    }

    get level() {
        return this.element.data?.level || RelationPageTreeLevel.four;
    }

    get isToolbarOpen() {
        return this.popoverRef && this.popoverRef.getOverlayRef() && this.popoverRef.getOverlayRef().hasAttached();
    }

    private router = inject(Router);
    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);
    private contextService = inject(TheContextService);
    private wikiPluginContext = inject(WikiPluginContext);
    private route = inject(ActivatedRoute);
    public viewContainerRef = inject(ViewContainerRef);

    beforeContextChange(context: SlateElementContext<RelationPageTreeElement>) {
        if (this.initialized) {
            if (context.element?.extension_id !== this.extensionId) {
                this.fetchPageNodes(context.element?.extension_id);
            }
            if (context.element?.data.level !== this.level) {
                this.changeLevel(context.element?.data.level);
            }
        }
    }

    onContextChange() {
        super.onContextChange();
        if (!this.mode) {
            this.mode = getMode(this.editor);
        }
        if (isMobileMode(this.mode)) {
            return;
        }
        if (TheQueries.isGlobalCollapsed(this.editor) && this.isCollapsedAndNonReadonly) {
            this.openToolbar();
        } else {
            this.closeToolbar();
        }
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.resizeBounds = {
            nativeElement: this.contextService.getEditableElement()
        };
        this.outsideOptions = getOutsideOptions(this.mode, this.route);
    }

    ngAfterViewInit(): void {
        if (this.element.data) {
            this.fetchPageNodes();
        }
        if (!this.element.data) {
            if (this.readonly) {
                this.isEmpty = true;
                return;
            }
            this.initializeContent();
        }
    }

    initializeContent() {
        const allPages = this.wikiPluginContext.getPages();
        this.selectedPages = allPages.filter(x => x.parent_ids?.length < RelationPageTreeLevel.four);
        this.spaceId = this.wikiPluginContext.getSpaceId();
        this.saveContent();
    }

    fetchPageNodes(extensionId?: string) {
        let mode: TheExtensionMode;
        let fetchId = this.pageId;
        switch (this.mode) {
            case TheExtensionMode.print:
            case TheExtensionMode.outsideSpace:
            case TheExtensionMode.outsidePage:
                mode = this.outsideOptions.mode;
                fetchId = this.outsideOptions.fetchId;
                break;
            case TheExtensionMode.stencils:
                mode = this.mode;
                break;
            default:
                mode = TheExtensionMode.default;
        }

        this.loadingDone = false;
        this.wikiPluginContext
            .fetchPageExtension(fetchId, extensionId || this.extensionId, mode)
            .pipe(
                finalize(() => {
                    this.loadingDone = true;
                    this.cdr.detectChanges();
                })
            )
            .subscribe({
                next: (
                    response: ResponseData<
                        PageExtensionInfo<PageTreeExtension>,
                        {
                            pages?: PageInfo[];
                            space?: SpaceInfo;
                        }
                    >
                ) => {
                    const isNoExist = response.value.data.page_ids.length === 0 || response.references.pages.length === 0;
                    if (isNoExist) {
                        this.isEmpty = true;
                    }
                    // 分享空间的页面不能跨跳转
                    if (
                        mode === TheExtensionMode.outsidePage ||
                        (mode === TheExtensionMode.outsideSpace && response.value.space_id !== response.value.data.space_id)
                    ) {
                        this.isDisabledJump = true;
                    }
                    this.spaceIdentifier = response.references.space.identifier;
                    this.selectedPages = response.references.pages;
                    this.buildDisplayPagesByLevel(this.level);
                    this.includePageHome = this.displayPages.some(x => x.system === PageSystems.home);
                    this.spaceId = response.value.data.space_id;
                    this.pageNodes = this.buildPageTree(this.displayPages, this.disabledPageIds);
                },
                error: () => {
                    this.isEmpty = true;
                    this.loadingDone = true;
                }
            });
    }

    saveContent() {
        const content: { key: string; data: PageExtensionData } = {
            key: this.element.key,
            data: {
                space_id: this.spaceId,
                page_ids: this.selectedPages.filter(x => x.is_visibility === Is.yes).map(x => x._id),
                level: this.level
            }
        };

        this.loadingDone = false;
        return this.wikiPluginContext.savePageExtension(this.pageId, { ...content, type: PageExtensionType.pageTree }).subscribe(
            (
                result: ResponseData<
                    PageExtensionInfo,
                    {
                        pages?: PageInfo[];
                        space?: SpaceInfo;
                    }
                >
            ) => {
                this.spaceIdentifier = result.references.space.identifier;
                const extensionId = result.value._id;
                const newContent = { data: { level: this.level }, extension_id: extensionId };
                RelationPageTreeEditor.setAttribute(this.editor, this.element, newContent);
                this.buildDisplayPagesByLevel(this.level);
                this.includePageHome = this.displayPages.some(x => x.system === PageSystems.home);
                this.pageNodes = this.buildPageTree(this.displayPages, this.disabledPageIds);
                this.loadingDone = true;
                this.cdr.detectChanges();
            }
        );
    }

    onClick(event: ThyTreeEmitEvent) {
        if (event.node.origin.type === PageTypes.group) {
            const isExpanded = event.node.isExpanded;
            event.node.setExpanded(!isExpanded);
        }
    }

    buildPageTree(nodes: PageInfo[], disabledPages: string[]) {
        const pageNodes = buildPageTree(nodes, disabledPages);
        // 默认展开第一层级有子级的节点
        return pageNodes.map(node => {
            if (node.children && node.children.length > 0) {
                return { ...node, expanded: true };
            }
            return node;
        });
    }

    /**
     * 1. 处理 this.disabledPageIds
     * 2. 处理 this.displayPages
     * 3. 处理 this.isEmpty
     * @param level 当前设置的层级
     */
    buildDisplayPagesByLevel(level: RelationPageTreeLevel) {
        // 两种情况展示：
        // 1. 本身有可见性的页面
        const visiblePages = [];
        // 2. 本身不可见的父，但子可见，需要展示的父页面
        const displayAndInvisibleParents = [];
        // 3. 分组下有可见的子，分组也需要展示
        const pageGroups = [];
        this.selectedPages.forEach(x => {
            if (x.type === PageTypes.group) {
                pageGroups.push(x);
                return;
            }
            if (x.is_visibility === Is.yes) {
                visiblePages.push(x);
            } else {
                const hasChildren = this.selectedPages.some(y => y.is_visibility === Is.yes && y.parent_ids.includes(x._id));
                if (hasChildren) {
                    displayAndInvisibleParents.push(x);
                }
            }
        });
        const allPages: PageInfo[] = [...visiblePages, ...displayAndInvisibleParents, ...pageGroups];
        this.isEmpty = !allPages.length;
        // 层级展示的数据只有可见的页面的层数：
        // 比如有 2 级数据 [a, b]，a 是父，b 是子，其中 a 不可见，b 可见，选中的层级是 1，那么最终需要展示 a，b。
        this.disabledPageIds = displayAndInvisibleParents.map(x => x._id);
        const groupIds = pageGroups.map(x => x._id);
        this.displayPages = allPages.filter(x => {
            // 过滤出有效的层级（分组和不可见的父）
            const validLevel = x.parent_ids.filter((id: string) => !this.disabledPageIds.includes(id) && !groupIds.includes(id)).length;
            return validLevel < level;
        });
    }

    changeLevel = (value: RelationPageTreeLevel) => {
        this.buildDisplayPagesByLevel(value);
        this.pageNodes = this.buildPageTree(this.displayPages, this.disabledPageIds);
        this.cdr.detectChanges();
    };

    changeSpaceAndPages = (selectedPages: PageInfo[]) => {
        this.spaceId = selectedPages[0] ? selectedPages[0].space_id : this.spaceId;
        this.selectedPages = selectedPages;
        if (this.isEmpty) {
            this.isEmpty = false;
        }
        this.saveContent();
    };

    openPageSelection() {
        return this.wikiPluginContext.selectRelationPage({
            selectMultiple: true,
            isPageTreeSelection: true,
            isParentChildLinkageEnabled: true,
            selectedIds: this.selectedPages.filter(x => x.is_visibility).map(x => x._id),
            selectedSpaceId: this.spaceId,
            confirmHandle: (selectPages: PageInfo[]) => {
                if (selectPages) {
                    this.changeSpaceAndPages(selectPages);
                }
            }
        });
    }

    openToolbar() {
        if (this.isToolbarOpen) {
            return;
        }

        const origin = this.elementRef.nativeElement;
        this.popoverRef = this.thyPopover.open(WikiCommonRelationPageTreeToolbarComponent, {
            initialState: {
                editor: this.editor,
                element: this.element,
                pageSelection: () => this.openPageSelection(),
                getLevel: () => this.level,
                changeLevel: (value: RelationPageTreeLevel) => {
                    RelationPageTreeEditor.setAttribute(this.editor, this.element, { data: { level: value } });
                }
            },
            origin,
            panelClass: 'the-plugin-toolbar-popover',
            minWidth: 0,
            placement: 'topLeft',
            hasBackdrop: false,
            manualClosure: true,
            insideClosable: false,
            outsideClosable: false,
            scrollStrategy: this.overlay.scrollStrategies.reposition(),
            autoAdaptive: true
        });

        const overlayRef = this.popoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    closeToolbar() {
        if (this.isToolbarOpen) {
            this.popoverRef.close();
        }
    }

    openPage(event: Event, page: PageInfo & { disabled: boolean }) {
        if (!this.readonly || page.disabled || page.type === PageTypes.group) {
            return;
        }
        if (!(<any>event.target).className.includes('styx-awesome-text-words')) {
            return;
        }
        event.preventDefault();
        if (isMobileMode(this.mode)) {
            ClientEventEmitter.open(WikiPluginTypes.relationPage, page.short_id ?? page._id);
            return;
        }
        if (this.outsideOptions.mode === TheExtensionMode.outsideSpace && !this.isDisabledJump) {
            this.router.navigateByUrl(`/spaces/${this.outsideOptions.shortSpaceId}/pages/${page._id}`);
            return;
        }
        const options = this.wikiPluginContext.getRelationPageRoute(this.spaceIdentifier || this.spaceId, page.short_id ?? page._id);
        if (options.url && !this.isDisabledJump) {
            if (options.open_type) {
                this.router.navigateByUrl(options.url);
                return;
            }
            window.open(options.url, '_blank');
        }
    }

    onResize({ height }: ThyResizeEvent) {
        this.resizeHeight = height;
    }

    onEndResize() {
        Transforms.select(this.editor, AngularEditor.findPath(this.editor, this.element));
        RelationPageTreeEditor.setAttribute(this.editor, this.element, { height: this.resizeHeight });
    }
}
