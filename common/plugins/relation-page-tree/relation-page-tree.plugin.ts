import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { createPluginFactory, TheEditor, ThePluginMenuItem, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';
import { WikiCommonRelationPageTreeComponent } from './relation-page-tree.component';
import { RelationPageTreeEditor } from './relation-page-tree.editor';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withRelationPageTree = <T extends TheEditor>(editor: T): T => {
    const { renderElement, isBlockCard, isVoid } = editor;

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationPageTree) {
            return WikiCommonRelationPageTreeComponent;
        }
        return renderElement(element);
    };

    editor.isVoid = (element: Element) => {
        return element.type === WikiPluginTypes.relationPageTree ? true : isVoid(element);
    };

    editor.isBlockCard = (element: Element) => {
        if (element && element.type === WikiPluginTypes.relationPageTree) {
            return true;
        }
        return isBlockCard(element);
    };
    return editor;
};

export const relationPageTreeMenu = (translate: StyxTranslateService): ThePluginMenuItem[] => [
    {
        key: WikiPluginTypes.relationPageTree,
        keywords: `yemianshu,yms,page tree,pagetree,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationPageTree')}`,
        execute: (editor: TheEditor) => RelationPageTreeEditor.insert(editor),
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationPageTree'),
        type: ThePluginMenuItemType.group,
        menuIcon: WikiPluginMenuIcons.relationPageTree,
        displayKey: '/yms'
    }
];

export const createRelationPageTreePlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.relationPageTree,
        withOverrides: withRelationPageTree,
        toolbarItems: [
            {
                key: WikiPluginTypes.relationPageTree,
                icon: 'work',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationPageTree'),
                execute: (editor: TheEditor) => RelationPageTreeEditor.insert(editor)
            }
        ],
        menuItems: relationPageTreeMenu(translate)
    })();
