@use 'ngx-tethys/styles/variables.scss';

.slate-element-relation-page-tree {
    .pages-tree {
        .page-tree-scroll {
            position: relative;
            overflow: auto;
            height: 100%;
            max-height: 320px;
            padding-top: 16px;
            .thy-tree {
                .styx-awesome-text:not(.styx-awesome-text-disabled) {
                    .styx-awesome-text-icon,
                    .styx-awesome-text-words {
                        color: variables.$primary;
                    }
                }

                .thy-tree-node-wrapper {
                    color: variables.$primary;
                    background-color: variables.$bg-default;
                    border: 0;
                    cursor: default;
                }

                .thy-tree-expand:hover {
                    cursor: pointer;
                }

                .thy-tree-node:first-child {
                    .thy-tree-expand {
                        display: inline-flex;
                    }
                }
            }
            .thy-tree.include-page-home {
                .thy-tree-node:first-child {
                    .thy-tree-expand {
                        display: none;
                    }
                }
            }
        }
        .page-tree-scroll.resized {
            max-height: 100%;
        }
    }
    .relation-page-tree-resize-handle {
        cursor: row-resize;
        height: 8px;
        width: 45px;
        left: 50%;
        transform: translateX(-50%);
        background: variables.$gray-300;
        bottom: -5px;
        border-radius: 5px;
        position: absolute;

        &::before,
        &:after {
            content: '';
            position: absolute;
            width: 34px;
            height: 1px;
            background: variables.$bg-default;
            left: 50%;
            transform: translateX(-50%);
            top: 2px;
        }

        &:after {
            top: 5px;
        }
    }
}

.the-editor-readonly {
    .slate-element-relation-page-tree {
        .common-plugin-card-element {
            &.pages-tree {
                .page-tree-scroll {
                    max-height: 100%;
                    transform: translateY(0);
                    .thy-tree {
                        padding: 4px 0;
                        margin-top: 0;
                        transform: translateY(0);
                        // 编辑页鼠标样式小手，预览页是指针
                        .thy-tree-node-wrapper {
                            cursor: default;
                        }
                    }
                }
                .styx-awesome-text:not(.styx-awesome-text-disabled):not(.disabled):not(.page-group):not(.empty-page-group) {
                    color: variables.$primary;
                    .styx-awesome-text-icon,
                    .styx-awesome-text-words {
                        &:hover {
                            cursor: pointer;
                            text-decoration: underline;
                        }
                    }
                }
                .page-group:not(.empty-page-group) {
                    cursor: pointer;
                }
                .empty-page-group {
                    cursor: default;
                }
            }
        }
    }
}
