@if (isEmpty) {
  <common-empty-content iconName="wiki-page-tree" styxI18nTracking [text]="'common.noResult' | translate"></common-empty-content>
} @else {
  <div
    class="common-plugin-card-element pages-tree"
    contenteditable="false"
    thyResizable
    [thyMinHeight]="60"
    [thyBounds]="resizeBounds"
    [style.height.px]="height"
    (thyResize)="onResize($event)"
    (thyResizeEnd)="onEndResize()"
  >
    @if (!loadingDone) {
      <thy-loading [thyDone]="loadingDone"></thy-loading>
    }
    <!-- 此 div 只是为了降低 tree 的优先级，保证自适应下 tree 的滚动 -->
    <div class="page-tree-scroll border-radius-covering" [ngClass]="{ resized: height }">
      @if (loadingDone) {
        <thy-tree
          thySize="sm"
          [ngClass]="{ 'include-page-home': includePageHome }"
          [thyVirtualScroll]="true"
          [thyIcons]="thyIcons"
          [thyNodes]="pageNodes"
          (thyOnClick)="onClick($event)"
        >
          <ng-template #treeNodeTemplate let-node="node" let-page="origin">
            <div class="page-tree-node-content ml-n1 w-100">
              <styx-awesome-text
                [ngClass]="{
                  disabled: isDisabledJump,
                  'page-group': page | isPageGroup,
                  'empty-page-group': (page | isPageGroup) && !node.children?.length
                }"
                [styxText]="page.title"
                styxAutofocus="true"
                [styxDisabled]="(page | isDocument) && page.disabled"
                [styxCode]="page.emoji_icon"
                [styxIconColor]="page | pageIconColor"
                [styxIcon]="page | pageIcon"
                (click)="openPage($event, page)"
              ></styx-awesome-text>
            </div>
          </ng-template>
        </thy-tree>
      }
    </div>
    @if (isCollapsedAndNonReadonly) {
      <thy-resize-handle thyDirection="bottom" class="relation-page-tree-resize-handle"></thy-resize-handle>
    }
  </div>
}
