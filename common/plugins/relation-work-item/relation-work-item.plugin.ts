import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';
import { CommonRelationItemComponent } from '@wiki/common/components/relation-item/relation-item.component';
import { RelationWorkItemElement } from '@wiki/common/custom-types';
import { RelationItemEditor } from '@wiki/common/plugins/relation-item/relation-item.editor';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { verifyAvailableAppAndPermission } from '@wiki/common/util/common';
import { createPluginFactory, ElementKinds, TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';

export const withRelationWorkItem = (editor: TheEditor) => {
    const { isVoid, isInline, renderElement, isBlockCard } = editor;

    editor.isInline = (element: RelationWorkItemElement) => {
        return RelationItemEditor.isRelationItem(element) && !RelationItemEditor.isCard(element) ? true : isInline(element);
    };

    editor.isVoid = (element: RelationWorkItemElement) => {
        return RelationItemEditor.isRelationItem(element) ? true : isVoid(element);
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationWorkItem) {
            return CommonRelationItemComponent;
        }
        return renderElement(element);
    };

    editor.isBlockCard = (element: RelationWorkItemElement) => {
        if (element.type === WikiPluginTypes.relationWorkItem && RelationItemEditor.isCard(element)) {
            return true;
        }
        return isBlockCard(element);
    };

    return editor;
};

export const relationWorkItemMenuItem = (translate: StyxTranslateService) => {
    return {
        key: WikiPluginTypes.relationWorkItem,
        keywords: `gongzuoxiang,gzx,project,work item,workitem,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.workItem')}`,
        execute: (editor: TheEditor) => RelationItemEditor.insertSuggestion(editor, WikiPluginTypes.relationWorkItem),
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.workItem', { isTitle: true, isPlural: false }),
        type: ThePluginMenuItemType.group,
        menuIcon: WikiPluginMenuIcons.relationWorkItem,
        displayKey: '/gzx',
        isDisabled: (editor: TheEditor) => {
            // 无权限和没有购买 project 产品都是禁用
            return !verifyAvailableAppAndPermission(translate, editor, WikiPluginTypes.relationWorkItem);
        },
        removeKeywordsType: 'character' as 'character'
    };
};

export const createRelationWorkItemPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.relationWorkItem,
        withOverrides: withRelationWorkItem,
        toolbarItems: [
            {
                key: WikiPluginTypes.relationWorkItem,
                icon: 'work',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.workItem', { isTitle: true, isPlural: false }),
                execute: (editor: TheEditor) => RelationItemEditor.insertSuggestion(editor, WikiPluginTypes.relationWorkItem)
            }
        ],
        menuItems: [relationWorkItemMenuItem(translate)],
        options: {
            allowParentTypes: [
                ElementKinds.tableCell,
                ElementKinds.blockquote,
                WikiPluginTypes.layoutColumn,
                WikiPluginTypes.toggleListItem,
                WikiPluginTypes.alert
            ]
        }
    })();
