import { Is, MemberInfo, WorkItemStateTypes } from '@atinc/ngx-styx';
import { DateEntry } from 'ngx-tethys/date-picker';
import { RelationOpenType } from '@wiki/common/types/editor.types';

export const WIKI_RELATED_WORK_ITEM_ORIGIN = 'Agile';

export interface RelationWorkItemOption {
    icon: string;
    placeholder: string;
}

export interface RelationWorkItemInfo {
    _id?: string;
    title?: string;
    type?: number;
    identifier?: number;
    whole_identifier?: string;
    state?: {
        name?: string;
        color?: string;
        type?: WorkItemStateTypes;
    };
    project?: {
        name?: string;
    };
    assignee?: MemberInfo;
    due?: DateEntry;
    start?: DateEntry;
    is_archived?: Is;
    is_deleted?: Is;
}

export interface RelationWorkItems {
    [key: string]: RelationWorkItemInfo;
}

export interface RelationWorkItemRoute {
    url?: string;
    open_type?: RelationOpenType;
}
