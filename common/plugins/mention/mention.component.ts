import { Component, HostListener, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { GlobalUsersStore, StyxUserProfileComponent, StyxUserProfileService } from '@atinc/ngx-styx';
import { TheBaseElement, TheEditor } from '@worktile/theia';
import { ThyPopover } from 'ngx-tethys/popover';
import { MentionElement } from '../../custom-types';
import { PageStore } from '../../stores/page.store';

@Component({
    selector: 'wiki-common-mention, [theWikiCommonMention]',
    template: `<a contenteditable="false" class="the-mention-plugin mention" [class.focus]="isCollapsedAndNonReadonly"
        >&#64;{{ memberName }}</a
    >`,
    host: {
        class: 'styx-mention-member'
    },
    standalone: false
})
export class WikiCommonMentionComponent extends TheBaseElement<MentionElement, TheEditor> implements OnInit, On<PERSON><PERSON>roy {
    get memberName(): string {
        const userInfo = this.getMemberByUid(this.element.uid);
        return userInfo && userInfo.display_name;
    }

    @HostListener('click', ['$event'])
    handleClick(event: MouseEvent) {
        if (this.readonly) {
            this.openMemberInformation(event, this.element.uid);
        }
    }

    private userProfileService = inject(StyxUserProfileService);
    private thyPopover = inject(ThyPopover);
    public pageStore = inject(PageStore);
    public globalUsersStore = inject(GlobalUsersStore);

    ngOnInit() {
        super.ngOnInit();
    }

    private openMemberInformation(event: MouseEvent, uid: string) {
        if (this.pageStore.snapshot.isShared) {
            return;
        }
        this.thyPopover.open(StyxUserProfileComponent, {
            origin: event.target as HTMLElement,
            minWidth: 0,
            viewContainerRef: this.viewContainerRef,
            autoAdaptive: true,
            initialState: {
                user: this.userProfileService.fetchUser(uid)
            }
        });
    }

    getMentionData() {
        return this.globalUsersStore.snapshot.teamUsers || [];
    }

    getMentionReferenceData() {
        if (this.pageStore && this.pageStore.snapshot.members) {
            return this.pageStore.snapshot.members || [];
        }
        return [];
    }

    getMemberByUid(id: string) {
        let memberReferences = [...this.getMentionData(), ...this.getMentionReferenceData()];
        if (memberReferences.length) {
            return memberReferences.find(item => id === item.uid);
        }
        return null;
    }

    ngOnDestroy(): void {
        super.ngOnDestroy();
    }
}
