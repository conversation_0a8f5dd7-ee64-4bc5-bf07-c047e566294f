import {
    MentionPluginKey,
    <PERSON>T<PERSON>,
    Nested<PERSON>ey,
    StyxMentionMemberSuggestionComponent,
    StyxMentionUserGroupComponent,
    StyxTranslateService
} from '@atinc/ngx-styx';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { MentionEditor, PluginKeys, TheEditor, ThePluginMenuItemType, createPluginFactory } from '@worktile/theia';
import { WikiPluginTypes } from '../../types/editor.types';
import { WikiCommonMentionComponent } from './mention.component';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

const createMentionMemberPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.mention,
        overrideByKey: {
            [PluginKeys.mention]: {
                options: {
                    isInline: true,
                    mentions: [
                        {
                            type: WikiPluginTypes.mention,
                            trigger: '@',
                            suggestion: StyxMentionMemberSuggestionComponent,
                            render: WikiCommonMentionComponent
                        }
                    ],
                    userGroupSelectable: true,
                    clickable: true
                }
            }
        },
        toolbarItems: [
            {
                key: WikiPluginTypes.mention,
                icon: 'mention',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.member'),
                execute: (editor: TheEditor) => MentionEditor.insertTrigger(editor, WikiPluginTypes.mention),
                active: (editor: TheEditor) =>
                    MentionEditor.isActive(editor, WikiPluginTypes.mention) || MentionEditor.isActive(editor, MentionType.mentionUserGroup)
            }
        ],
        menuItems: [
            {
                key: WikiPluginTypes.mention,
                keywords: `chengyuan,cy,member,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.member')}`,
                execute: (editor: TheEditor) => MentionEditor.insertTrigger(editor, WikiPluginTypes.mention),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.member'),
                icon: 'mention',
                type: ThePluginMenuItemType.icon,
                menuIcon: WikiPluginMenuIcons.mention,
                displayKey: '/cy'
            }
        ]
    })();

const createMentionUserGroupPlugin = createPluginFactory({
    key: MentionPluginKey.mentionUserGroup,
    overrideByKey: {
        [PluginKeys.mention]: {
            options: {
                isInline: true,
                mentions: [
                    {
                        type: MentionType.mentionUserGroup,
                        trigger: '@',
                        render: StyxMentionUserGroupComponent
                    }
                ],
                clickable: true
            }
        }
    }
})();

export function createMentionPlugin(translate: StyxTranslateService) {
    return [createMentionMemberPlugin(translate), createMentionUserGroupPlugin];
}
