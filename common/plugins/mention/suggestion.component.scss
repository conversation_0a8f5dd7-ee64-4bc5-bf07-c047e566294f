@use 'ngx-tethys/styles/variables.scss';

.wiki-common-mention-suggestions {
    .user-group-item {
        .user-group-logo,
        .user-group-name {
            vertical-align: middle;
        }

        .user-group-logo {
            width: variables.$font-size-xlg;
            height: variables.$font-size-xlg;
            border-radius: 50%;
            font-size: variables.$font-size-xlg;
            color: variables.$info;
        }
    }

    .mention-member-group {
        &:first-child {
            margin-top: 8px;
        }

        .group-name {
            width: 100%;
            height: 30px;
            display: flex;
            align-items: center;
            color: variables.$gray-600;
            background-color: variables.$gray-80;

            padding: {
                left: 20px;
                right: 20px;
            }
        }
    }
}
