import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnInit } from '@angular/core';
import { GlobalUserGroupStore, GlobalUsersStore, MemberInfo, MentionType, ScopeDesignationType, UserGroupInfo } from '@atinc/ngx-styx';
import { MentionEditor, TheBaseSuggestion, TheEditor } from '@worktile/theia';
import { PageStore } from '../../stores/page.store';

@Component({
    selector: 'wiki-common-mention-suggestions',
    templateUrl: './suggestion.component.html',
    host: {
        class: 'wiki-common-mention-suggestions thy-mention-suggestions'
    },
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiCommonMentionSuggestionComponent extends TheBaseSuggestion implements OnInit {
    memberList: MemberInfo[];

    userGroupList: {
        type: ScopeDesignationType;
        data: UserGroupInfo;
    }[] = [];

    bindDom: HTMLElement;

    scopeMemberIds: string[];

    constructor(
        public elementRef: ElementRef,
        public globalUsersStore: GlobalUsersStore,
        public pageStore: PageStore,
        private globalUserGroupStore: GlobalUserGroupStore,
        private cdr: ChangeDetectorRef
    ) {
        super();
    }

    ngOnInit() {
        this.bindDom = TheEditor.toDOMNode(this.editor, this.editor);
        this.scopeMemberIds = this.pageStore.snapshot.spaceMembers.map(x => x.uid);
        this.getSearchResult(this.keywords);
    }

    getMentionData() {
        return this.globalUsersStore.snapshot.teamUsers;
    }

    getSearchedData(searchText: string) {
        if (searchText) {
            this.memberList = this.getMentionData().filter(item => {
                return (
                    item.name.toLowerCase().includes(searchText.toLowerCase()) ||
                    item.display_name.includes(searchText) ||
                    (item.display_name_pinyin || '').toLowerCase().includes(searchText.toLowerCase())
                );
            });
            const searchedUserGroups = this.globalUserGroupStore.snapshot.globalUserGroups.filter(userGroup => {
                const nameIncludes = userGroup.name.toLowerCase().includes(searchText);
                const pinyinNameIncludes = userGroup?.name_pinyin.toLowerCase().includes(searchText);
                return nameIncludes || pinyinNameIncludes;
            });
            this.userGroupList = this.buildUserGroups(searchedUserGroups);
        } else {
            this.memberList = this.globalUsersStore.snapshot.teamUsers;
            const spaceUserGroups = this.globalUserGroupStore.snapshot.globalUserGroups;
            this.userGroupList = this.buildUserGroups(spaceUserGroups);
        }
        this.cdr.markForCheck();
    }

    getSearchResult(keywords: string) {
        this.getSearchedData(keywords);
        const parent = (this.elementRef.nativeElement as HTMLElement).closest('.thy-popover-container') as HTMLElement;
        if (this.memberList.length === 0) {
            parent && parent.classList.add('border-0');
        } else {
            parent && parent.classList.remove('border-0');
        }
    }

    private buildUserGroups(userGroups: UserGroupInfo[]) {
        return userGroups.map(item => ({
            type: ScopeDesignationType.userGroup,
            data: item
        }));
    }

    selectMention(event: any) {
        MentionEditor.deleteTriggerText(this.editor);
        if (event.value.type === ScopeDesignationType.userGroup) {
            const userGroupInfo = event.value.data as UserGroupInfo;
            MentionEditor.insertMention<{ _id: string; data: { name: string } }>(this.editor, MentionType.mentionUserGroup, {
                _id: userGroupInfo._id,
                data: {
                    name: userGroupInfo.name
                }
            });
        } else {
            MentionEditor.insertMention<{ uid: string }>(this.editor, this.type, {
                uid: event?.value?.uid || ''
            });
        }
        MentionEditor.close(this.editor);
    }
}
