@if (memberList?.length > 0 || userGroupList?.length > 0) {
  <thy-selection-list
    [thyBindKeyEventContainer]="bindDom"
    thyMultiple="false"
    thyAutoActiveFirstItem="true"
    thySpaceKeyEnabled="false"
    [thyScrollContainer]="elementRef"
    (thySelectionChange)="selectMention($event)"
  >
    @if (memberList | includeMembers: scopeMemberIds; as includeMembers) {
      @if (includeMembers.length) {
        <div class="mention-member-group">
          <span class="group-name" translate="wiki.space.member"></span>
          <ng-container [ngTemplateOutlet]="membersTemplate" [ngTemplateOutletContext]="{ members: includeMembers }"></ng-container>
        </div>
      }
    }
    @if (memberList | excludeMembers: scopeMemberIds; as excludeMembers) {
      @if (excludeMembers.length) {
        <div class="mention-member-group">
          <span class="group-name" translate="wiki.space.settings.pageShared.nonSpaceMember"></span>
          <ng-container [ngTemplateOutlet]="membersTemplate" [ngTemplateOutletContext]="{ members: excludeMembers }"></ng-container>
        </div>
      }
    }
    <ng-template #membersTemplate let-members="members">
      @for (item of members | membersLimit; track trackByFn($index, item)) {
        <thy-list-option [thyValue]="item" [styxMemberProfile]="item">
          <div class="text-truncate">
            <thy-avatar thySize="xs" [thyName]="item.display_name" [thySrc]="item.avatar" [thyShowName]="true"></thy-avatar>
            <span class="text-desc ml-2">({{ item.name }})</span>
          </div>
        </thy-list-option>
      }
    </ng-template>
    @if (userGroupList.length) {
      <div class="mention-member-group">
        <span class="group-name" translate="styx.userGroup"></span>
        @for (item of userGroupList; track trackByFn($index, item)) {
          <thy-list-option [thyValue]="item" [styxMemberProfile]="item">
            @if (item.data; as userGroup) {
              <div class="text-truncate user-group-item">
                @if (item.data.avatar) {
                  <img class="user-group-logo" [src]="item.data.avatar | logoUrl" />
                }
                @if (!item.data.avatar) {
                  <thy-icon class="user-group-logo" thyIconName="user-group-circle-fill"></thy-icon>
                }
                <span class="user-group-name ml-2">{{ item.data.name }}</span>
              </div>
            }
          </thy-list-option>
        }
      </div>
    }
  </thy-selection-list>
}
