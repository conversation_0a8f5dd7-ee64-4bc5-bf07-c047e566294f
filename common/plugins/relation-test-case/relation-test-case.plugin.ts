import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';
import { CommonRelationItemComponent } from '@wiki/common/components/relation-item/relation-item.component';
import { RelationTestCaseElement } from '@wiki/common/custom-types';
import { RelationItemEditor } from '@wiki/common/plugins/relation-item/relation-item.editor';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { verifyAvailableAppAndPermission } from '@wiki/common/util/common';
import { createPluginFactory, ElementKinds, TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';

export const withRelationTestCase = (editor: TheEditor) => {
    const { isVoid, isInline, renderElement, isBlockCard } = editor;

    editor.isInline = (element: RelationTestCaseElement) => {
        return RelationItemEditor.isRelationItem(element) && !RelationItemEditor.isCard(element) ? true : isInline(element);
    };

    editor.isVoid = (element: RelationTestCaseElement) => {
        return RelationItemEditor.isRelationItem(element) ? true : isVoid(element);
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationTestCase) {
            return CommonRelationItemComponent;
        }
        return renderElement(element);
    };

    editor.isBlockCard = (element: RelationTestCaseElement) => {
        if (element.type === WikiPluginTypes.relationTestCase && RelationItemEditor.isCard(element)) {
            return true;
        }
        return isBlockCard(element);
    };

    return editor;
};

export const relationTestCaseMenuItem = (translate: StyxTranslateService) => {
    return {
        key: WikiPluginTypes.relationTestCase,
        keywords: `ceshiyongli,csyl,test cases,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.testCase')}`,
        execute: (editor: TheEditor) => RelationItemEditor.insertSuggestion(editor, WikiPluginTypes.relationTestCase),
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.testCase'),
        type: ThePluginMenuItemType.group,
        menuIcon: WikiPluginMenuIcons.relationTestCase,
        displayKey: '/csyl',
        isDisabled: (editor: TheEditor) => {
            // 无权限和没有购买 testhub 产品都是禁用
            return !verifyAvailableAppAndPermission(translate, editor, WikiPluginTypes.relationTestCase);
        },
        removeKeywordsType: 'character' as 'character'
    };
};

export const createRelationTestCasePlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.relationTestCase,
        withOverrides: withRelationTestCase,
        toolbarItems: [
            {
                key: WikiPluginTypes.relationTestCase,
                icon: 'work',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.testCase'),
                execute: (editor: TheEditor) => RelationItemEditor.insertSuggestion(editor, WikiPluginTypes.relationTestCase)
            }
        ],
        menuItems: [relationTestCaseMenuItem(translate)],
        options: {
            allowParentTypes: [
                ElementKinds.tableCell,
                ElementKinds.blockquote,
                WikiPluginTypes.layoutColumn,
                WikiPluginTypes.toggleListItem,
                WikiPluginTypes.alert
            ]
        }
    })();
