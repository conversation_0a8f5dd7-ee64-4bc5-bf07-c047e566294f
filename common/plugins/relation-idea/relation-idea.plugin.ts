import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';
import { CommonRelationItemComponent } from '@wiki/common/components/relation-item/relation-item.component';
import { RelationIdeaElement } from '@wiki/common/custom-types';
import { RelationItemEditor } from '@wiki/common/plugins/relation-item/relation-item.editor';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { verifyAvailableAppAndPermission } from '@wiki/common/util/common';
import { createPluginFactory, ElementKinds, TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';

export const withIdea = (editor: TheEditor) => {
    const { isVoid, isInline, renderElement, isBlockCard } = editor;

    editor.isInline = (element: RelationIdeaElement) => {
        return RelationItemEditor.isRelationItem(element) && !RelationItemEditor.isCard(element) ? true : isInline(element);
    };

    editor.isVoid = (element: RelationIdeaElement) => {
        return RelationItemEditor.isRelationItem(element) ? true : isVoid(element);
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationIdea) {
            return CommonRelationItemComponent;
        }
        return renderElement(element);
    };

    editor.isBlockCard = (element: RelationIdeaElement) => {
        if (element.type === WikiPluginTypes.relationIdea && RelationItemEditor.isCard(element)) {
            return true;
        }
        return isBlockCard(element);
    };

    return editor;
};

export const relationIdeaMenuItem = (translate: StyxTranslateService) => {
    return {
        key: WikiPluginTypes.relationIdea,
        keywords: `chanpinxuqiu,cpxq,product demand,productdemand,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationIdea')}`,
        execute: (editor: TheEditor) => RelationItemEditor.insertSuggestion(editor, WikiPluginTypes.relationIdea),
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationIdea'),
        type: ThePluginMenuItemType.group,
        menuIcon: WikiPluginMenuIcons.relationIdea,
        displayKey: '/cpxq',
        isDisabled: (editor: TheEditor) => {
            // 无权限和没有购买 ship 产品都是禁用
            return !verifyAvailableAppAndPermission(translate, editor, WikiPluginTypes.relationIdea);
        },
        removeKeywordsType: 'character' as 'character'
    };
};

export const createRelationIdeaPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.relationIdea,
        withOverrides: withIdea,
        toolbarItems: [
            {
                key: WikiPluginTypes.relationIdea,
                icon: 'work',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationIdea'),
                execute: (editor: TheEditor) => RelationItemEditor.insertSuggestion(editor, WikiPluginTypes.relationIdea)
            }
        ],
        menuItems: [relationIdeaMenuItem(translate)],
        options: {
            allowParentTypes: [
                ElementKinds.tableCell,
                ElementKinds.blockquote,
                WikiPluginTypes.layoutColumn,
                WikiPluginTypes.toggleListItem,
                WikiPluginTypes.alert
            ]
        }
    })();
