import { PageExtensionInfo, PageExtensionReferences, PageExtensionType, TestCaseListExtension } from '@wiki/app/entities/page-extendsion';
import { PageApiService } from '@wiki/app/services';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { THE_MODE_TOKEN, TheEditor, TheTransforms, idCreator } from '@worktile/theia';
import { of } from 'rxjs';
import { Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { DefaultPropertyColumns } from '../../custom-constants';
import { RelationTestCaseListElement } from '../../custom-types';
import { CommonBroadObjectService } from '../../services/broad-object.service';
import { PageStore } from '../../stores/page.store';
import { RelationListEditor } from '../../types';
import { WikiPluginTypes } from '../../types/editor.types';

export const RelationTestCaseListEditor: RelationListEditor<TestCaseListExtension> = {
    insert(editor: TheEditor, data: PageExtensionInfo) {
        const element: RelationTestCaseListElement = {
            extension_id: data._id,
            type: WikiPluginTypes.relationTestCaseList,
            data: {
                columns: DefaultPropertyColumns[WikiPluginTypes.relationTestCaseList]
            },
            children: [{ text: '' }]
        };
        TheTransforms.insertElements(editor, element);
    },

    selectItems<RelationTestCaseInfo>(
        editor: TheEditor,
        handle?: (data: PageExtensionInfo<TestCaseListExtension>, references?: PageExtensionReferences) => void,
        elementKey?: string,
        selectedItems?: RelationTestCaseInfo[]
    ) {
        const pageService = editor.injector.get(PageApiService);
        const pageStore = editor.injector.get(PageStore);
        const broadObjectService = editor.injector.get(CommonBroadObjectService);
        const pageId = pageStore.snapshot.page._id;
        const modeConfig = editor.injector.get(THE_MODE_TOKEN);
        const mode = modeConfig.mode as TheExtensionMode;

        broadObjectService.openTestCaseSelection(
            selectedItems,
            relationItems => {
                pageService
                    .savePageExtension<TestCaseListExtension>(
                        pageId,
                        {
                            key: elementKey || idCreator(),
                            type: PageExtensionType.testCaseList,
                            data: { test_case_ids: relationItems?.map(x => x._id) || [] }
                        },
                        mode
                    )
                    .subscribe(result => result && handle(result.value, result.references));
                return of(true);
            },
            { principalId: pageId }
        );
    },

    setAttribute(editor: TheEditor, element: RelationTestCaseListElement, value: { [key: string]: unknown }) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.setNodes(editor, value, { at });
    }
};
