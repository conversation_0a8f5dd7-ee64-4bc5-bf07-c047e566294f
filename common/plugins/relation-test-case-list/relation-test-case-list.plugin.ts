import { createPluginFactory, TheEditor, ThePluginMenuItem, ThePluginMenuItemType } from '@worktile/theia';
import { WikiPluginTypes } from '../../types/editor.types';
import { WikiPluginMenuIcons } from '../../types/plugin-menu';
import { CustomPluginKeys } from '../../types/plugins.types';
import { RelationTestCaseListEditor } from './relation-test-case-list.editor';
import { Element } from 'slate';
import { CommonRelationListComponent } from '../../components/relation-list/relation-list.component';
import { verifyAvailableAppAndPermission } from '../../util/common';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

const withRelationTestCaseList = <T extends TheEditor>(editor: T): T => {
    const { renderElement, isVoid, isBlockCard } = editor;

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationTestCaseList) {
            return CommonRelationListComponent;
        }
        return renderElement(element);
    };

    editor.isVoid = (element: Element) => {
        return element.type === WikiPluginTypes.relationTestCaseList ? true : isVoid(element);
    };

    editor.isBlockCard = (element: Element) => {
        if (element && element.type === WikiPluginTypes.relationTestCaseList) {
            return true;
        }
        return isBlockCard(element);
    };
    return editor;
};

export const relationTestCaseListMenuItem = (translate: StyxTranslateService): ThePluginMenuItem => {
    return {
        key: WikiPluginTypes.relationTestCaseList,
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationTestCaseList'),
        type: ThePluginMenuItemType.group,
        keywords: `ceshiyongliliebiao,csyllb,testcaselist,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.relationTestCaseList')}`,
        menuIcon: WikiPluginMenuIcons.relationTestCaseList,
        displayKey: '/csyllb',
        execute: (editor: TheEditor) =>
            RelationTestCaseListEditor.selectItems(editor, data => RelationTestCaseListEditor.insert(editor, data)),
        isDisabled: (editor: TheEditor) => {
            return !verifyAvailableAppAndPermission(translate, editor, WikiPluginTypes.relationTestCaseList);
        }
    };
};

export const createRelationTestCaseListPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        withOverrides: withRelationTestCaseList,
        menuItems: [relationTestCaseListMenuItem(translate)],
        key: CustomPluginKeys.relationTestCaseList
    })();
