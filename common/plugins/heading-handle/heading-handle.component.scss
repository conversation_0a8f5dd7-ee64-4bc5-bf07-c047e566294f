@use 'ngx-tethys/styles/variables.scss';

.the-heading-handle {
    .handle {
        position: absolute;
        top: 0;
        left: 0;
    }

    .thy-icon {
        color: variables.$gray-500;
        width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        font-size: variables.$font-size-base;
        border-radius: 3px;

        &:hover {
            cursor: pointer;
            background-color: rgba(variables.$primary, 0.15);
            color: variables.$primary;
        }
    }
}
