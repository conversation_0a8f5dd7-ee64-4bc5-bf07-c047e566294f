import { StyxTranslateService } from '@atinc/ngx-styx';
import { createPluginFactory, PluginKeys, TheEditor } from '@worktile/theia';
import { Element } from 'slate';

export const withDisabledHeadingFold = (editor: TheEditor): TheEditor => {
    const { isVisible } = editor;

    editor.isVisible = (element: Element) => {
        return true;
    };

    return editor;
};

export const createDisabledHeadingFoldPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: PluginKeys.heading,
        withOverrides: withDisabledHeadingFold
    })();
