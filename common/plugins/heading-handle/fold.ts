import { setVisibility } from '@wiki/common/util/block-position';
import { isSlateHeading } from '@wiki/common/util/common';
import { coercePixelsFromCssValue } from '@worktile/theia';
import { HANDLE_ICON_SIZE, HeadingHandleScene, HeadingLevel, ICON_OFFSET } from './constants';

export function setFoldIconPosition(
    foldIconDom: HTMLElement,
    highestDom: HTMLElement,
    highestDomRect: DOMRect,
    positionContainerRect: DOMRect,
    scene: HeadingHandleScene
) {
    if (highestDom && isSlateHeading(highestDom) && highestDomRect && positionContainerRect) {
        const textNodes = highestDom.querySelectorAll('[data-slate-string="true"]');
        const lastTextNode = textNodes && textNodes.item(textNodes.length - 1);
        const headingLineHeight = coercePixelsFromCssValue(window.getComputedStyle(lastTextNode || highestDom)['lineHeight']);
        const top = highestDomRect.top - positionContainerRect.top + (headingLineHeight - HANDLE_ICON_SIZE) / 2;
        let offsetLeft = ICON_OFFSET + HANDLE_ICON_SIZE;
        if (scene === HeadingHandleScene.editing) {
            offsetLeft = offsetLeft * 2;
        }
        const left = highestDomRect.left - positionContainerRect.left - offsetLeft;
        foldIconDom.style.top = `${top}px`;
        foldIconDom.style.left = `${left}px`;
        setVisibility(foldIconDom, true);
    } else {
        setVisibility(foldIconDom, false);
    }
}
