import { afterNextRender, Component, computed, DestroyRef, ElementRef, inject, input, signal, viewChild } from '@angular/core';
import { NodeEntry, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { EDITOR_TO_ELEMENT } from 'slate-dom';
import { HeadingElement, TheEditor } from '@worktile/theia';
import { fromEvent, merge, throttleTime } from 'rxjs';
import { findNodeEntryByPoint } from '../../util/common';
import { setVisibility } from '@wiki/common/util/block-position';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { setAnchorIconPosition } from './anchor';
import { HeadingFoldIcon, HeadingHandleScene } from './constants';
import { setFoldIconPosition } from './fold';
import { StyxTranslateService, UtilService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { WikiElement } from '@wiki/common/custom-types';

@Component({
    selector: '[wikiHeadingHandle]',
    templateUrl: './heading-handle.component.html',
    host: {
        class: 'the-heading-handle'
    },
    standalone: false
})
export class WikiCommonHeadingHandleComponent {
    hasHeadingAnchor = input();

    hasHeadingFold = input();

    editor = input<TheEditor>();

    // 监听 mousemove 的容器，必须要包含内容区域延伸出一些 padding，避免鼠标移动到内容区域左侧一些时没有反馈
    triggerOriginContainerClass = input('');

    scene = input<HeadingHandleScene>();

    triggerOriginContainer = computed(() => {
        if (this.scene() === HeadingHandleScene.preview) {
            const className = this.triggerOriginContainerClass();
            return this.elementRef.nativeElement.closest(`.${className}`);
        } else if (this.scene() === HeadingHandleScene.editing) {
            return this.editor() && EDITOR_TO_ELEMENT.get(this.editor());
        }
    });

    positionContainer = computed(() => {
        return this.elementRef.nativeElement.parentElement;
    });

    anchorIcon = viewChild<ElementRef<HTMLElement>>('anchorIcon');

    foldIcon = viewChild<ElementRef<HTMLElement>>('foldIcon');

    foldIconName = signal(HeadingFoldIcon.unfold);

    private util = inject(UtilService);

    private translate = inject(StyxTranslateService);

    private destroyRef = inject(DestroyRef);

    targetElement: HTMLElement;

    targetNodeEntry: NodeEntry<WikiElement>;

    constructor(private elementRef: ElementRef<HTMLElement>) {
        afterNextRender(() => {
            if (this.triggerOriginContainer()) {
                merge(
                    fromEvent<MouseEvent>(this.triggerOriginContainer(), `mousemove`).pipe(
                        throttleTime(20, null, { leading: true, trailing: false })
                    ),
                    fromEvent<MouseEvent>(this.triggerOriginContainer(), `mouseleave`)
                )
                    .pipe(takeUntilDestroyed(this.destroyRef))
                    .subscribe(e => this.setHandleIcon(e));
            }
            if (this.anchorIcon()) {
                setVisibility(this.anchorIcon().nativeElement, false);
            }
            if (this.foldIcon()) {
                setVisibility(this.foldIcon().nativeElement, false);
            }
        });
    }

    copyLink(event: MouseEvent) {
        const fragment = this.targetElement.getAttribute('id');
        try {
            navigator.clipboard.writeText(`${window.location.origin}${window.location.pathname}#${fragment}`);
            this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.copySuccess'));
        } catch (err) {
            this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.copyFailed'));
        }
    }

    setHandleIcon(event: MouseEvent) {
        const nodeEntry = findNodeEntryByPoint(this.triggerOriginContainer(), this.editor(), event.x, event.y, 'highest');
        let highestDom: HTMLElement | null = null;
        let highestDomRect: DOMRect | null = null;
        let positionContainerRect: DOMRect | null = null;
        if (nodeEntry) {
            highestDom = AngularEditor.toDOMNode(this.editor(), nodeEntry[0]);
            positionContainerRect = this.positionContainer().getBoundingClientRect();
            highestDomRect = highestDom.getBoundingClientRect();
        }
        if (this.hasHeadingAnchor()) {
            setAnchorIconPosition(this.anchorIcon().nativeElement, highestDom, highestDomRect, positionContainerRect);
        }
        if (this.hasHeadingFold()) {
            if (this.targetNodeEntry && (this.targetNodeEntry[0] as HeadingElement).isCollapsed) {
                this.foldIconName.set(HeadingFoldIcon.fold);
            } else {
                this.foldIconName.set(HeadingFoldIcon.unfold);
            }
            setFoldIconPosition(this.foldIcon().nativeElement, highestDom, highestDomRect, positionContainerRect, this.scene());
        }
        this.targetElement = highestDom;
        this.targetNodeEntry = nodeEntry;
    }

    toggleHeadingFold = (event: MouseEvent) => {
        const targetSlateElement = AngularEditor.toSlateNode(this.editor(), this.targetElement);
        const targetPath = AngularEditor.findPath(this.editor(), targetSlateElement);
        const newState = !(targetSlateElement as HeadingElement).isCollapsed;
        Transforms.setNodes(this.editor(), { isCollapsed: newState }, { at: targetPath });
        if (newState) {
            this.foldIconName.set(HeadingFoldIcon.fold);
        } else {
            this.foldIconName.set(HeadingFoldIcon.unfold);
        }
    };
}
