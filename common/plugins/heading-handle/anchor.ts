import { setVisibility } from '@wiki/common/util/block-position';
import { isSlateHeading } from '@wiki/common/util/common';
import { coercePixelsFromCssValue } from '@worktile/theia';
import { HANDLE_ICON_SIZE, ICON_OFFSET } from './constants';

export function setAnchorIconPosition(
    anchorIconDom: HTMLElement,
    highestDom: HTMLElement,
    highestDomRect: DOMRect,
    positionContainerRect: DOMRect
) {
    if (highestDom && isSlateHeading(highestDom) && highestDomRect && positionContainerRect) {
        const textNodes = highestDom.querySelectorAll('[data-slate-string="true"]');
        const lastTextNode = textNodes && textNodes.item(textNodes.length - 1);
        if (lastTextNode) {
            const range = document.createRange();
            range.selectNodeContents(lastTextNode.childNodes[0]);
            range.collapse(false);
            const textRect = range.getBoundingClientRect();
            const headingLineHeight = coercePixelsFromCssValue(window.getComputedStyle(lastTextNode)['lineHeight']);
            const top = highestDomRect.bottom - headingLineHeight - positionContainerRect.top + (headingLineHeight - HANDLE_ICON_SIZE) / 2;
            const left = textRect.right - positionContainerRect.left + ICON_OFFSET;
            anchorIconDom.style.top = `${top}px`;
            anchorIconDom.style.left = `${left}px`;
            setVisibility(anchorIconDom, true);
        } else {
            setVisibility(anchorIconDom, false);
        }
    } else {
        setVisibility(anchorIconDom, false);
    }
}
