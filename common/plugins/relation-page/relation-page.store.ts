import { HttpClient } from '@angular/common/http';
import { Injectable, Optional, signal, WritableSignal } from '@angular/core';
import { ResponseData } from '@atinc/ngx-styx';
import { ShareSpaceStore } from 'outside/src/app/stores/share-space.store';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { RelationPageInfo, RelationPages } from './type';

@Injectable()
export class RelationPageStore {
    relationPages: WritableSignal<RelationPages> = signal<RelationPages>({});

    constructor(
        public http: HttpClient,
        @Optional() public shareSpaceStore: ShareSpaceStore
    ) {}

    fetchRelationPage(id: string): Observable<RelationPageInfo> {
        return this.http.get(`/api/wiki/pages/${id}/summary`).pipe(
            map((data: ResponseData<any>) => {
                return data.value;
            })
        );
    }

    initializeRelationPages(relationPages: RelationPages) {
        this.relationPages.set(relationPages);
    }

    getRelationPages(): RelationPages {
        return this.relationPages();
    }

    addRelationPage(value: RelationPageInfo) {
        this.relationPages.set({
            ...this.relationPages(),
            [value._id]: value
        });
    }

    getRelationPageById(id: string) {
        return this.relationPages && this.relationPages()[id];
    }

    getShareSpaceStore() {
        return this.shareSpaceStore;
    }
}
