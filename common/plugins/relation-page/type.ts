import { RelationOpenType } from '@wiki/common/types/editor.types';

export interface RelationPageInfo {
    _id?: string;
    space_id?: string;
    name?: string;
    space_name?: string;
    space_identifier?: string;
    summary?: string;
    emoji_icon?: string;
    short_id?: string;
}

export interface RelationPages {
    [key: string]: RelationPageInfo;
}

export interface RelationPageRoute {
    url?: string;
    open_type?: RelationOpenType;
}
