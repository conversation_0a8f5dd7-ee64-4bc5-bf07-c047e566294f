import { Overlay } from '@angular/cdk/overlay';
import { Component, HostBinding, HostListener, inject, OnInit, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { Router } from '@angular/router';
import { AppRootContext } from '@atinc/ngx-styx/core';
import { StyxTranslateService } from '@atinc/ngx-styx/i18n/core';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { RelationItemState } from '@wiki/common/interface/relation';
import { RelationItemEditor } from '@wiki/common/plugins/relation-item/relation-item.editor';
import { PageStore } from '@wiki/common/stores/page.store';
import { getCommonRelationOption, RelationItemOption } from '@wiki/common/types';
import { RelationOpenType, WikiPluginTypes } from '@wiki/common/types/editor.types';
import { ClientEventEmitter } from '@wiki/common/util/bridge-for-mobile';
import { getDomainUrl } from '@wiki/common/util/common';
import { isMobileMode, isSharedMode } from '@wiki/common/util/extension-mode';
import { getMode, TheBaseElement, TheEditor, TheQueries, topLeftPosition, updatePopoverPosition } from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { finalize } from 'rxjs/operators';
import { RelationPageElement } from '../../custom-types';
import { WikiCardEditor } from '../common/card-editor';
import { RelationPageInfo, RelationPageRoute } from './type';

@Component({
    selector: 'wiki-common-relation-page, [wikiCommonRelationPage]',
    templateUrl: './relation-page.component.html',
    host: {
        '[attr.data-page-url]': 'url',
        '[class.danger-mode]': 'isDanger',
        '[class.text-mode]': 'element?.mode !== "card"',
        '[class.disabled]': 'currentState === RelationItemState.not_found'
    },
    standalone: false
})
export class WikiCommonRelationPageComponent extends TheBaseElement<RelationPageElement, TheEditor> implements OnInit {
    translate = inject(StyxTranslateService);

    page: RelationPageInfo;

    RelationItemState = RelationItemState;

    currentState: RelationItemState = RelationItemState.not_found;

    isFetched = false;

    isDanger: boolean;

    relationPageRoute: RelationPageRoute;

    toolbarPopoverRef: ThyPopoverRef<any>;

    relationItemOption: RelationItemOption;

    mode: TheExtensionMode;

    @HostBinding('style.position') position = 'relative';

    @ViewChild('toolbar', { read: TemplateRef, static: true })
    toolbar: TemplateRef<any>;

    @HostListener('click')
    handleClick() {
        if (isMobileMode(this.mode) && this.element._id) {
            ClientEventEmitter.open(WikiPluginTypes.relationPage, this.element._id);
            return;
        }
        if (this.readonly && this.currentState !== RelationItemState.not_found && this.element._id && this.relationPageRoute?.url) {
            const openType = this.relationPageRoute.open_type ? this.relationPageRoute.open_type : RelationOpenType.BLANK;
            if (openType === RelationOpenType.BLANK) {
                window.open(this.relationPageRoute?.url, RelationOpenType.BLANK);
            }
            if (openType === RelationOpenType.SELF) {
                this.router.navigate([this.relationPageRoute?.url]);
            }
            return;
        }
    }

    get isToolbarOpen() {
        return this.toolbarPopoverRef && this.toolbarPopoverRef.getOverlayRef() && this.toolbarPopoverRef.getOverlayRef().hasAttached();
    }

    get autoFocus() {
        return this.isCollapsedAndNonReadonly && TheQueries.isGlobalCollapsed(this.editor);
    }

    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);
    private appRootContext = inject(AppRootContext);
    public router = inject(Router);
    public pageStore = inject(PageStore);
    public viewContainerRef = inject(ViewContainerRef);

    onContextChange() {
        super.onContextChange();
        if (!this.mode) {
            this.mode = getMode(this.editor) as TheExtensionMode;
        }
        if (!isMobileMode(this.mode)) {
            if (this.isCollapsedAndNonReadonly) {
                this.openToolbar();
            } else {
                this.closeToolbar();
            }
        }
        if (this.initialized && this.element?._id && !this.isFetched) {
            this.initializedData();
        }
    }

    ngOnInit() {
        super.ngOnInit();
        this.relationItemOption = getCommonRelationOption(this.translate)[this.element?.type];
        this.initializedData();
    }

    initializedData() {
        const pageId = this.element._id;
        const page = this.pageStore.relationPageStore.getRelationPageById(pageId);
        if (pageId) {
            this.usePageData(page);
        }
    }

    openToolbar() {
        if (!TheQueries.isGlobalCollapsed(this.editor) || this.isToolbarOpen) {
            return;
        }
        const origin = this.elementRef.nativeElement as HTMLElement;
        this.toolbarPopoverRef = this.thyPopover.open(this.toolbar, {
            origin,
            viewContainerRef: this.viewContainerRef,
            minWidth: 0,
            panelClass: 'the-plugin-toolbar-popover',
            manualClosure: true,
            hasBackdrop: false,
            insideClosable: true,
            outsideClosable: false,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });

        const overlayRef = this.toolbarPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    closeToolbar() {
        if (this.isToolbarOpen) {
            this.toolbarPopoverRef.close();
        }
    }

    usePageData(page?: RelationPageInfo) {
        if (page) {
            this.isFetched = true;
            this.pageDoneHandler(page);
            return;
        }
        this.fetchPage();
    }

    pageErrorHandler() {
        this.currentState = RelationItemState.not_found;
        this.cdr.markForCheck();
    }

    getRelationPageRoute(page: RelationPageInfo): RelationPageRoute {
        const { space_id, _id, space_identifier, short_id } = page;
        const identifier = space_identifier ?? space_id;
        if (isSharedMode(this.mode) || isMobileMode(this.mode)) {
            const shareSpaceStore = this.pageStore.relationPageStore.getShareSpaceStore();
            const currentSpace = shareSpaceStore?.snapshot?.spaceInfo;
            if (shareSpaceStore && currentSpace && currentSpace?._id === space_id) {
                return {
                    url: `/spaces/${currentSpace.shortId}/pages/${short_id ?? _id}`,
                    open_type: RelationOpenType.SELF
                };
            }
        }
        if (this.mode === TheExtensionMode.default || this.mode === TheExtensionMode.print) {
            return {
                url: `${getDomainUrl(
                    this.appRootContext.globalInfo.config.baseUrlFormat,
                    this.appRootContext.team.domain
                )}/wiki/spaces/${identifier}/pages/${short_id ?? _id}`
            };
        }
        return {
            url: ''
        };
    }

    pageDoneHandler(page: RelationPageInfo) {
        this.page = page;
        this.relationPageRoute = this.getRelationPageRoute(page);
        this.currentState = RelationItemState.success;
    }

    fetchPage() {
        this.currentState = RelationItemState.loading;
        this.pageStore.relationPageStore
            .fetchRelationPage(this.element._id)
            .pipe(
                finalize(() => {
                    this.isFetched = true;
                    this.cdr.detectChanges();
                })
            )
            .subscribe({
                next: (page: RelationPageInfo) => {
                    if (page) {
                        this.pageStore.relationPageStore.addRelationPage(page);
                        this.usePageData(page);
                    } else {
                        this.pageErrorHandler();
                    }
                },
                error: () => {
                    this.pageErrorHandler();
                }
            });
    }

    wrapText(event: MouseEvent) {
        event.preventDefault();
        RelationItemEditor.wrapText(this.editor, WikiPluginTypes.relationPage, this.element);
    }

    switchCard(event: MouseEvent) {
        event.preventDefault();
        WikiCardEditor.switchCard(this.editor, this.element);
    }

    openPage(event: KeyboardEvent) {
        event.preventDefault();
        window.open(this.relationPageRoute?.url, '_blank');
    }

    removeNode(event: Event) {
        event.preventDefault();
        RelationItemEditor.removeNode(this.editor, this.element);
    }

    onEnterDelete() {
        this.isDanger = true;
    }

    onLeaveDelete() {
        this.isDanger = false;
    }
}
