@if (currentState === RelationItemState.loading) {
  <span contenteditable="false" class="common-placeholder-loading text-mode-placeholder">
    <styx-awesome-text [styxIcon]="1 | pageIcon" [styxIconColor]="1 | pageIconColor"></styx-awesome-text>
  </span>
} @else {
  @if (currentState === RelationItemState.not_found) {
    <common-empty-content [iconName]="relationItemOption.emptyIcon" [text]="relationItemOption.notFoundText"></common-empty-content>
  } @else {
    @if (element?.mode === 'text') {
      <styx-awesome-text
        class="pr-1 ml-0 common-plugin-card-element relation-page-inline"
        [ngClass]="{ pointer: this.relationPageRoute?.url }"
        [styxText]="page?.name"
        [styxCode]="page?.emoji_icon"
        [styxIcon]="page | pageIcon"
        [styxIconColor]="page | pageIconColor"
        [styxActive]="true"
      ></styx-awesome-text>
    } @else {
      <wiki-common-relation-card [ngClass]="{ pointer: this.relationPageRoute?.url }" [page]="page"></wiki-common-relation-card>
    }
  }
}

<ng-template #toolbar>
  <thy-actions thySize="xxs">
    @if (currentState !== RelationItemState.not_found && currentState !== RelationItemState.empty) {
      @if (element?.mode === 'card') {
        <a
          href="javascript:;"
          thyAction
          thyActionIcon="inline"
          styxI18nTracking
          [thyTooltip]="'styx.text' | translate"
          (mousedown)="wrapText($event)"
        ></a>
      }
      @if (element?.mode === 'text') {
        <a
          href="javascript:;"
          thyAction
          thyActionIcon="float-center"
          styxI18nTracking
          [thyTooltip]="'styx.card' | translate"
          (mousedown)="switchCard($event)"
        ></a>
      }
      @if (currentState !== RelationItemState.loading) {
        <thy-divider class="mr-2 ml-1 align-self-center" thyColor="light" [thyVertical]="true"></thy-divider>
      }
      <a
        href="javascript:;"
        thyAction
        thyActionIcon="publish"
        styxI18nTracking
        [thyTooltip]="'styx.openNew' | translate"
        (mousedown)="openPage($event)"
      ></a>
      <thy-divider class="mr-2 ml-1 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
    }
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      (mousedown)="removeNode($event)"
      (mouseenter)="onEnterDelete()"
      (mouseleave)="onLeaveDelete()"
    ></a>
  </thy-actions>
</ng-template>
