@use 'ngx-tethys/styles/variables.scss';
@use '../../styles/variables.scss' as commonVariables;
@use './card/card.component.scss';

.slate-element-relation-page {
    &:not(.text-mode) {
        display: block;
    }

    &.text-mode {
        display: inline-block;
        max-width: 100%;
        border: 2px solid transparent;

        .styx-awesome-text {
            height: 28px;
            margin-left: 2px;
            .styx-awesome-text-emoji {
                margin-right: 0;
            }
        }
    }

    &.disabled {
        .relation-page-block {
            cursor: inherit;
        }
        .relation-page-inline {
            color: variables.$gray-400;
        }
    }
    .pointer {
        cursor: pointer;
    }
}

.relation-page-inline {
    max-width: 100%;
    color: variables.$primary;
    white-space: initial;

    .styx-awesome-text-content {
        // 当页面名称过长时，需要折行显示，此时 icon 应该与文字第一行对齐
        align-items: initial !important;
    }
}
