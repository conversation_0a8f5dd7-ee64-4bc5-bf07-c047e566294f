import { RelationItemEditor } from '@wiki/common/plugins/relation-item/relation-item.editor';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { createPluginFactory, ElementKinds, TheEditor, ThePluginMenuItem, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';
import { RelationPageElement } from '../../custom-types';
import { WikiCommonRelationPageComponent } from './relation-page.component';
import { RelationPageEditor } from './relation-page.editor';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withRelationPage = (editor: TheEditor) => {
    const { isVoid, isInline, isBlockCard, renderElement } = editor;

    editor.isInline = (element: RelationPageElement) => {
        return RelationPageEditor.isRelationPage(element) && !RelationPageEditor.isCard(element) ? true : isInline(element);
    };

    editor.isVoid = (element: RelationPageElement) => {
        return RelationPageEditor.isRelationPage(element) ? true : isVoid(element);
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationPage) {
            return WikiCommonRelationPageComponent;
        }
        return renderElement(element);
    };

    editor.isBlockCard = (element: RelationPageElement) => {
        if (element && element.type === WikiPluginTypes.relationPage && RelationPageEditor.isCard(element)) {
            return true;
        }
        return isBlockCard(element);
    };

    return editor;
};

export const relationPageMenu = (translate: StyxTranslateService): ThePluginMenuItem => {
    return {
        key: WikiPluginTypes.relationPage,
        keywords: `yemian,ym,page,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.page')}`,
        execute: (editor: TheEditor) => RelationItemEditor.insertSuggestion(editor, WikiPluginTypes.relationPage),
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.page', {  isTitle: true, isPlural: false }),
        type: ThePluginMenuItemType.group,
        menuIcon: WikiPluginMenuIcons.relationPage,
        displayKey: '/ym',
        removeKeywordsType: 'character' as 'character'
    };
};

export const createRelationPagePlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.relationPage,
        withOverrides: withRelationPage,
        toolbarItems: [
            {
                key: WikiPluginTypes.relationPage,
                icon: 'relation-page',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('styx.page', { isTitle: true, isPlural: false }),
                execute: (editor: TheEditor) => RelationItemEditor.insertSuggestion(editor, WikiPluginTypes.relationPage)
            }
        ],
        menuItems: [relationPageMenu(translate)],
        options: {
            allowParentTypes: [
                ElementKinds.tableCell,
                ElementKinds.blockquote,
                WikiPluginTypes.layoutColumn,
                WikiPluginTypes.toggleListItem,
                WikiPluginTypes.alert
            ]
        }
    })();
