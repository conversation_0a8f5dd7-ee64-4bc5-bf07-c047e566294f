import { RelationPageElement } from '@wiki/common/custom-types';
import { TheEditor } from '@worktile/theia';
import { Editor, Element } from 'slate';

export const RelationPageEditor = {
    isRelationPage(element: Element) {
        if (element.type && element.type === 'relation-page') {
            return true;
        }
        return false;
    },
    isCard(relation: RelationPageElement) {
        if (relation.mode === 'card') {
            return true;
        }
        return false;
    },
    isRelationPageActive(editor: TheEditor) {
        const [relation] = Editor.nodes(editor, { match: (n: Element) => RelationPageEditor.isRelationPage(n) });
        return !!relation;
    }
};
