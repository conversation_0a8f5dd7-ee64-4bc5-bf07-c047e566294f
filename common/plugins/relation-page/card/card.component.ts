import { Component, OnInit, ElementRef, Input, HostBinding } from '@angular/core';
import { Range } from 'slate';
import { RelationPageInfo } from '../type';

@Component({
    selector: 'wiki-common-relation-card, [wikiCommonRelationCard]',
    templateUrl: 'card.component.html',
    standalone: false
})
export class WikiCommonRelationCardComponent implements OnInit {
    @HostBinding('class')
    class = 'relation-page-block common-plugin-card-element';

    @Input() page: RelationPageInfo;

    @Input() selection: Range;

    constructor(public elementRef: ElementRef) {}

    ngOnInit() {}
}
