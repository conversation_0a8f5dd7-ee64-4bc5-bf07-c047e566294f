import { Overlay } from '@angular/cdk/overlay';
import { ChangeDetectionStrategy, Component, OnDestroy, OnInit, ViewContainerRef, inject } from '@angular/core';
import { isMobileMode } from '@wiki/common/util/extension-mode';
import { TheBaseElement, TheEditor, getMode } from '@worktile/theia';
import { CompatibleDate, DateEntry, DatePopup } from 'ngx-tethys/date-picker';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { TinyDate } from 'ngx-tethys/util';
import { Subscription, fromEvent } from 'rxjs';
import { BeforeContextChange, SlateElementContext } from 'slate-angular';
import { DateElement } from '../../custom-types';
import { DateEditor } from './date.editor';

@Component({
    selector: 'wiki-common-date, [wikiCommonDate]',
    templateUrl: './date.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        '[class.readonly]': 'readonly',
        '(click)': 'handleClick($event)'
    },
    standalone: false
})
export class WikiCommonDateComponent
    extends TheBaseElement<DateElement, TheEditor>
    implements OnInit, OnDestroy, BeforeContextChange<SlateElementContext>
{
    thyPopoverEditRef: ThyPopoverRef<any>;

    subscription: Subscription;

    public viewContainerRef = inject(ViewContainerRef);
    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);

    get isDateEditOpened(): boolean {
        return this.thyPopoverEditRef && this.thyPopoverEditRef.componentInstance;
    }

    get date(): DateEntry {
        return {
            date: this.element.date,
            with_time: 0
        };
    }

    beforeContextChange = (value: SlateElementContext) => {
        if ((!value.selection && this.editor && this.editor.selection) || value.readonly) {
            this.closePopover();
        }
    };

    ngOnInit(): void {
        super.ngOnInit();
    }

    ngOnDestroy(): void {
        super.ngOnDestroy();
        this.closePopover();
    }

    handleClick(event: MouseEvent) {
        event.preventDefault();
        event.stopPropagation();
        this.openDatePicker();
    }

    openDatePicker(): void {
        if (this.readonly || isMobileMode(getMode(this.editor))) {
            return;
        }

        this.thyPopoverEditRef = this.thyPopover.open(DatePopup, {
            origin: this.elementRef.nativeElement,
            hasBackdrop: false,
            backdropClosable: true,
            viewContainerRef: this.viewContainerRef,
            scrollStrategy: this.overlay.scrollStrategies.reposition(),
            backdropClass: 'thy-overlay-transparent-backdrop',
            panelClass: 'wiki-common-date-popover',
            minWidth: 0,
            placement: 'bottomLeft',
            outsideClosable: true,
            initialState: {
                isRange: false,
                showWeek: false,
                showTime: false,
                mustShowTime: false,
                showShortcut: false,
                panelMode: 'date',
                format: 'yyyy-MM-dd',
                disabledDate: this.readonly,
                defaultPickerValue: this.element.date
            }
        });
        if (this.thyPopoverEditRef) {
            const componentInstance = this.thyPopoverEditRef.componentInstance;
            this.subscription = fromEvent((this.thyPopoverEditRef.containerInstance as any).elementRef.nativeElement, 'click').subscribe(
                (event: MouseEvent) => {
                    event.stopPropagation();
                }
            );
            componentInstance.valueChange.subscribe((event: CompatibleDate) => this.dateChange(event));
        }
    }

    dateChange(date: CompatibleDate) {
        const selectTime = (date as unknown as TinyDate).getUnixTime();
        DateEditor.setDateValue(this.editor, selectTime, this.element);
        this.closePopover();
    }

    closePopover() {
        if (this.isDateEditOpened) {
            this.thyPopoverEditRef.close();
            this.subscription.unsubscribe();
        }
    }
}
