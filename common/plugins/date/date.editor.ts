import { TheEditor, TheQueries, TheTransforms } from '@worktile/theia';
import { Node, Path, Range, Transforms } from 'slate';
import { DateElement } from '../../custom-types';
import { WikiPluginTypes } from '../../types/editor.types';
import { TinyDate } from 'ngx-tethys/util';

export const DateEditor = {
    insertDate(editor: TheEditor) {
        const dateElement = {
            type: WikiPluginTypes.date,
            date: new TinyDate().getTime(),
            children: [{ text: '' }]
        } as DateElement;
        Promise.resolve()
            .then(() => {
                const { selection } = editor;
                const isCollapsed = selection && Range.isCollapsed(selection);
                if (isCollapsed) {
                    TheTransforms.insertInlineElement(editor, dateElement);
                } else {
                    Transforms.delete(editor);
                    Transforms.insertNodes(editor, dateElement);
                }
            })
            .then(() => {
                const dateNode = Node.parent(editor, editor.selection.anchor.path);
                const element = TheEditor.toDOMNode(editor, dateNode);
                (element.querySelector('.wiki-common-date') as HTMLElement).click();
            });
    },
    setDateValue(editor: TheEditor, date: number, element?: DateElement) {
        let path: Path;
        if (!element) {
            const node = TheQueries.getAboveByType(editor, WikiPluginTypes.date);
            if (!node) {
                return;
            }
            path = node && node[1];
        } else {
            path = TheEditor.findPath(editor, element);
        }
        Transforms.setNodes(editor, { date }, { at: path });
        TheEditor.focus(editor as TheEditor);
        Transforms.select(editor, Path.next(path));
        Transforms.collapse(editor, { edge: 'start' });
    }
};
