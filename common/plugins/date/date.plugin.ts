import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { createPluginFactory, TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';
import { WikiElement } from '../../custom-types';
import { WikiPluginTypes } from '../../types/editor.types';
import { WikiCommonDateComponent } from './date.component';
import { DateEditor } from './date.editor';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withDate = (editor: TheEditor) => {
    const { isVoid, isInline, renderElement } = editor;

    editor.isInline = (element: WikiElement) => (element.type === WikiPluginTypes.date ? true : isInline(element));

    editor.isVoid = (element: WikiElement) => (element.type === WikiPluginTypes.date ? true : isVoid(element));

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.date) {
            return WikiCommonDateComponent;
        }
        return renderElement(element);
    };

    return editor;
};

export const createDatePlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.date,
        withOverrides: withDate,
        toolbarItems: [
            {
                key: WikiPluginTypes.date,
                icon: 'calendar',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.date'),
                execute: (editor: TheEditor) => DateEditor.insertDate(editor)
            }
        ],
        menuItems: [
            {
                key: WikiPluginTypes.date,
                keywords: `riqi,rq,date,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.date')}`,
                execute: editor => DateEditor.insertDate(editor),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.date'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.date,
                displayKey: '/rq'
            }
        ],
        options: {
            isInline: true
        }
    })();
