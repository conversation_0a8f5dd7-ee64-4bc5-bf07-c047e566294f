import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';
import { CommonRelationItemComponent } from '@wiki/common/components/relation-item/relation-item.component';
import { RelationObjectiveElement } from '@wiki/common/custom-types';
import { RelationItemEditor } from '@wiki/common/plugins/relation-item/relation-item.editor';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { verifyAvailableAppAndPermission } from '@wiki/common/util/common';
import { createPluginFactory, ElementKinds, TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';

export const withRelationObjective = (editor: TheEditor) => {
    const { isVoid, isInline, renderElement, isBlockCard } = editor;

    editor.isInline = (element: RelationObjectiveElement) => {
        return RelationItemEditor.isRelationItem(element) && !RelationItemEditor.isCard(element) ? true : isInline(element);
    };

    editor.isVoid = (element: RelationObjectiveElement) => {
        return RelationItemEditor.isRelationItem(element) ? true : isVoid(element);
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationObjective) {
            return CommonRelationItemComponent;
        }
        return renderElement(element);
    };

    editor.isBlockCard = (element: RelationObjectiveElement) => {
        if (element.type === WikiPluginTypes.relationObjective && RelationItemEditor.isCard(element)) {
            return true;
        }
        return isBlockCard(element);
    };

    return editor;
};

export const relationObjectiveMenuItem = (translate: StyxTranslateService) => {
    return {
        key: WikiPluginTypes.relationObjective,
        keywords: `mubiao, mb, objective, target, goal, ${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.resource.objective')}`,
        execute: (editor: TheEditor) => RelationItemEditor.insertSuggestion(editor, WikiPluginTypes.relationObjective),
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.resource.objective'),
        type: ThePluginMenuItemType.group,
        menuIcon: WikiPluginMenuIcons.relationObjective,
        displayKey: '/mb',
        isDisabled: (editor: TheEditor) => {
            return !verifyAvailableAppAndPermission(translate, editor, WikiPluginTypes.relationObjective);
        },
        removeKeywordsType: 'character' as 'character'
    };
};

export const createRelationObjectivePlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.relationObjective,
        withOverrides: withRelationObjective,
        menuItems: [relationObjectiveMenuItem(translate)],
        options: {
            allowParentTypes: [
                ElementKinds.tableCell,
                ElementKinds.blockquote,
                WikiPluginTypes.layoutColumn,
                WikiPluginTypes.toggleListItem,
                WikiPluginTypes.alert
            ]
        }
    })();
