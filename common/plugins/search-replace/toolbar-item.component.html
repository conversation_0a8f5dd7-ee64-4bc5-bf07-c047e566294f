<a
  href="javascript:;"
  thyAction
  [thyActionIcon]="toolbarItem?.icon"
  [thyActionActive]="isOpen"
  [thyTooltip]="toolbarItem?.name"
  thyTooltipPlacement="top"
  (mousedown)="execute($event)"
></a>

<ng-template #searchReplaceTemplate>
  <thy-layout>
    <thy-header [thyDivided]="true">
      <ng-template #headerTitle>
        <thy-nav>
          @for (tab of tabs; track tab.key) {
            <a href="javascript:;" thyNavLink [thyNavLinkActive]="currentTab === tab.key" (mousedown)="switchTab($event, tab)">{{
              tab.name
            }}</a>
          }
        </thy-nav>
      </ng-template>
      <ng-template #headerOperation>
        <a thyAction thyIcon="close-bold" href="javascript:;" (click)="closePopover()"></a>
      </ng-template>
    </thy-header>
    <thy-content class="pt-4 px-5 pb-4">
      <form thyForm name="searchForm" #searchForm="thyForm" thyLayout="vertical" thyEnterKeyMode="forbidSubmit">
        <thy-form-group styxI18nTracking [thyLabelText]="'wiki.search.replace.find' | translate">
          <thy-input
            [thyAutofocus]="true"
            name="search"
            styxI18nTracking
            [placeholder]="'wiki.search.replace.input' | translate"
            [(ngModel)]="keyword"
            (ngModelChange)="search($event)"
          >
            <ng-template #append>
              <span class="">{{ currentIndex }}/{{ searchTotal }}</span>
            </ng-template>
          </thy-input>
        </thy-form-group>
        @if (currentTab === SearchReplaceTypes.replace) {
          <thy-form-group styxI18nTracking [thyLabelText]="'wiki.search.replace.replaceTo' | translate">
            <input
              thyInput
              name="replace"
              styxI18nTracking
              [placeholder]="'wiki.search.replace.input' | translate"
              [(ngModel)]="replaceKeyword"
            />
          </thy-form-group>
        }
        <thy-form-group-footer [class]="{ between: currentTab === SearchReplaceTypes.replace }" thyAlign="left">
          @if (currentTab === SearchReplaceTypes.replace) {
            <button
              thyButton="outline-primary"
              thySize="sm"
              [disabled]="buttonDisabled"
              (click)="allReplace()"
              translate="wiki.search.replace.replaceAll"
            ></button>
          }
          @if (currentTab === SearchReplaceTypes.replace) {
            <button
              thyButton="outline-primary"
              thySize="sm"
              [disabled]="buttonDisabled"
              (click)="singleReplace()"
              translate="wiki.search.replace.replace"
            ></button>
          }
          <button
            thyButton="outline-primary"
            thySize="sm"
            [disabled]="buttonDisabled"
            (click)="prev()"
            translate="wiki.search.replace.previous"
          ></button>
          <button
            thyButton="outline-primary"
            thySize="sm"
            [disabled]="buttonDisabled"
            (click)="next()"
            translate="wiki.search.replace.next"
          ></button>
        </thy-form-group-footer>
      </form>
    </thy-content>
  </thy-layout>
</ng-template>
