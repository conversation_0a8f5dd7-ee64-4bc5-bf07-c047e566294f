import { ChangeDetectionStrategy, Component, HostBinding } from '@angular/core';
import { BaseLeafComponent } from 'slate-angular';
import { SearchHighlightKey } from './constants';

@Component({
    selector: 'span[searchHighlightingLeaf]',
    template: `<span slateString [context]="context" [viewContext]="viewContext"><span></span></span>`,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiCommonSearchHighlightingLeafComponent extends BaseLeafComponent {
    @HostBinding('class.search-highlighting-leaf') get highlight() {
        return this.leaf[SearchHighlightKey];
    }
}
