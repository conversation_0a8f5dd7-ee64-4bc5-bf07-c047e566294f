import { Overlay } from '@angular/cdk/overlay';
import {
    ChangeDetectorRef,
    Component,
    ElementRef,
    HostBinding,
    inject,
    OnInit,
    TemplateRef,
    ViewChild,
    ViewContainerRef
} from '@angular/core';
import { CommonEditingPageStore } from '@wiki/common/stores/editing-page.store';
import { COMMON_SEARCH_REPLACE_KEYWORDS } from '@wiki/common/util/weak-map';
import { TheBaseToolbarItem, TheContextService, TheEditor } from '@worktile/theia';
import * as _lodash from 'lodash';
import { ScrollToService } from 'ngx-tethys/core';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { Range, Text, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { SearchDirection, SearchReplaceTab, SearchReplaceTypes } from './constants';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-common-search-replace-toolbar-item',
    templateUrl: './toolbar-item.component.html',
    standalone: false
})
export class WikiCommonSearchReplaceToolbarItemComponent extends TheBaseToolbarItem implements OnInit {
    translate = inject(StyxTranslateService);

    SearchReplaceTypes = SearchReplaceTypes;
    currentTab = SearchReplaceTypes.search;
    tabs: SearchReplaceTab[] = [
        {
            key: SearchReplaceTypes.search,
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.search.replace.find')
        },
        {
            key: SearchReplaceTypes.replace,
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.search.replace.replace')
        }
    ];
    dropdownPopoverRef: ThyPopoverRef<any>;
    keyword = '';
    replaceKeyword = '';
    currentIndex = 0;
    searchTotal = 0;
    searchElements: Element[] = [];

    get buttonDisabled() {
        return this.searchTotal === 0;
    }

    get isOpen() {
        return this.dropdownPopoverRef && this.dropdownPopoverRef.getOverlayRef() && this.dropdownPopoverRef.getOverlayRef().hasAttached();
    }

    search = _lodash.debounce(function (keyword: string) {
        return this.searchKeyword(keyword);
    }, 500);

    @ViewChild('searchReplaceTemplate', { static: true })
    searchReplaceTemplate: TemplateRef<any>;

    @HostBinding('class.the-toolbar-item') toolbarItemContainer = true;

    constructor(
        public elementRef: ElementRef,
        private thyPopover: ThyPopover,
        private viewContainerRef: ViewContainerRef,
        private overlay: Overlay,
        private editingPageStore: CommonEditingPageStore,
        private cdr: ChangeDetectorRef
    ) {
        super();
    }

    ngOnInit() {}

    selectionChange(editor: TheEditor): void {
        setTimeout(() => {
            this.getSearchElements();
            if (this.currentIndex > this.searchTotal) {
                this.currentIndex = 0;
            }
            this.cdr.detectChanges();
        });
    }

    execute(event: MouseEvent) {
        super.execute(event);
        if (this.isOpen) {
            this.closePopover();
            return;
        }
        this.openPopover();
    }

    public statusChange = _lodash.debounce(function (ediotr: TheEditor) {
        if (this.isOpen) {
            this.getSearchElements();
            if (this.searchTotal > 0 && this.currentIndex === 0) {
                this.currentIndex = 1;
                this.setHighlight(SearchDirection.init, true);
            }
        }
    }, 500);

    switchTab(event: Event, tab: SearchReplaceTab) {
        event.preventDefault();
        event.stopPropagation();
        this.currentTab = tab.key;
    }

    getSearchElements(direction: SearchDirection = SearchDirection.init, isScroll: boolean = false) {
        const contextService = this.editor.injector.get(TheContextService);
        const editableElement = contextService.getEditableElement();
        this.searchElements = Array.from(editableElement.querySelectorAll('span.search-highlighting-leaf'));
        this.searchTotal = this.searchElements?.length || 0;
        this.setHighlight(direction, isScroll);
    }

    searchKeyword(keyword: string) {
        COMMON_SEARCH_REPLACE_KEYWORDS.set(this.editor, keyword);
        this.editingPageStore.updateDecorate();
        setTimeout(() => {
            this.getSearchElements();
            if (this.searchTotal > 0) {
                this.currentIndex = 1;
                this.setHighlight();
            }
            if (this.searchTotal === 0) {
                this.currentIndex = 0;
            }
            this.cdr.detectChanges();
        });
    }

    replace(range: Range) {
        if (!range) {
            return;
        }
        Transforms.select(this.editor, range);
        Transforms.insertText(this.editor, this.replaceKeyword);
    }

    allReplace() {
        this.getSearchElements();
        const ranges = this.searchElements.map(element => this.getElementRange(element));
        ranges.reverse().forEach(range => this.replace(range));
        setTimeout(() => {
            this.getSearchElements();
            if (this.searchTotal === 0) {
                this.currentIndex = 0;
            }
        });
    }

    singleReplace() {
        this.getSearchElements();

        const index = this.currentIndex - 1;
        const element = this.searchElements[index];
        const range = this.getElementRange(element);
        this.replace(range);

        if (this.currentIndex === this.searchTotal) {
            this.currentIndex = 1;
        }
        setTimeout(() => {
            this.getSearchElements(SearchDirection.init, true);
            if (this.searchTotal === 0) {
                this.currentIndex = 0;
            }
        });
    }

    getElementRange(element: Element | HTMLElement) {
        const slateNode = AngularEditor.toSlateNode(this.editor, element);
        const slatePath = AngularEditor.findPath(this.editor, slateNode);
        const { text } = slateNode as Text;
        const parts = text.split(this.keyword);
        let offset = 0;
        let range: Range;

        parts.forEach((part, i) => {
            if (i !== 0) {
                const patchRange = {
                    anchor: { path: slatePath, offset: offset - this.keyword.length },
                    focus: { path: slatePath, offset }
                };
                const domPoint = AngularEditor.toDOMPoint(this.editor, patchRange.focus);
                const [endNode] = domPoint;
                const isEqual = endNode?.parentNode?.parentElement === element;

                if (isEqual) {
                    range = patchRange;
                }
            }
            offset = offset + part.length + this.keyword.length;
        });
        return range;
    }

    prev() {
        this.getSearchElements(SearchDirection.prev, true);
    }

    next() {
        this.getSearchElements(SearchDirection.next, true);
    }

    setHighlight(direction: SearchDirection = SearchDirection.init, isScroll: boolean = false) {
        if (direction === SearchDirection.next) {
            this.currentIndex = this.currentIndex === this.searchTotal ? 1 : this.currentIndex + 1;
        }
        if (direction === SearchDirection.prev) {
            this.currentIndex = this.currentIndex === 1 ? this.searchTotal : this.currentIndex - 1;
        }

        const currentElement = this.searchElements[this.currentIndex - 1];
        if (currentElement) {
            this.searchElements.forEach(element => element?.classList?.remove('the-search-highlight'));
            currentElement?.classList?.add('the-search-highlight');

            const bodyContent = currentElement.closest('.detail-body-content') as HTMLElement;
            if (isScroll && bodyContent) {
                ScrollToService.scrollToElement(currentElement as HTMLElement, bodyContent);
            }
        }
    }

    openPopover() {
        this.dropdownPopoverRef = this.thyPopover.open(this.searchReplaceTemplate, {
            origin: this.elementRef,
            panelClass: ['the-toolbar-dropdown-popover', this.toolbarItem?.key],
            placement: 'bottomLeft',
            insideClosable: false,
            backdropClosable: false,
            hasBackdrop: false,
            offset: 10,
            minWidth: 0,
            width: '400px',
            viewContainerRef: this.viewContainerRef,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });
    }

    closePopover() {
        if (this.dropdownPopoverRef) {
            this.keyword = '';
            this.replaceKeyword = '';
            this.currentIndex = 0;
            this.searchTotal = 0;
            this.searchElements = null;
            this.dropdownPopoverRef?.close();
            COMMON_SEARCH_REPLACE_KEYWORDS.set(this.editor, null);
            this.editingPageStore.updateDecorate();
        }
    }
}
