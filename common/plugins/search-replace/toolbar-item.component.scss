@use 'ngx-tethys/styles/variables.scss';
@use '@worktile/theia/styles/variables.scss' as theia;

.the-toolbar-dropdown-popover.search-replace {
    .thy-popover-container {
        width: 400px;
    }
    .form-group {
        .form-footer-actions {
            .btn + .btn {
                margin-left: 8px;
            }
        }
        &.between {
            .form-footer-actions {
                display: flex;
                justify-content: space-between;
                width: 100%;
            }
        }
    }
    .input-append {
        color: variables.$gray-600;
    }
}

.search-highlighting-leaf {
    background-color: #ffeeba;

    &.the-search-highlight {
        background: theia.$selection-background;
    }
}
