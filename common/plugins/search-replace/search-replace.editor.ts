import { Editor, NodeEntry, Text, Node, Element } from 'slate';
import { COMMON_SEARCH_REPLACE_KEYWORDS } from '@wiki/common/util/weak-map';

export const SearchReplaceEditor = {
    searchDecorate: (editor: Editor, nodeEntry: NodeEntry) => {
        if (!editor || !nodeEntry) {
            return;
        }
        let decorations = [];
        const [currentNode, nodePath] = nodeEntry;
        const keywords = COMMON_SEARCH_REPLACE_KEYWORDS.get(editor);

        if (keywords && Element.isElement(currentNode) && Editor.isBlock(editor, currentNode)) {
            for (const entry of Node.descendants(currentNode)) {
                const textNode = entry[0];
                const path = [...nodePath, ...entry[1]];
                if (Text.isText(textNode)) {
                    const { text } = textNode;
                    const parts = text.split(keywords);
                    let offset = 0;
                    parts.forEach((part, i) => {
                        if (i !== 0) {
                            decorations.push({
                                anchor: { path, offset: offset - keywords.length },
                                focus: { path, offset },
                                highlight: true
                            });
                        }
                        offset = offset + part.length + keywords.length;
                    });
                }
            }
        }

        return decorations;
    }
};
