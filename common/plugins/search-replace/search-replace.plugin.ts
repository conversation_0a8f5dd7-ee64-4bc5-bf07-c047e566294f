import { Text } from 'slate';
import { createPluginFactory, TheEditor } from '@worktile/theia';
import { WikiCommonSearchHighlightingLeafComponent } from './search-highlighting-leaf.component';
import { SearchHighlightKey } from './constants';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiCommonSearchReplaceToolbarItemComponent } from './toolbar-item.component';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withSearchReplace = (editor: TheEditor) => {
    const { renderLeaf } = editor;

    editor.renderLeaf = (text: Text) => {
        if (text[SearchHighlightKey]) {
            return WikiCommonSearchHighlightingLeafComponent;
        }

        return renderLeaf(text);
    };

    return editor;
};

export const createSearchReplacePlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.searchReplace,
        withOverrides: withSearchReplace,
        toolbarItems: [
            {
                key: WikiPluginTypes.searchReplace,
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.searchReplace'),
                icon: 'preview',
                iconComponent: WikiCommonSearchReplaceToolbarItemComponent
            }
        ]
    })();
