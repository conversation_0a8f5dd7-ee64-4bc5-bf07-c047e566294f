import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';
import { CommonRelationItemComponent } from '@wiki/common/components/relation-item/relation-item.component';
import { RelationTicketElement } from '@wiki/common/custom-types';
import { RelationItemEditor } from '@wiki/common/plugins/relation-item/relation-item.editor';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { verifyAvailableAppAndPermission } from '@wiki/common/util/common';
import { createPluginFactory, ElementKinds, TheEditor, ThePluginMenuItemType } from '@worktile/theia';
import { Element } from 'slate';

export const withRelationTicket = (editor: TheEditor) => {
    const { isVoid, isInline, renderElement, isBlockCard } = editor;

    editor.isInline = (element: RelationTicketElement) => {
        return RelationItemEditor.isRelationItem(element) && !RelationItemEditor.isCard(element) ? true : isInline(element);
    };

    editor.isVoid = (element: RelationTicketElement) => {
        return RelationItemEditor.isRelationItem(element) ? true : isVoid(element);
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.relationTicket) {
            return CommonRelationItemComponent;
        }
        return renderElement(element);
    };

    editor.isBlockCard = (element: RelationTicketElement) => {
        if (element.type === WikiPluginTypes.relationTicket && RelationItemEditor.isCard(element)) {
            return true;
        }
        return isBlockCard(element);
    };

    return editor;
};

export const relationTicketMenuItem = (translate: StyxTranslateService) => {
    return {
        key: WikiPluginTypes.relationTicket,
        keywords: `gongdan,gd,ticket,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.resource.workOrder')}`,
        execute: (editor: TheEditor) => RelationItemEditor.insertSuggestion(editor, WikiPluginTypes.relationTicket),
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.resource.workOrder'),
        type: ThePluginMenuItemType.group,
        menuIcon: WikiPluginMenuIcons.relationTicket,
        displayKey: '/gd',
        isDisabled: (editor: TheEditor) => {
            return !verifyAvailableAppAndPermission(translate, editor, WikiPluginTypes.relationTicket);
        },
        removeKeywordsType: 'character' as 'character'
    };
};

export const createRelationTicketPlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.relationTicket,
        withOverrides: withRelationTicket,
        menuItems: [relationTicketMenuItem(translate)],
        options: {
            allowParentTypes: [
                ElementKinds.tableCell,
                ElementKinds.blockquote,
                WikiPluginTypes.layoutColumn,
                WikiPluginTypes.toggleListItem,
                WikiPluginTypes.alert
            ]
        }
    })();
