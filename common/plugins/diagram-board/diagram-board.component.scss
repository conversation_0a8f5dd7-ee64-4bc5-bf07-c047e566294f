@use 'ngx-tethys/styles/variables.scss';

.slate-element-diagram-board {
    width: calc(100% - 2px);
    max-width: 100%;
    user-select: text;

    .common-plugin-card-element {
        width: 100%;
        min-height: 460px;
        height: 460px;

        .plait-toolbar {
            display: none;
        }
        &.slate-focus-element {
            .plait-toolbar {
                display: block;
            }
        }
    }

    &.wiki-fullscreen-node {
        .common-plugin-card-element {
            height: calc(100vh - 52px);
            .plait-text-container {
                .slate-editable-container {
                    overflow: initial !important;
                }
            }
        }
    }
}
