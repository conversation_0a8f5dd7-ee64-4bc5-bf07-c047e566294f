<div
  class="common-plugin-card-element"
  thyResizable
  [thyMinHeight]="minHeight"
  [thyBounds]="resizeBounds"
  [style.height.px]="resizeHeight"
  (thyResize)="onResize($event)"
  (thyResizeEnd)="onEndResize()"
>
  @if (isInitPlaitEditor) {
    <plait-editor
      class="border-radius-covering"
      [class.focused]="isCollapsedAndNonReadonly"
      [class.readonly]="readonly"
      [options]="plaitOptions"
      [value]="element?.data || []"
      [viewport]="element?.viewport"
      [theme]="element.theme"
      (editorInitialized)="editorInitialized($event)"
      (editorValueChange)="valueChange($event)"
      (editorMoveEnd)="setBoardNodes($event)"
      (editorResizeEnd)="setBoardNodes($event)"
    ></plait-editor>
  }

  @if (isCollapsedAndNonReadonly && !isFullscreen) {
    <thy-resize-handle thyDirection="bottom" class="the-resizable-handle-bottom"></thy-resize-handle>
  }
</div>

<ng-template #toolbar>
  <thy-actions contenteditable="false" thySize="xxs" thePreventDefault>
    <a
      href="javascript:;"
      thyAction
      thyActionIcon="arrows-alt"
      styxI18nTracking
      [thyTooltip]="'common.fullScreen' | translate"
      thyTooltipPlacement="top"
      (pointerup)="toggleFullscreen($event)"
    ></a>
    <a
      href="javascript:;"
      thyAction
      thyActionIcon="copy"
      styxI18nTracking
      [thyTooltip]="'common.copy' | translate"
      thyTooltipPlacement="top"
      (click)="onCopy($event)"
    ></a>
    @if (page?.permissions | hasPermission: 'page_export') {
      <a
        href="javascript:;"
        thyAction
        thyActionIcon="file-export"
        styxI18nTracking
        [thyTooltip]="'wiki.plugins.diagramBoard.exportImage' | translate"
        thyTooltipPlacement="top"
        (click)="downloadImage()"
      ></a>
    }
    <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      thyTooltipPlacement="top"
      (click)="onDelete($event)"
    ></a>
  </thy-actions>
</ng-template>
