import { hasSelectedElement, hasSelectedImageElement } from '@plait-editor/utils/board';
import { DiagramBoardElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { ThePluginMenuItemType, createPluginFactory, getPluginOptions } from '@worktile/theia';
import { isHotkey } from 'is-hotkey';
import { Editor, Element, Transforms } from 'slate';
import { AngularEditor, ELEMENT_TO_COMPONENT } from 'slate-angular';
import { DiagramBoardOptions } from './constants';
import { WikiCommonDiagramBoardComponent } from './diagram-board.component';
import { DiagramBoardEditor } from './diagram-board.editor';
import { hotkeys } from '@plait/core';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withDiagramBoard = (editor: Editor) => {
    const { isVoid, isBlockCard, renderElement, setFragmentData, insertData, deleteCutData, deleteBackward, onKeydown, onClick } = editor;

    editor.isVoid = (element: DiagramBoardElement) => {
        return element.type === WikiPluginTypes.diagramBoard ? true : isVoid(element);
    };

    editor.isBlockCard = (element: Element) => {
        return element.type === WikiPluginTypes.diagramBoard ? true : isBlockCard(element);
    };

    editor.renderElement = (element: DiagramBoardElement) => {
        if (element.type === WikiPluginTypes.diagramBoard) {
            return WikiCommonDiagramBoardComponent;
        }
        return renderElement(element);
    };

    editor.setFragmentData = (data, origin) => {
        const board = getBoard(editor);
        if (hasSelectedElement(board) || hasSelectedImageElement(board)) {
            return;
        }
        setFragmentData(data, origin);
    };

    editor.deleteCutData = () => {
        const board = getBoard(editor);
        if (hasSelectedElement(board) || hasSelectedImageElement(board)) {
            return;
        }
        // 全屏编辑状态下剪切处理：当没有选中任何节点时，禁用剪切功能 #WIK-11844
        const nodeEntry =
            editor.selection && Editor.above(editor, { match: e => Element.isElement(e) && e.type === WikiPluginTypes.diagramBoard });
        if (nodeEntry) {
            const diagramBoardComponent = ELEMENT_TO_COMPONENT.get(nodeEntry[0]) as unknown as WikiCommonDiagramBoardComponent;
            if (diagramBoardComponent.isFullscreen) {
                return;
            }
        }
        deleteCutData();
    };

    editor.deleteBackward = unit => {
        const board = getBoard(editor);
        if (hasSelectedElement(board) || hasSelectedImageElement(board)) {
            return;
        }
        const options = getPluginOptions(editor, CustomPluginKeys.diagramBoard);
        if (!options.showFullscreen) {
            return;
        }
        deleteBackward(unit);
    };

    editor.onKeydown = (event: KeyboardEvent) => {
        if (isDiagramBoard(editor) && (isHotkey('mod+z', event) || isHotkey('mod+shift+z', event) || isHotkey('mod+a', event))) {
            event.preventDefault();
            return;
        }
        const board = getBoard(editor);
        if (hasSelectedElement(board) && hotkeys.isArrow(event)) {
            event.preventDefault();
            return;
        }
        onKeydown(event);
    };

    editor.insertData = (data: DataTransfer) => {
        const board = getBoard(editor);
        if (hasSelectedElement(board) || isDiagramBoard(editor)) {
            return;
        }
        insertData(data);
    };

    editor.onClick = (event: MouseEvent) => {
        if (isDiagramBoard(editor)) {
            const board = getBoard(editor);
            if (hasSelectedElement(board) || hasSelectedImageElement(board)) {
                return;
            }
            // select spacer text when click diagram board
            const selection = editor.selection;
            AngularEditor.deselect(editor);
            Transforms.select(editor, selection);
            return;
        }
        onClick(event);
    };

    return editor;
};

export const getBoard = (editor: Editor) => {
    const nodeEntry =
        editor.selection && Editor.above(editor, { match: e => Element.isElement(e) && e.type === WikiPluginTypes.diagramBoard });
    if (nodeEntry) {
        const component = ELEMENT_TO_COMPONENT.get(nodeEntry[0]) as unknown as WikiCommonDiagramBoardComponent;
        return component.board;
    }
    return null;
};

export const isDiagramBoard = (editor: Editor) => {
    const nodeEntry =
        editor.selection && Editor.above(editor, { match: e => Element.isElement(e) && e.type === WikiPluginTypes.diagramBoard });
    return !!nodeEntry;
};

export const createDiagramBoardPlugin = (translate: StyxTranslateService) =>
    createPluginFactory<DiagramBoardOptions>({
        key: CustomPluginKeys.diagramBoard,
        withOverrides: withDiagramBoard,
        toolbarItems: [
            {
                key: WikiPluginTypes.diagramBoard,
                icon: 'mind-map',
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.mindmap'),
                execute: editor => DiagramBoardEditor.insert(editor, WikiPluginTypes.diagramBoard)
            },
            {
                key: WikiPluginTypes.board,
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.board'),
                execute: editor => DiagramBoardEditor.insert(editor, WikiPluginTypes.board)
            },
            {
                key: WikiPluginTypes.flowchart,
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.flowchart'),
                execute: editor => DiagramBoardEditor.insert(editor, WikiPluginTypes.flowchart)
            }
        ],
        menuItems: [
            {
                key: WikiPluginTypes.diagramBoard,
                keywords: `siweidaotu,swdt,mindmapping,brain mapping,brainmapping,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.mindmap')}`,
                execute: editor => DiagramBoardEditor.insert(editor, WikiPluginTypes.diagramBoard),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.mindmap'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.diagramBoard,
                displayKey: '/swdt'
            },
            {
                key: WikiPluginTypes.board,
                keywords: `huaban,hb,board,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.board')}`,
                execute: editor => DiagramBoardEditor.insert(editor, WikiPluginTypes.board),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.board'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.board,
                displayKey: '/hb'
            },
            {
                key: WikiPluginTypes.flowchart,
                keywords: `liuchengtu,lct,flowchart,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.flowchart')}`,
                execute: editor => DiagramBoardEditor.insert(editor, WikiPluginTypes.flowchart),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.flowchart'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.flowchart,
                displayKey: '/lct'
            }
        ],
        options: {
            allowParentTypes: [WikiPluginTypes.layoutColumn, WikiPluginTypes.toggleListItem],
            showFullscreen: true
        }
    })();
