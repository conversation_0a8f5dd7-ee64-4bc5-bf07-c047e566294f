import { PLViewportMode } from '@plait-editor/types/editor';
import { BaseViewport, PlaitElement, PlaitTheme, Transforms as PlaitTransforms } from '@plait/core';
import { MindLayoutType } from '@plait/layouts';
import { DiagramBoardElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { TheEditor, TheTransforms } from '@worktile/theia';
import { <PERSON><PERSON><PERSON>, Editor, Element, Node, Range, Transforms } from 'slate';
import { AngularEditor, ELEMENT_TO_COMPONENT } from 'slate-angular';
import { WikiCommonDiagramBoardComponent } from './diagram-board.component';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { createDefaultFlowchart, createDefaultMind } from './creation';

interface DiagramBoardAttribute {
    data: PlaitElement[];
    viewport: BaseViewport;
    height: number;
    theme: PlaitTheme;
    viewportMode?: PLViewportMode;
}

export const DiagramBoardEditor = {
    insert(editor: Editor, type: WikiPluginTypes) {
        const translate = editor.injector.get(StyxTranslateService);
        if (Range.isCollapsed(editor.selection)) {
            let defaultData: PlaitElement[];
            switch (type) {
                case WikiPluginTypes.diagramBoard:
                    defaultData = [createDefaultMind(translate, [230, 208], 4, MindLayoutType.right)];
                    break;
                case WikiPluginTypes.flowchart:
                    defaultData = createDefaultFlowchart(translate, [230, 208]);
                    break;
                default:
                    defaultData = [];
                    break;
            }
            const e: DiagramBoardElement = {
                type: WikiPluginTypes.diagramBoard,
                data: defaultData,
                children: [{ text: '' }]
            };
            TheTransforms.insertElements(editor, e);

            const node = Node.parent(editor, editor.selection.anchor.path);
            setTimeout(() => {
                if (type === WikiPluginTypes.diagramBoard) {
                    DiagramBoardEditor.selectRootNode(node, e);
                } else {
                    const boardComponent = ELEMENT_TO_COMPONENT.get(node) as WikiCommonDiagramBoardComponent;
                    PlaitTransforms.setSelection(boardComponent.plaitEditor.board, { anchor: [0, 0], focus: [0, 0] });
                }
            }, 180);
        }
    },
    setAttribute(editor: Editor, element: DiagramBoardElement, data: Partial<DiagramBoardAttribute>) {
        Transforms.setNodes(editor, data, {
            at: AngularEditor.findPath(editor, element)
        });
    },
    removeNode(editor: Editor, element: Element) {
        Transforms.move(editor, { reverse: true });

        const selection = editor.selection;
        const path = TheEditor.findPath(editor, element);

        Transforms.removeNodes(editor, { at: path });
        Transforms.select(editor, selection);
        TheEditor.focus(editor);
    },
    selectRootNode(node: Ancestor, element: DiagramBoardElement) {
        const diagramBoardComponent = ELEMENT_TO_COMPONENT.get(node) as unknown as WikiCommonDiagramBoardComponent;
        const rootPoint: [number, number] = [
            element.data[0].points[0][0] + element.data[0].width / 2,
            element.data[0].points[0][1] + element.data[0].height / 2
        ];
        PlaitTransforms.setSelection(diagramBoardComponent.plaitEditor.board, { anchor: rootPoint, focus: rootPoint });
    }
};
