import { Overlay } from '@angular/cdk/overlay';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    HostBinding,
    NgZone,
    OnDestroy,
    OnInit,
    TemplateRef,
    ViewChild,
    ViewContainerRef,
    inject
} from '@angular/core';
import { DateFormatPipe, css } from '@atinc/ngx-styx';
import { PlaitEditorComponent } from '@plait-editor/editor/editor.component';
import { SettingPanelService } from '@plait-editor/services/setting-panel.service';
import { PLOptions, PLViewportMode } from '@plait-editor/types/editor';
import { OnChangeData } from '@plait/angular-board';
import { TinyDate } from 'ngx-tethys/util';
import {
    BoardTransforms,
    FitViewportOptions,
    PlaitBoard,
    PlaitElement,
    Viewport,
    debounce,
    depthFirstRecursion,
    isFromScrolling,
    isMovingElements
} from '@plait/core';
import { MindElement } from '@plait/mind';
import { BOARD_EXPORT_CONFIG, DEFAULT_BLOCK_CARD_HEIGHT } from '@wiki/common/constants/page';
import { DiagramBoardElement } from '@wiki/common/custom-types';
import { PageStore } from '@wiki/common/stores/page.store';
import { CustomPluginKeys } from '@wiki/common/types/plugins.types';
import { base64ToBlob, boardToImage } from '@wiki/common/util/board';
import { download } from '@wiki/common/util/download';
import { isMobileMode } from '@wiki/common/util/extension-mode';
import {
    TheBaseElement,
    TheContextService,
    TheEditor,
    TheModeType,
    TheQueries,
    copyNode,
    getMode,
    getPluginOptions,
    isPrintMode,
    topLeftPosition,
    updatePopoverPosition
} from '@worktile/theia';
import { ThyNotifyService } from 'ngx-tethys/notify';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { ThyResizeEvent } from 'ngx-tethys/resizable';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
import { Element, Transforms } from 'slate';
import { FullscreenService, FullscreenState } from '../../services/fullscreen.service';
import { DiagramBoardOptions } from './constants';
import { DiagramBoardEditor } from './diagram-board.editor';
import { isResizing } from '@plait/common';

@Component({
    selector: 'wiki-common-diagram-board',
    templateUrl: './diagram-board.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiCommonDiagramBoardComponent
    extends TheBaseElement<DiagramBoardElement, TheEditor>
    implements OnInit, AfterViewInit, OnDestroy
{
    toolbarPopoverRef: ThyPopoverRef<any>;

    options: DiagramBoardOptions;

    plaitOptions: PLOptions = {
        readonly: false,
        hideScrollbar: true,
        disabledScrollOnNonFocus: true,
        mode: 'embed'
    };

    isFullscreen = false;

    resizeBounds = null;

    resizeHeight: number;

    minHeight = 460;

    isInitPlaitEditor = false;

    private fullscreenViewport: Viewport;

    private embeddedViewport: Viewport;

    private elementHeight: number;

    private fullscreenSubscription: Subscription;

    private pageInfoSubscription: Subscription;

    private viewportModeSubscription: Subscription;

    board: PlaitBoard;

    mode: TheModeType;

    isMobileMode: boolean;

    get isFocused() {
        return this.plaitEditor?.board?.selection;
    }

    get isToolbarOpen() {
        return this.toolbarPopoverRef && this.toolbarPopoverRef.getOverlayRef() && this.toolbarPopoverRef.getOverlayRef().hasAttached();
    }

    get page() {
        return this.pageStore.snapshot.page;
    }

    @HostBinding('class') className = `${css.layout}`;

    @ViewChild(PlaitEditorComponent)
    plaitEditor!: PlaitEditorComponent;

    @ViewChild('toolbar', { read: TemplateRef, static: true })
    toolbar: TemplateRef<any>;

    public pageStore = inject(PageStore);
    public viewContainerRef = inject(ViewContainerRef);
    private ngZone = inject(NgZone);
    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);
    private contextService = inject(TheContextService);
    private fullscreenService = inject(FullscreenService);
    private thyNotifyService = inject(ThyNotifyService);
    private settingPanelService = inject(SettingPanelService);

    ngOnInit() {
        super.ngOnInit();
        this.options = getPluginOptions(this.editor, CustomPluginKeys.diagramBoard);
        this.fullscreenSubscription = this.fullscreenService.subscribe(this.element, value => {
            this.fullscreenHandler(value.state);
        });
        this.mindDataProcessing();
    }

    ngAfterViewInit() {
        this.resizeHeight = this.element?.height || this.minHeight;
        if (!this.readonly) {
            // edit mode can not delay
            this.ngZone.onStable.pipe(take(1)).subscribe(() => {
                this.resizeBounds = {
                    nativeElement: this.contextService.getEditableElement()
                };
                this.cdr.markForCheck();
            });
            this.subscribeViewportModeChange();
        }

        setTimeout(
            () => {
                this.isInitPlaitEditor = true;
                this.plaitOptions = {
                    ...this.plaitOptions,
                    readonly: this.isMobileMode ? true : this.plaitOptions.readonly,
                    viewportMode: this.element.viewportMode || 'autoFit'
                };
                this.cdr.markForCheck();
            },
            this.readonly ? 0 : 150
        );
    }

    onContextChange() {
        super.onContextChange();
        if (!this.mode) {
            this.mode = getMode(this.editor);
            this.isMobileMode = isMobileMode(this.mode);
        }
        this.plaitOptions = {
            ...this.plaitOptions,
            readonly: this.isMobileMode ? true : this.readonly
        };
        if (!this.isFullscreen && !this.isMobileMode) {
            this.isCollapsedAndNonReadonly ? this.openToolbar() : this.closeToolbar();
        }

        setTimeout(() => {
            this.settingPanelService?.updateNodeProperties();
        }, 150);
    }

    ngOnDestroy() {
        super.ngOnDestroy();
        this.fullscreenSubscription?.unsubscribe();
        this.pageInfoSubscription?.unsubscribe();
        this.viewportModeSubscription?.unsubscribe();
    }

    fullscreenHandler(state: FullscreenState) {
        const isFullscreen = state === FullscreenState.on;
        if (isFullscreen) {
            this.embeddedViewport = this.element.viewport;
        }
        this.plaitOptions = {
            ...this.plaitOptions,
            hideScrollbar: !isFullscreen,
            mode: isFullscreen ? 'default' : 'embed'
        };
        this.plaitEditor.options = this.plaitOptions;
        this.plaitEditor.setEditorRect();
        this.isFullscreen = isFullscreen;
        this.isCollapsedAndNonReadonly && this.openToolbar();

        if (isFullscreen) {
            const pluginCard = this.elementRef.nativeElement.querySelector('.common-plugin-card-element') as HTMLElement;
            this.elementHeight = pluginCard.getBoundingClientRect().height;
            this.resizeHeight = null;
        } else {
            this.resizeHeight = this.elementHeight;
            this.elementHeight = null;
            this.settingPanelService.closeSettingPanel();
        }
        this.cdr.markForCheck();
        if (isFullscreen && !this.fullscreenViewport) {
            // 如果不加 setTimeout 视觉上会出现全屏已经关闭，然后节点再移动。
            setTimeout(() => {
                BoardTransforms.fitViewport(this.board);
            });
            return;
        }
        const viewport = isFullscreen ? this.fullscreenViewport : this.embeddedViewport;
        const { zoom, origination } = viewport;
        // 等待 ResizeObserver 先执行
        // 如果 resize observer 不先执行：viewportContainer 的 width、height 是不正确的，会导致后续的状态更新不正确
        setTimeout(() => {
            BoardTransforms.updateViewport(this.board, origination, zoom);
        }, 0);
    }

    editorInitialized(board: PlaitBoard) {
        this.board = board;
        const options: FitViewportOptions = {
            limitHeight: DEFAULT_BLOCK_CARD_HEIGHT,
            containerClass: 'common-plugin-card-element',
            autoFitPadding: BOARD_EXPORT_CONFIG.autoFitPadding
        };
        // 打印模式下，基于宽度自适应高度
        if (this.readonly && isPrintMode(this.editor)) {
            options.maxWidth = BOARD_EXPORT_CONFIG.a4Width;
            BoardTransforms.fitViewportWidth(this.board, options);
            return;
        }
        // viewport 不存在时，适应画布
        if (!this.element.viewport || !this.element.viewport.origination) {
            BoardTransforms.fitViewport(this.board);
            return;
        }
        // viewportMode 不为 useEditSetting 时，基于宽度自适应高度
        if (this.readonly && this.element?.viewportMode !== 'useEditSetting') {
            BoardTransforms.fitViewportWidth(this.board, options);
        }
    }

    debounceValueChange = debounce(() => {
        const { viewport } = this.board;
        DiagramBoardEditor.setAttribute(this.editor, this.element, {
            viewport: !this.isFullscreen ? viewport : this.element.viewport
        });
    }, 500);

    valueChange(value: OnChangeData) {
        if (this.isFullscreen) {
            this.fullscreenViewport = value.viewport;
        }

        if (this.isCollapsedAndNonReadonly && isFromScrolling(this.board)) {
            this.debounceValueChange();
            return;
        }

        if (!isMovingElements(this.board) && !isResizing(this.board)) {
            this.setBoardNodes(value);
        }
    }

    setBoardNodes(value: OnChangeData) {
        DiagramBoardEditor.setAttribute(this.editor, this.element, {
            data: value.children,
            viewport: !this.isFullscreen ? value.viewport : this.element.viewport,
            theme: value.theme
        });
    }

    openToolbar() {
        if (!TheQueries.isGlobalCollapsed(this.editor) || this.isToolbarOpen) {
            return;
        }

        const origin = this.elementRef.nativeElement as HTMLElement;
        this.toolbarPopoverRef = this.thyPopover.open(this.toolbar, {
            origin,
            viewContainerRef: this.viewContainerRef,
            minWidth: 0,
            panelClass: 'the-plugin-toolbar-popover',
            hasBackdrop: false,
            insideClosable: false,
            outsideClosable: false,
            manualClosure: true,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });

        const overlayRef = this.toolbarPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    closeToolbar() {
        if (this.isToolbarOpen) {
            this.toolbarPopoverRef.close();
        }
    }

    onCopy(e: Event) {
        copyNode(this.editor, this.element, this.thyNotifyService);
    }

    toggleFullscreen(e: MouseEvent) {
        // board.globalPointerUp 导致失去焦点，所以需要更改成 pointerup 然后阻止事件
        e.preventDefault();
        e.stopPropagation();
        this.closeToolbar();
        this.fullscreenService.setFullscreen(this.editor, e, this.element, true);
    }

    onResize({ height }: ThyResizeEvent) {
        this.resizeHeight = height as number;
    }

    onEndResize() {
        Transforms.select(this.editor, TheEditor.findPath(this.editor, this.element));
        DiagramBoardEditor.setAttribute(this.editor, this.element, { height: this.resizeHeight });
    }

    onDelete(event: Event) {
        event.preventDefault();
        DiagramBoardEditor.removeNode(this.editor, this.element);
    }

    mindDataProcessing() {
        const nodes: { children: PlaitElement[]; height?: number; isRoot?: boolean } = { children: this.element.data };
        depthFirstRecursion(nodes, node => {
            const oldNode = node as MindElement & { value: Element };
            if (!oldNode.data && oldNode.value) {
                oldNode.data = { topic: oldNode.value };
            }
        });
    }

    private subscribeViewportModeChange() {
        if (!this.readonly) {
            this.viewportModeSubscription = this.settingPanelService.subscribeViewportModeChange((mode: PLViewportMode) => {
                Transforms.select(this.editor, TheEditor.findPath(this.editor, this.element));
                DiagramBoardEditor.setAttribute(this.editor, this.element, { viewportMode: mode });
            });
        }
    }

    downloadImage(e: Event) {
        boardToImage(this.board).then(image => {
            const pngImage = base64ToBlob(image);
            const dateTimeAutoFormat = new DateFormatPipe();
            const date = dateTimeAutoFormat.transform(new TinyDate().getTime(), 'yyyyMMdd');
            const imageName = `${this.page.name}-图片-${date}.png`;
            download(pngImage, imageName);
        });
    }
}
