import { StyxTranslateService } from '@atinc/ngx-styx';
import { buildText, DEFAULT_FONT_FAMILY, measureElement } from '@plait/common';
import { PlaitBoard, Point } from '@plait/core';
import {
    ArrowLineMarkerType,
    ArrowLineShape,
    createArrowLineElement,
    createGeometryElement,
    DefaultBasicShapeProperty,
    DefaultLineStyle,
    FlowchartSymbols,
    getDefaultFlowchartProperty,
    getDefaultGeometryPoints
} from '@plait/draw';
import { MindLayoutType } from '@plait/layouts';
import { createMindElement, getTopicSize } from '@plait/mind';
import { DEFAULT_FONT_SIZE } from '@plait/text-plugins';
import { Element } from 'slate';

export const createDefaultMind = (translate: StyxTranslateService, point: Point, rightNodeCount: number, layout: MindLayoutType) => {
    const text = translate.instant('styx.mindMap');
    const topicSize = getTopicSize(true, false, buildText(text));
    const root = createMindElement(text, topicSize.width, topicSize.height, { layout });
    root.rightNodeCount = rightNodeCount;
    root.isRoot = true;
    root.type = 'mindmap';
    root.points = [point];
    const branchText = translate.instant('wiki.plugin.newNode');
    const branchTopicSize = getTopicSize(false, true, buildText(branchText));
    const children = [1, 1, 1].map(() => {
        return createMindElement(branchText, branchTopicSize.width, branchTopicSize.height, {});
    });
    root.children = children;
    return root;
};

export const createDefaultFlowchart = (translate: StyxTranslateService, point: Point) => {
    const decisionProperty = getDefaultFlowchartProperty(FlowchartSymbols.decision);
    const processProperty = getDefaultFlowchartProperty(FlowchartSymbols.process);
    const terminalProperty = getDefaultFlowchartProperty(FlowchartSymbols.terminal);

    const options = {
        strokeWidth: DefaultBasicShapeProperty.strokeWidth
    };

    const startNodeText = translate.instant('common.start');
    const endNodeText = translate.instant('wiki.plugin.end');
    const processNodeText = translate.instant('wiki.plugin.process');
    const decisionNodeText = translate.instant('wiki.plugin.decision');
    const yesText = translate.instant('wiki.plugin.yes');
    const noText = translate.instant('wiki.plugin.no');

    const lineOptions = {
        strokeWidth: DefaultLineStyle.strokeWidth
    };
    const startElement = createGeometryElement(
        FlowchartSymbols.terminal,
        getDefaultGeometryPoints(FlowchartSymbols.terminal, point),
        startNodeText,
        options
    );

    const processPoint1: Point = [point[0], point[1] + terminalProperty.height / 2 + 55 + processProperty.height / 2];
    const processElement1 = createGeometryElement(
        FlowchartSymbols.process,
        getDefaultGeometryPoints(FlowchartSymbols.process, processPoint1),
        processNodeText,
        options
    );

    const decisionPoint: Point = [processPoint1[0], processPoint1[1] + processProperty.height / 2 + 55 + decisionProperty.height / 2];
    const decisionElement = createGeometryElement(
        FlowchartSymbols.decision,
        getDefaultGeometryPoints(FlowchartSymbols.decision, decisionPoint),
        decisionNodeText,
        options
    );

    const processPoint2: Point = [decisionPoint[0] + decisionProperty.width / 2 + 75 + processProperty.width / 2, decisionPoint[1]];
    const processElement2 = createGeometryElement(
        FlowchartSymbols.process,
        getDefaultGeometryPoints(FlowchartSymbols.process, processPoint2),
        processNodeText,
        options
    );

    const endPoint: Point = [decisionPoint[0], decisionPoint[1] + decisionProperty.height / 2 + 95 + terminalProperty.height / 2];
    const endElement = createGeometryElement(
        FlowchartSymbols.terminal,
        getDefaultGeometryPoints(FlowchartSymbols.terminal, endPoint),
        endNodeText,
        options
    );

    const line1 = createArrowLineElement(
        ArrowLineShape.elbow,
        [
            [0, 0],
            [0, 0]
        ],
        { marker: ArrowLineMarkerType.none, connection: [0.5, 1], boundId: startElement.id },
        { marker: ArrowLineMarkerType.arrow, connection: [0.5, 0], boundId: processElement1.id },
        [],
        lineOptions
    );

    const line2 = createArrowLineElement(
        ArrowLineShape.elbow,
        [
            [0, 0],
            [0, 0]
        ],
        { marker: ArrowLineMarkerType.none, connection: [0.5, 1], boundId: processElement1.id },
        { marker: ArrowLineMarkerType.arrow, connection: [0.5, 0], boundId: decisionElement.id },
        [],
        lineOptions
    );

    const yesElement = buildText(yesText) as Element;
    const yesSize = measureElement(yesElement, {
        fontSize: DEFAULT_FONT_SIZE,
        fontFamily: DEFAULT_FONT_FAMILY
    });

    const line3 = createArrowLineElement(
        ArrowLineShape.elbow,
        [
            [0, 0],
            [0, 0]
        ],
        { marker: ArrowLineMarkerType.none, connection: [0.5, 1], boundId: decisionElement.id },
        { marker: ArrowLineMarkerType.arrow, connection: [0.5, 0], boundId: endElement.id },
        [
            {
                text: yesElement,
                position: 0.5,
                width: yesSize.width,
                height: yesSize.height
            }
        ],
        lineOptions
    );

    const noElement = buildText(noText) as Element;
    const noSize = measureElement(noElement, {
        fontSize: DEFAULT_FONT_SIZE,
        fontFamily: DEFAULT_FONT_FAMILY
    });

    const line4 = createArrowLineElement(
        ArrowLineShape.elbow,
        [
            [0, 0],
            [0, 0]
        ],
        { marker: ArrowLineMarkerType.none, connection: [1, 0.5], boundId: decisionElement.id },
        { marker: ArrowLineMarkerType.arrow, connection: [0, 0.5], boundId: processElement2.id },
        [
            {
                text: noElement,
                position: 0.5,
                width: noSize.width,
                height: noSize.height
            }
        ],
        lineOptions
    );

    const line5 = createArrowLineElement(
        ArrowLineShape.elbow,
        [
            [0, 0],
            [0, 0]
        ],
        { marker: ArrowLineMarkerType.none, connection: [0.5, 1], boundId: processElement2.id },
        { marker: ArrowLineMarkerType.arrow, connection: [1, 0.5], boundId: endElement.id },
        [],
        lineOptions
    );

    return [startElement, processElement1, decisionElement, processElement2, endElement, line1, line2, line3, line4, line5];
};
