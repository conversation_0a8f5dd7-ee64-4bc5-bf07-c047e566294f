import { WikiCardElementTypes } from '@wiki/common/custom-types';
import { TheEditor, TheTransforms } from '@worktile/theia';
import { Editor, Transforms } from 'slate';

export const WikiCardEditor = {
    switchCard(editor: Editor, element: WikiCardElementTypes) {
        const targetPath = TheEditor.findPath(editor, element);
        Transforms.removeNodes(editor, { at: targetPath });
        const fileElement = {
            type: element.type,
            mode: 'card',
            _id: element._id,
            children: [
                {
                    text: ''
                }
            ]
        } as WikiCardElementTypes;
        TheTransforms.insertElements(editor, fileElement);
    }
};
