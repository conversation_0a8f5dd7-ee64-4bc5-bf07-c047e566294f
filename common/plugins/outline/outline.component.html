@if (headerList?.length > 0) {
  <div
    class="wiki-common-outline-resizable common-plugin-card-element w-100"
    contenteditable="false"
    thyResizable
    [thyMinHeight]="52"
    [thyBounds]="resizeBounds"
    [style.height.px]="resizeHeight"
    (thyResize)="onResize($event)"
    (thyResizeEnd)="onEndResize()"
  >
    <thy-anchor
      class="outline-anchor border-radius-covering w-100"
      [thyAffix]="false"
      [thyContainer]="container"
      [style.max-height]="maxHeight"
    >
      @if (element.mode === outlineMode.tree) {
        @for (header of outlineTree; track $index; let index = $index) {
          <ng-template [ngTemplateOutlet]="anchorLink" [ngTemplateOutletContext]="{ $implicit: header, header: header }"></ng-template>
        }
        <ng-template #anchorLink let-header="header">
          <thy-anchor-link
            thyHref="{{ '#' + header.key }}"
            [attr.level]="header.level"
            [ngClass]="{
              expand: headersExpandMap[header.key]?.expand,
              'parent-item': !header.parentKey
            }"
          >
            @if (header.children?.length) {
              @for (node of header.children; track $index; let childIndex = $index) {
                <ng-template
                  [ngTemplateOutlet]="anchorLink"
                  [ngTemplateOutletContext]="{
                    $implicit: node,
                    header: node
                  }"
                >
                </ng-template>
              }
            }
            <ng-template #thyTemplate>
              @if (noChild) {
                <thy-icon
                  class="mr-2 expand-button"
                  [style.visibility]="element.level > outlineTreeLevel.one && header.children?.length ? 'visible' : 'hidden'"
                  [thyIconName]="headersExpandMap[header.key]?.expand ? 'angle-down' : 'angle-right'"
                  (click)="changeExpand($event, header, childIndex)"
                >
                </thy-icon>
              }

              <span class="link-title-text">
                @if (element.numbered) {
                  <span class="mr-1">{{ header.numbered }}</span>
                }

                {{ header.text }}</span
              >
            </ng-template>
          </thy-anchor-link>
        </ng-template>
      }

      @if (element.mode === outlineMode.tiled) {
        <div class="outline-tiled px-2 py-1">
          @for (header of headerList; track $index; let index = $index) {
            <thy-anchor-link class="thy-anchor-link-tiled d-inline-block px-1" thyHref="{{ '#' + header.key }}" [attr.level]="header.level">
              <ng-template #thyTemplate>
                <span class="thy-anchor-link-tiled-text">
                  [
                  <span class="link-title-text">
                    @if (element.numbered) {
                      <span>{{ header.numbered }}</span>
                    }
                    {{ header.text }}
                  </span>
                  ]
                </span>
              </ng-template>
            </thy-anchor-link>
          }
        </div>
      }
    </thy-anchor>

    @if (isCollapsedAndNonReadonly) {
      <thy-resize-handle thyDirection="bottom" class="outline-resize-handle"></thy-resize-handle>
    }
  </div>
} @else {
  <common-empty-content iconName="wiki-outline" styxI18nTracking [text]="'common.noResult' | translate"></common-empty-content>
}
