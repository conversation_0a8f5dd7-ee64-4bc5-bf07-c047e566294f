<thy-actions contenteditable="false" thySize="xxs">
  @for (outlineMode of outlineModes(); track $index) {
    <a
      thyAction
      [thyActionIcon]="outlineMode.icon"
      thyActiveClass="active"
      [thyTooltip]="outlineMode.tooltip"
      [thyActionActive]="outlineMode.mode === outlineEntity.mode"
      href="javascript:;"
      (click)="outlineModeChange(outlineMode)"
    >
    </a>
  }
  <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>

  <a #optionTemplate thyAction thyActiveClass="active" href="javascript:;" (click)="openOptionMenu($event)" styxI18nTracking>
    {{ 'wiki.plugins.outline.options' | translate }} <thy-icon thyIconName="caret-down" class="font-size-sm text-desc ml-1"></thy-icon
  ></a>

  <a
    #treeLevelTemplate
    thyAction
    thyActiveClass="active"
    styxI18nTracking
    [thyTooltip]="'wiki.plugins.outline.levelTitle' | translate"
    href="javascript:;"
    (click)="openTreeLevelMenu($event)"
  >
    {{ 'wiki.space.settings.directory.level' | translate }}
    <thy-icon thyIconName="caret-down" class="font-size-sm text-desc ml-1"></thy-icon
  ></a>

  <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
  <a
    href="javascript:;"
    thyAction
    thyType="danger"
    thyActionIcon="trash"
    styxI18nTracking
    [thyTooltip]="'common.delete' | translate"
    thyTooltipPlacement="top"
    (mousedown)="removeNode($event)"
  ></a>
</thy-actions>

<ng-template #dropdownOptionMenuTemplate>
  <div class="thy-dropdown-menu">
    @for (menu of optionMenus(); track $index) {
      <a
        thyDropdownMenuItem
        href="javascript:;"
        [thyDropdownMenuItemActive]="outlineEntity.numbered"
        (mousedown)="preventDefault($event)"
        (click)="optionChange(outlineEntity.numbered)"
      >
        <span thyDropdownMenuItemName>{{ menu.name }}</span>
      </a>
    }
  </div>
</ng-template>

<ng-template #dropdownTreeLevelMenuTemplate>
  <div class="thy-dropdown-menu" style.width="100%">
    @for (menu of treeLevelMenus(); track $index) {
      <a
        thyDropdownMenuItem
        href="javascript:;"
        [thyDropdownMenuItemActive]="menu.value === outlineEntity.level"
        (mousedown)="preventDefault($event)"
        (click)="treeLevelChange(menu.value)"
      >
        <span thyDropdownMenuItemName>{{ menu.name }}</span>
      </a>
    }
  </div>
</ng-template>
