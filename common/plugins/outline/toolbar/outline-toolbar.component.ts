import { Overlay } from '@angular/cdk/overlay';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    inject,
    Input,
    OnDestroy,
    OnInit,
    TemplateRef,
    ViewChild,
    ViewContainerRef
} from '@angular/core';
import { bottomLeftPosition, updatePopoverPosition } from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { Editor } from 'slate';
import { OutlineElement } from '../../../custom-types';
import { OutlineEditor } from '../../outline/outline.editor';
import { OutlineEntity } from '../outline.component';
import { OutlineModeType, OutlineTreeLevel, getOutlineModes, getOutlineOptionMenus, getOutlineTreeLevelMenus } from '../outline.types';
import { StyxTranslateService } from '@atinc/ngx-styx';

@Component({
    selector: 'wiki-common-outline-toolbar',
    templateUrl: './outline-toolbar.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiCommonOutlineToolbarComponent implements OnInit, OnDestroy {
    translate = inject(StyxTranslateService);

    @Input() editor: Editor;

    @Input() element: OutlineElement;

    @Input() set entity(v: OutlineEntity) {
        this.outlineEntity = v;
    }

    protected outlineEntity: OutlineEntity;

    protected outlineModes = computed(() => getOutlineModes(this.translate));

    protected optionMenus = computed(() => getOutlineOptionMenus(this.translate));

    protected treeLevelMenus = computed(() => getOutlineTreeLevelMenus(this.translate));

    private dropdownPopoverRef: ThyPopoverRef<any>;

    @ViewChild('optionTemplate') optionTemplate: TemplateRef<any>;

    @ViewChild('treeLevelTemplate') treeLevelTemplate: TemplateRef<any>;

    @ViewChild('dropdownOptionMenuTemplate', { static: true }) dropdownOptionMenu: TemplateRef<any>;

    @ViewChild('dropdownTreeLevelMenuTemplate', { static: true }) dropdownTreeLevelMenu: TemplateRef<any>;

    constructor(
        private overlay: Overlay,
        private thyPopover: ThyPopover,
        private viewContainerRef: ViewContainerRef
    ) {}

    ngOnInit(): void {}

    removeNode(event: Event) {
        this.preventDefault(event);
        OutlineEditor.removeNode(this.editor, this.element);
    }

    outlineModeChange(value: OutlineModeType) {
        this.outlineEntity.mode = value.mode;
        OutlineEditor.setAttribute(this.editor, this.element, { mode: value.mode });
    }

    openOptionMenu(event: Event) {
        this.openDropdownMenu(event, this.dropdownOptionMenu, this.optionTemplate);
    }

    openTreeLevelMenu(event: Event) {
        this.openDropdownMenu(event, this.dropdownTreeLevelMenu, this.treeLevelTemplate);
    }

    openDropdownMenu(event: Event, templateMenu: TemplateRef<any>, template: TemplateRef<any>) {
        const origin = event.currentTarget as HTMLElement;
        this.dropdownPopoverRef = this.thyPopover.open(templateMenu, {
            origin,
            originActiveClass: 'active',
            placement: 'bottom',
            autoAdaptive: false,
            insideClosable: true,
            outsideClosable: true,
            backdropClosable: true,
            hasBackdrop: false,
            offset: 8,
            viewContainerRef: this.viewContainerRef,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });
        const overlayRef = this.dropdownPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, template.elementRef.nativeElement, [bottomLeftPosition]);
    }

    optionChange(numbered: boolean) {
        this.outlineEntity.numbered = !numbered;
        OutlineEditor.setAttribute(this.editor, this.element, { numbered: !numbered });
    }

    treeLevelChange(level: OutlineTreeLevel) {
        this.outlineEntity.level = level;
        OutlineEditor.setAttribute(this.editor, this.element, { level });
    }

    ngOnDestroy(): void {
        this.dropdownPopoverRef = null;
    }

    preventDefault(event: Event) {
        event.stopPropagation();
        event.preventDefault();
    }
}
