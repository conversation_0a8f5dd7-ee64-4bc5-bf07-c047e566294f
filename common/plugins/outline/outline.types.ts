import { StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

export enum OutlineMode {
    tree = 'tree',
    tiled = 'tiled'
}

export type OutlineModeType = {
    icon: string;
    mode: OutlineMode;
    tooltip: string;
};

export enum OutlineTreeLevel {
    one = 1,
    two = 2,
    three = 3,
    four = 4
}

export const getOutlineModes = (translate: StyxTranslateService) => {
    return [
        {
            icon: 'sitemap',
            mode: OutlineMode.tree,
            tooltip: translate.instant<I18nSourceDefinitionType>('wiki.plugins.outline.list')
        },
        {
            icon: 'view-tile',
            mode: OutlineMode.tiled,
            tooltip: translate.instant<I18nSourceDefinitionType>('styx.flat')
        }
    ];
};

export const getOutlineOptionMenus = (translate: StyxTranslateService) => {
    return [{ name: translate.instant<I18nSourceDefinitionType>('wiki.plugins.outline.showNodes') }];
};

export const getOutlineTreeLevelMenus = (translate: StyxTranslateService) => {
    return [
        { name: translate.instant<I18nSourceDefinitionType>('wiki.plugins.outline.level1'), value: OutlineTreeLevel.one },
        { name: translate.instant<I18nSourceDefinitionType>('wiki.plugins.outline.level2'), value: OutlineTreeLevel.two },
        { name: translate.instant<I18nSourceDefinitionType>('wiki.plugins.outline.level3'), value: OutlineTreeLevel.three },
        { name: translate.instant<I18nSourceDefinitionType>('wiki.plugins.outline.level4'), value: OutlineTreeLevel.four }
    ];
};
