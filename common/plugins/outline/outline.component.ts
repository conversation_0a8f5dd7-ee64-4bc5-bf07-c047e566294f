import { Overlay } from '@angular/cdk/overlay';
import { Component, HostBinding, OnDestroy, OnInit, ViewContainerRef, inject } from '@angular/core';
import { helpers } from '@atinc/ngx-styx';
import { isMobileMode } from '@wiki/common/util/extension-mode';
import { TheBaseElement, TheContextService, TheQueries, getMode, topLeftPosition, updatePopoverPosition } from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { ThyResizeEvent } from 'ngx-tethys/resizable';
import { isUndefinedOrNull } from 'ngx-tethys/util';
import { Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { OutlineElement, TheDocument } from '../../custom-types';
import { HeadingType, extractHeading } from '../../util/extract-heading';
import { OutlineEditor } from './outline.editor';
import { OutlineMode, OutlineTreeLevel } from './outline.types';
import { WikiCommonOutlineToolbarComponent } from './toolbar/outline-toolbar.component';

export interface OutlineEntity {
    numbered: boolean;
    mode: OutlineMode;
    level: OutlineTreeLevel;
}

@Component({
    selector: 'wiki-common-outline, [theWikiCommonOutline]',
    templateUrl: './outline.component.html',
    standalone: false
})
export class WikiCommonOutlineComponent extends TheBaseElement<OutlineElement> implements OnInit, OnDestroy {
    content: TheDocument;

    unbindingContentChange: () => void;

    @HostBinding('class.empty') get empty() {
        return this.headerList?.length <= 0;
    }

    headerList: HeadingType[];

    outlineTree: HeadingType[];

    protected outlineTreeLevel = OutlineTreeLevel;

    protected outlineMode = OutlineMode;

    anchorPopoverRef: ThyPopoverRef<any>;

    resizeBounds = null;

    resizeHeight: number;

    headersExpandMap: { [key: string]: { expand: boolean } } = {};

    noChild: boolean = false;

    container: HTMLElement;

    public get maxHeight(): string {
        return this.element.height || this.resizeHeight ? 'inherit' : '320px';
    }

    get isToolbarOpen() {
        return this.anchorPopoverRef && this.anchorPopoverRef.getOverlayRef() && this.anchorPopoverRef.getOverlayRef().hasAttached();
    }

    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);
    private contextService = inject(TheContextService);
    public viewContainerRef = inject(ViewContainerRef);

    ngOnInit(): void {
        super.ngOnInit();
        this.resizeBounds = {
            nativeElement: this.contextService.getEditableElement()
        };
        this.resizeHeight = this.element.height;

        setTimeout(() => {
            this.container = (this.elementRef.nativeElement as HTMLElement).closest('.wiki-editor-scroll-container');
            this.cdr.markForCheck();
        });

        this.buildOutlineData();
        this.unbindingContentChange = this.bindingContentChange();
    }

    bindingContentChange() {
        const { onChange } = this.editor;
        this.editor.onChange = () => {
            onChange();
            try {
                if (this.editor.children === this.content) {
                    return;
                }
                this.buildOutlineData();
                this.cdr.markForCheck();
            } catch (error) {}
        };

        return () => {
            this.editor.onChange = onChange;
        };
    }

    buildOutlineData() {
        this.content = this.editor?.children as TheDocument;
        this.headerList = this.buildHeaderList();
        this.outlineTree = this.buildOutlineTree();
    }

    onContextChange() {
        super.onContextChange();
        if (isMobileMode(getMode(this.editor))) {
            return;
        }
        if (TheQueries.isGlobalCollapsed(this.editor) && this.isCollapsedAndNonReadonly) {
            this.openToolbar();
        } else {
            this.closeToolbar();
        }
    }

    openToolbar() {
        if (this.isToolbarOpen) {
            return;
        }

        const origin = this.elementRef.nativeElement;
        this.anchorPopoverRef = this.thyPopover.open(WikiCommonOutlineToolbarComponent, {
            initialState: {
                editor: this.editor,
                element: this.element,
                entity: {
                    numbered: this.element?.numbered,
                    mode: this.element?.mode,
                    level: this.element?.level
                }
            },
            origin,
            viewContainerRef: this.viewContainerRef,
            panelClass: 'the-plugin-toolbar-popover',
            minWidth: 0,
            placement: 'topLeft',
            hasBackdrop: false,
            manualClosure: true,
            insideClosable: false,
            outsideClosable: false,
            scrollStrategy: this.overlay.scrollStrategies.reposition(),
            autoAdaptive: true
        });

        const overlayRef = this.anchorPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    closeToolbar() {
        if (this.isToolbarOpen) {
            this.anchorPopoverRef?.close();
        }
    }

    changeExpand(event: Event, current: HeadingType) {
        event.preventDefault();
        event.stopPropagation();
        const { expand } = this.headersExpandMap[current.key];
        this.headersExpandMap[current.key].expand = !expand;
    }

    buildOutlineTree() {
        const result = helpers.buildTree(this.headerList, {
            idKey: 'key',
            parentIdKey: 'parentKey'
        });
        this.noChild = result.some((x: any) => x.children.length);
        return result;
    }

    buildHeaderList(): HeadingType[] {
        const headers = extractHeading(this.editor, this.editor.children as TheDocument);
        const minLevel = Math.min(...headers.map(heading => heading.level));
        const headerList = headers.map(header => {
            if (isUndefinedOrNull(this.headersExpandMap[header.key])) {
                this.headersExpandMap[header.key] = {
                    expand: true
                };
            }
            return { ...header, level: header.level - minLevel };
        });
        return this.buildHeaders(headerList).filter(it => it.level < this.element.level);
    }

    buildHeaders(headers: HeadingType[]): HeadingType[] {
        const numberedRuler = [1, 1, 1, 1];
        return headers.map((header, index) => {
            let parentKey = null;
            if (index > 0) {
                const previousHeader = headers[index - 1];
                if (header.level > previousHeader.level) {
                    parentKey = previousHeader.key;
                } else {
                    this.updateNumberedRuler(numberedRuler, header.level);
                    for (let i = index - 1; i >= 0; i--) {
                        if (header.level > headers[i].level) {
                            parentKey = headers[i].key;
                            break;
                        }
                    }
                }
            }
            this.setNumbered(numberedRuler, header);
            return { ...header, parentKey };
        });
    }

    setNumbered(numberedRuler: number[], header: HeadingType) {
        numberedRuler.slice(0, header.level + 1).forEach((it, i) => {
            header.numbered = i ? header.numbered + `.${it}` : `${it}`;
        });
    }

    updateNumberedRuler(numberedRuler: number[], i: number) {
        numberedRuler.map((_, index, arr) => {
            if (index === i) {
                arr[index]++;
            }
            if (index > i) {
                arr[index] = 1;
            }
        });
    }

    onResize({ height }: ThyResizeEvent) {
        this.resizeHeight = height;
    }

    onEndResize() {
        Transforms.select(this.editor, AngularEditor.findPath(this.editor, this.element));
        OutlineEditor.setAttribute(this.editor, this.element, { height: this.resizeHeight });
    }

    ngOnDestroy(): void {
        super.ngOnDestroy();
        if (this.unbindingContentChange) {
            this.unbindingContentChange();
        }
        this.headersExpandMap = null;
    }
}
