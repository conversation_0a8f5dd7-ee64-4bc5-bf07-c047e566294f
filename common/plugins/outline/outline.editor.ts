import { createEmptyParagraph, TheEditor, TheTransforms } from '@worktile/theia';
import { Editor, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { OutlineElement } from '../../custom-types';
import { WikiPluginTypes } from '../../types/editor.types';
import { OutlineMode, OutlineTreeLevel } from './outline.types';

export const OutlineEditor = {
    insert(editor: TheEditor) {
        const outlineElement: OutlineElement = {
            type: WikiPluginTypes.outline,
            children: [{ text: '' }],
            mode: OutlineMode.tree,
            numbered: false,
            level: OutlineTreeLevel.four
        };
        TheTransforms.insertElements(editor, outlineElement);
    },
    setAttribute(editor: Editor, element: OutlineElement, value: { [key: string]: unknown }) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.setNodes(editor, value, { at });
    },
    removeNode(editor: Editor, element: OutlineElement) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.removeNodes(editor, { at });
        Transforms.insertNodes(editor, createEmptyParagraph(), { at });
        AngularEditor.focus(editor);
    }
};
