import { createPluginFactory, TheEditor, ThePluginMenuItem, ThePluginMenuItemType } from '@worktile/theia';
import { WikiPluginTypes } from '../../types/editor.types';
import { CustomPluginKeys } from '../../types/plugins.types';
import { OutlineEditor } from './outline.editor';
import { Element } from 'slate';
import { WikiCommonOutlineComponent } from './outline.component';
import { WikiPluginMenuIcons } from '@wiki/common/types/plugin-menu';
import { NestedKey, StyxTranslateService } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';

export const withOutline = (editor: TheEditor) => {
    const { isBlockCard, renderElement, isVoid } = editor;

    editor.isBlockCard = (element: Element) => {
        if (element && element.type === WikiPluginTypes.outline) {
            return true;
        }
        return isBlockCard(element);
    };

    editor.isVoid = (element: Element) => {
        return element.type === WikiPluginTypes.outline ? true : isVoid(element);
    };

    editor.renderElement = (element: Element) => {
        if (element.type === WikiPluginTypes.outline) {
            return WikiCommonOutlineComponent;
        }
        return renderElement(element);
    };

    return editor;
};

export const createOutlinePlugin = (translate: StyxTranslateService) =>
    createPluginFactory({
        key: CustomPluginKeys.outline,
        withOverrides: withOutline,
        toolbarItems: [
            {
                key: WikiPluginTypes.outline,
                icon: 'toc', // 后期替换icon
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.outline'),
                execute: (editor: TheEditor) => OutlineEditor.insert(editor)
            }
        ],
        menuItems: [
            {
                key: WikiPluginTypes.outline,
                keywords: `yemiandagang,ymdg,pageoutline,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.pageOutline')}`,
                execute: (editor: TheEditor) => OutlineEditor.insert(editor),
                name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.pageOutline'),
                type: ThePluginMenuItemType.group,
                menuIcon: WikiPluginMenuIcons.outline,
                displayKey: '/ymdg'
            }
        ]
    })();

export const outlineMenu = (translate: StyxTranslateService) => [
    {
        key: WikiPluginTypes.outline,
        keywords: `yemiandagang,ymdg,page outline,pageoutline,${translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.pageOutline')}`,
        execute: (editor: TheEditor) => OutlineEditor.insert(editor),
        name: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.pageOutline'),
        description: translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.plugin.pageOutlineDesc'),
        type: ThePluginMenuItemType.group,
        menuIcon: WikiPluginMenuIcons.outline,
        displayKey: '/ymdg'
    }
];
