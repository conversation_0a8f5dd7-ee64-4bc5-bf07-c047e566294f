@use 'ngx-tethys/styles/variables.scss';

$anchor-link-level-padding: 12px;

.slate-element-outline {
    height: auto;
    &.empty {
        height: auto;
        overflow: hidden;
    }

    .wiki-common-outline-resizable {
        position: relative;
        display: flex;
        height: inherit;
        .outline-anchor {
            overflow-y: auto;
        }
        .thy-anchor {
            padding: 8px 0;
            &-wrapper {
                width: 100%;
                margin: 0;
                max-height: none !important;
                overflow: hidden;
            }
        }
        .thy-anchor-ink {
            display: none;
        }
        .thy-anchor-link {
            padding-left: 0;
            &-title {
                display: inline-block;
                margin-bottom: 0;
                cursor: pointer;
            }
            .expand-button {
                color: variables.$gray-600;
                cursor: pointer;
                &:hover {
                    color: variables.$gray-700;
                }
            }
            .link-title-text {
                color: variables.$primary;
                &:hover {
                    text-decoration: underline;
                }
            }
            &:not(.expand) {
                .thy-anchor-link {
                    display: none;
                }
            }
            &[level='0'] {
                padding-left: 16px;
            }
            &[level='1'] {
                padding-left: $anchor-link-level-padding * 2;
            }
            &[level='2'] {
                padding-left: $anchor-link-level-padding * 2;
            }
            &[level='3'] {
                padding-left: $anchor-link-level-padding * 2;
            }
            // 处理 2,3,4 级出现在第一次的情况
            &.parent-item {
                &[level='1'] {
                    padding-left: $anchor-link-level-padding * 3;
                }
                &[level='2'] {
                    padding-left: $anchor-link-level-padding * 5;
                }
                &[level='3'] {
                    padding-left: $anchor-link-level-padding * 7;
                }
            }
        }

        .outline-resize-handle {
            cursor: row-resize;
            height: 8px;
            width: 45px;
            left: 50%;
            transform: translateX(-50%);
            background: variables.$gray-300;
            bottom: -5px;
            border-radius: 5px;
            position: absolute;

            &::before,
            &:after {
                content: '';
                position: absolute;
                width: 34px;
                height: 1px;
                background: variables.$bg-default;
                left: 50%;
                transform: translateX(-50%);
                top: 2px;
            }

            &:after {
                top: 5px;
            }
        }
        .outline-tiled {
            .thy-anchor-link {
                &-title {
                    padding: 0;
                }
            }
        }
    }
}
.the-editor-readonly {
    .wiki-common-outline-resizable {
        height: auto !important;
        // 页面大纲预览页无边框复写
        border: none !important;
        .outline-anchor {
            max-height: none !important;
        }
    }
}
