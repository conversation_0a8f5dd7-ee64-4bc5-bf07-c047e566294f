import { Is, MemberInfo, StyxPivotEntity, WorkItemStateTypes } from '@atinc/ngx-styx';
import { RelationOpenType, WikiPluginTypes } from '@wiki/common/types/editor.types';
import { DateEntry } from 'ngx-tethys/date-picker';

export interface RelationItemInfo extends StyxPivotEntity {
    state?: {
        name?: string;
        color?: string;
        type?: WorkItemStateTypes;
    };
    project?: {
        name?: string;
    };
    short_id?: string;
    whole_identifier?: string;
    assignee?: MemberInfo;
    due?: DateEntry;
    period_id?: string;
    start?: DateEntry;
    is_archived?: Is;
    is_deleted?: Is;
    state_type?: WorkItemStateTypes;
    plan_at?: {
        end: number | null | Date;
    };
}

export interface RelationItems {
    [key: string]: RelationItemInfo;
}

export interface RelationItemRoute {
    url?: string;
    open_type?: RelationOpenType;
}

export type RelationItemType =
    | WikiPluginTypes.relationPage
    | WikiPluginTypes.relationWorkItem
    | WikiPluginTypes.relationTestCase
    | WikiPluginTypes.relationIdea
    | WikiPluginTypes.relationTicket
    | WikiPluginTypes.relationObjective
    | WikiPluginTypes.relationReport;

export const RelationItemTypes = [
    WikiPluginTypes.relationWorkItem,
    WikiPluginTypes.relationTestCase,
    WikiPluginTypes.relationIdea,
    WikiPluginTypes.relationTicket,
    WikiPluginTypes.relationObjective,
    WikiPluginTypes.relationReport
];
