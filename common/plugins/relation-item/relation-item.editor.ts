import { Overlay } from '@angular/cdk/overlay';
import { ChangeDetectorRef, ElementRef, ViewContainerRef } from '@angular/core';
import { hasPermissionTransform, helpers, StyxTranslateService } from '@atinc/ngx-styx';
import { CommonRelationItemSuggestionComponent } from '@wiki/common/components/relation-item/suggestion/suggestion.component';
import { wikiPermissionDefinition } from '@wiki/common/constants';
import { RelationItemElement } from '@wiki/common/custom-types';
import { PageStore } from '@wiki/common/stores/page.store';
import { getCommonRelationOption, RelationItemOption } from '@wiki/common/types';
import {
    ElementKinds,
    MentionEditor,
    THE_EDITOR_ORIGIN_ANCHOR,
    THE_EDITOR_POPOVER_REF,
    THE_PLUGIN_MENU_REF,
    TheEditor,
    TheQueries,
    createEmptyParagraph
} from '@worktile/theia';
import { ThyPopover } from 'ngx-tethys/popover';
import { Editor, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { RelationItemInfo, RelationItemType, RelationItemTypes } from './type';

export const RelationItemEditor = {
    isRelationItem(element: RelationItemElement) {
        if (element.type && RelationItemTypes.includes(element.type)) {
            return true;
        }
        return false;
    },
    isCard(relation: RelationItemElement) {
        if (relation.mode === 'card') {
            return true;
        }
        return false;
    },

    insertSuggestion(editor: TheEditor, type: RelationItemType) {
        const openDialog = function () {
            const { selection } = editor;
            const elementRef = editor.injector.get(ElementRef);
            const triggerAnchor = Editor.before(editor, selection.anchor);
            const entry = TheQueries.anchorBlockEntry(editor);
            const isParagraphStart = Editor.isStart(editor, triggerAnchor, entry[1]);
            const originAnchor = THE_EDITOR_ORIGIN_ANCHOR.get(editor);
            if (!originAnchor) {
                THE_EDITOR_ORIGIN_ANCHOR.set(editor, triggerAnchor);
            }
            const textElement = AngularEditor.toDOMNode(editor, entry[0]);
            const origin = isParagraphStart ? textElement : elementRef;
            RelationItemEditor.openSelectOverlay(editor, origin, type, false);
        };

        const currentRef = THE_EDITOR_POPOVER_REF.get(editor);
        const pluginMenuRef = THE_PLUGIN_MENU_REF.get(editor);
        const originAnchor = THE_EDITOR_ORIGIN_ANCHOR.get(editor);

        const isParagraphStart = Editor.isStart(editor, editor.selection.anchor, TheQueries.anchorBlockEntry(editor)[1]);

        const inlineClick = pluginMenuRef && isParagraphStart;

        if (!originAnchor) {
            if ((!currentRef && !pluginMenuRef) || inlineClick) {
                const insertNodePath = TheQueries.getInsertElementsPath(editor, [ElementKinds.paragraph]);
                if (!insertNodePath) {
                    Transforms.insertNodes(editor, createEmptyParagraph(), { select: true });
                }
                Transforms.insertText(editor, '#');
            } else {
                Transforms.delete(editor, { distance: 1, reverse: true });
                Transforms.insertText(editor, '#');
            }
        }

        setTimeout(() => {
            if (!currentRef && !pluginMenuRef) {
                openDialog();
            } else if (pluginMenuRef) {
                pluginMenuRef?.afterClosed().subscribe(() => {
                    openDialog();
                });
            } else if (currentRef) {
                currentRef?.afterClosed().subscribe(() => {
                    openDialog();
                });
            }
        }, 0);
    },

    openSelectOverlay(editor: TheEditor, origin: ElementRef | HTMLElement, type: RelationItemType, isSoftBreak = false) {
        const overlay = editor.injector.get(Overlay);
        const thyPopover = editor.injector.get(ThyPopover);
        const viewContainerRef = editor.injector.get(ViewContainerRef);
        const cdr = editor.injector.get(ChangeDetectorRef);
        const pageStore = editor.injector.get(PageStore);
        const relationItemStore = pageStore.getRelationItemStore(type);
        const translate = editor.injector.get(StyxTranslateService);
        const relationItemOption = getCommonRelationOption(translate)[type];

        let initialState: {
            editor: TheEditor;
            readonly?: boolean;
            disabled?: boolean;
            principal_id?: string;
            relationItemOption: RelationItemOption;
        } = {
            editor,
            readonly: false,
            relationItemOption
        };
        if (relationItemStore) {
            const principal_id = relationItemStore.queryParams.principal_id;
            const disabled = !hasPermissionTransform(
                relationItemOption?.permission,
                helpers.getStoragePermissionPoints(wikiPermissionDefinition),
                pageStore.snapshot.page?.permissions
            );
            initialState = {
                ...initialState,
                disabled,
                principal_id
            };
        }

        const popoverRef = thyPopover.open(CommonRelationItemSuggestionComponent, {
            origin,
            panelClass: `the-mention-relation-item`,
            placement: 'bottomLeft',
            insideClosable: false,
            outsideClosable: true,
            hasBackdrop: false,
            offset: 4,
            minWidth: 0,
            viewContainerRef,
            scrollStrategy: overlay.scrollStrategies.reposition(),
            autoAdaptive: true,
            initialState
        });

        THE_EDITOR_POPOVER_REF.set(editor, popoverRef);
        MentionEditor.updatePositionStrategy(editor, isSoftBreak);
        cdr.markForCheck();
    },

    insertText(editor: TheEditor, type: RelationItemType, relationItems: RelationItemInfo[]) {
        const nodes: RelationItemElement[] = relationItems.map(item => {
            return { type: type, mode: 'text', _id: item._id, children: [{ text: '' }] };
        });
        Transforms.insertNodes(editor, nodes, { select: true });
        AngularEditor.focus(editor);
    },

    wrapText(editor: TheEditor, type: RelationItemType, workItem: RelationItemElement) {
        Editor.withoutNormalizing(editor, () => {
            Transforms.setNodes(editor, { type: ElementKinds.paragraph, mode: null, _id: null });
            Transforms.insertNodes(editor, {
                type: type,
                mode: 'text',
                _id: workItem._id,
                children: [
                    {
                        text: ''
                    }
                ]
            } as RelationItemElement);
            TheEditor.focus(editor);
        });
    },

    removeNode(editor: TheEditor, element: RelationItemElement) {
        Transforms.move(editor, { reverse: true });
        const selection = editor.selection;
        const path = TheEditor.findPath(editor, element);
        Transforms.removeNodes(editor, { at: path });
        if (Editor.isBlock(editor, element)) {
            Transforms.insertNodes(editor, createEmptyParagraph(), { at: path });
            Transforms.select(editor, path);
        } else {
            Transforms.select(editor, selection);
        }
        TheEditor.focus(editor);
    },

    insert(editor: TheEditor, type: RelationItemType, relationItems: RelationItemInfo[]) {
        const nodes: RelationItemElement[] = relationItems.map(item => {
            return { type: type, mode: 'card', _id: item._id, children: [{ text: '' }] };
        });
        Editor.withoutNormalizing(editor, () => {
            Transforms.insertNodes(editor, nodes);
        });
    }
};
