import { PropertyColumn } from '@atinc/ngx-styx';
import { PLViewportMode } from '@plait-editor/types/editor';
import { PlaitElement, Viewport } from '@plait/core';
import { CustomElement, CustomText, EmptyText, TheEditor, TheElement } from '@worktile/theia';
import { Element, Range } from 'slate';
import { RelationPageTreeLevel, ThyAlertType } from './custom-constants';
import { WikiLayoutType } from './plugins/layout/layout.types';
import { OutlineMode, OutlineTreeLevel } from './plugins/outline/outline.types';
import { RelationItemType } from './plugins/relation-item/type';
import { TextDiagramType } from './plugins/text-diagram/constants';
import { WikiPluginTypes } from './types/editor.types';

export interface Discussion {
    _id?: string;
    quote: string;
    node_key: string;
    lowest_node_key: string;
    range: Range;
    text?: string;
    type?: string;
}

export interface AlertElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.alert;
    alertType: ThyAlertType;
    icon?: string;
    color?: string;
}

export interface RelationItemElement extends TheElement<WikiPluginTypes> {
    type: RelationItemType;
    mode: 'card' | 'text';
    _id?: string;
}

export interface RelationWorkItemElement extends RelationItemElement {
    type: WikiPluginTypes.relationWorkItem;
}

export interface RelationReportElement extends RelationItemElement {
    type: WikiPluginTypes.relationReport;
}

export interface LabelElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.label;
    label?: string;
    color?: string;
}

export interface MentionElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.mention;
    uid?: string;
}

export interface DateElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.date;
    date: number;
}

export interface EmojiElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.emoji;
    code: string;
}

export interface RelationPageElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.relationPage;
    children: EmptyText[];
    mode: 'card' | 'text';
}

export interface LayoutElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.layout;
    layoutType: WikiLayoutType;
    children: LayoutColumnElement[];
}

export interface LayoutColumnElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.layoutColumn;
}

export interface AttachmentElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.attachment;
    children: EmptyText[];
    mode?: 'card' | 'text';
    file?: File;
}
export interface FileElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.attachment | WikiPluginTypes.audio | WikiPluginTypes.video;
    children: EmptyText[];
    url?: string;
    mode?: 'card' | 'text';
    file?: File;
}

export interface ToggleListElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.toggleList;
    isExpanded: boolean;
    children: Element[];
}

export interface ToggleListItemElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.toggleListItem;
    children: Element[];
}

export interface FormulaElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.formula;
    content: string;
}

export interface TextDiagramElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.textDiagram;
    diagramType: TextDiagramType;
    content: string;
    height?: number;
}

export interface RelationElement extends TheElement<WikiPluginTypes> {
    extension_id: string;
    height?: number;
}

export interface RelationPageTreeElement extends RelationElement {
    type: WikiPluginTypes.relationPageTree;
    data: {
        level: RelationPageTreeLevel;
    };
}

export interface RelationWorkItemListElement extends RelationElement {
    type: WikiPluginTypes.relationWorkItemList;
    data: {
        columns: PropertyColumn[];
    };
}

export interface RelationTestCaseListElement extends RelationElement {
    type: WikiPluginTypes.relationTestCaseList;
    data: {
        columns: PropertyColumn[];
    };
}

export interface RelationIdeaListElement extends RelationElement {
    type: WikiPluginTypes.relationIdeaList;
    data: {
        columns: PropertyColumn[];
    };
}

export interface RelationTicketListElement extends RelationElement {
    type: WikiPluginTypes.relationTicketList;
    data: {
        columns: PropertyColumn[];
    };
}

export interface RelationObjectiveListElement extends RelationElement {
    type: WikiPluginTypes.relationObjectiveList;
    data: {
        columns: PropertyColumn[];
    };
}

export interface DiagramBoardElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.diagramBoard;
    data: PlaitElement[];
    viewport?: Viewport;
    height?: number;
    viewportMode?: PLViewportMode;
}

export interface DrawioElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.drawio;
    data: any;
    viewport?: Viewport;
    height?: number;
    viewportMode?: PLViewportMode;
}

export interface OutlineElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.outline;
    mode?: OutlineMode;
    level?: OutlineTreeLevel;
    numbered?: boolean;
    height?: number;
}

export interface AudioElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.audio;
    children: EmptyText[];
    mode?: 'card' | 'text';
    file?: File;
}

export interface VideoElement extends TheElement<WikiPluginTypes> {
    type: WikiPluginTypes.video;
    children: EmptyText[];
    mode?: 'card' | 'text';
    file?: File;
}

export interface RelationTestCaseElement extends RelationItemElement {
    type: WikiPluginTypes.relationTestCase;
}

export interface RelationIdeaElement extends RelationItemElement {
    type: WikiPluginTypes.relationIdea;
}

export interface RelationTicketElement extends RelationItemElement {
    type: WikiPluginTypes.relationTicket;
}

export interface RelationObjectiveElement extends RelationItemElement {
    type: WikiPluginTypes.relationObjective;
}

export type WikiCustomText = {
    strike?: boolean;
    color?: string;
    type?: string; // discussion
    _id?: string; // discussion
    data?: any; // shared
};

export type WikiCardElementTypes = VideoElement | AudioElement | RelationPageElement | RelationItemElement | FileElement;

export type WikiElement =
    | CustomElement
    | RelationWorkItemElement
    | LabelElement
    | DateElement
    | EmojiElement
    | AlertElement
    | LayoutElement
    | LayoutColumnElement
    | MentionElement
    | RelationPageElement
    | AttachmentElement
    | ToggleListElement
    | ToggleListItemElement
    | TextDiagramElement
    | FormulaElement
    | DiagramBoardElement
    | DrawioElement
    | RelationPageTreeElement
    | RelationWorkItemListElement
    | RelationTestCaseListElement
    | OutlineElement
    | AudioElement
    | VideoElement
    | FileElement
    | RelationItemElement
    | RelationTestCaseElement
    | RelationIdeaElement
    | RelationIdeaListElement
    | RelationTicketElement
    | RelationTicketListElement
    | RelationObjectiveElement
    | RelationObjectiveListElement;

export type WikiText = CustomText & WikiCustomText;

export type TheDocument = Element[];

export type NodeLevel = 'highest' | undefined;

declare module 'slate' {
    interface CustomTypes {
        Element: WikiElement;
        Text: WikiText;
        Editor: TheEditor;
    }
}
