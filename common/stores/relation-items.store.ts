import { HttpClient } from '@angular/common/http';
import { inject, Injectable, OnDestroy } from '@angular/core';
import {
    PaginationResponseData,
    StyxPivotEntity,
    StyxPivotEntityReferences,
    StyxPivotEntityState,
    StyxPivotEntityStore,
    StyxRelationParamsInfo,
    WikiBroadObjectTypes
} from '@atinc/ngx-styx';
import { helpers, ResponseData } from '@atinc/ngx-styx/core';
import { Action } from '@tethys/store';
import { Observable } from 'rxjs';
import { filter, map, take, tap } from 'rxjs/operators';
import { SafeAny } from 'slate-angular';
import { PageType } from '../interface/page';
import { RelationParamsInfo } from '../interface/relation';
import { RelationApiService } from '../services/relation-api.service';

@Injectable()
export abstract class RelationItemsStore<T extends string>
    extends StyxPivotEntityStore<
        StyxPivotEntity,
        StyxPivotEntityReferences,
        StyxPivotEntityState<StyxPivotEntity, StyxPivotEntityReferences>
    >
    implements OnDestroy
{
    private isInitialized: boolean;

    public queryParams: RelationParamsInfo<T>;

    public http = inject(HttpClient);

    public relationApiService = inject(RelationApiService);

    initializeParams(params: RelationParamsInfo<T>) {
        this.queryParams = {
            ...params,
            principal_name: WikiBroadObjectTypes.page
        };
    }

    initializeRelationItem(isShared: boolean, type: PageType, params: RelationParamsInfo<T>, data?: ResponseData) {
        this.initializeParams(params);
        if (type === 'default' && !data) {
            if (!isShared) {
                this.update({ entities: [], references: {} });
                this.fetchRelationList().subscribe();
            }
        } else if (data) {
            this.initializeWithReferences(data?.value, data.references);
            this.isInitialized = true;
        } else {
            this.isInitialized = true;
        }
    }

    @Action()
    fetchRelationList(params: StyxRelationParamsInfo = {}): Observable<SafeAny> {
        this.isInitialized = false;
        const fetchParams = { ...params, ...this.queryParams };
        return this.relationApiService.fetchRelatedItems(fetchParams).pipe(
            tap((data: PaginationResponseData<StyxPivotEntity[]>) => {
                this.isInitialized = true;
                this.initializeWithReferences(data.value, data.references);
            }),
            map((data: PaginationResponseData<StyxPivotEntity[]>) => data.value)
        );
    }

    @Action()
    fetchRecentSelectionItems(): Observable<SafeAny> {
        return this.relationApiService.fetchSelectableItems(this.queryParams);
    }

    getRelationItemById(broadObjectType: string, id: string): Observable<StyxPivotEntity | null> {
        let state: 'fetching' | 'fetched' | null = null;

        const emitEntitiesChange = () => {
            this.next({ ...this.snapshot, entities: [...this.snapshot.entities] });
        };

        return this.entities$.pipe(
            map((value: StyxPivotEntity[]) => {
                const item = helpers.find(value, { _id: id });
                if (this.isInitialized && value && !item && state === null) {
                    state = 'fetching';
                    this.relationApiService.fetchRelationItem(broadObjectType, id, this.queryParams.team_id).subscribe({
                        next: (data: ResponseData) => {
                            state = 'fetched';
                            if (data.value) {
                                if (this.snapshot.entities.length > 0 && data.value.length > 0) {
                                    this.addWithReferences(data.value, data.references);
                                } else {
                                    this.initializeWithReferences([...this.snapshot.entities, ...data.value], {
                                        ...this.snapshot.references,
                                        ...data.references
                                    });
                                }
                            } else {
                                // 外部站点无拦截器，所以接口异常不会进入 error
                                emitEntitiesChange();
                            }
                        },
                        error: () => {
                            state = 'fetched';
                            emitEntitiesChange();
                        }
                    });
                }
                return item || null;
            }),
            filter(relationItem => !!relationItem || state === 'fetched'),
            take(1)
        );
    }

    ngOnDestroy(): void {
        super.ngOnDestroy();
    }
}
