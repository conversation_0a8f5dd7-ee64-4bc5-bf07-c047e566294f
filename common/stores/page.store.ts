import { DestroyRef, Injectable, OnDestroy, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import {
    AgileBroadObjectTypes,
    AppRootContext,
    AttachmentEntity,
    AttachmentFeedEvent,
    DriveEntity,
    FeedInfo,
    GlobalUsersStore,
    Id,
    Is,
    MemberInfo,
    ResponseData,
    ShipBroadObjectTypes,
    StyxAttachmentApiService,
    StyxFeedReceiver,
    StyxTheImageService,
    TeamsBroadObjectTypes,
    TesthubBroadObjectTypes
} from '@atinc/ngx-styx';
import { Action, Store } from '@tethys/store';
import { API_PREFIX } from '@wiki/app/constants';
import { DiscussionInfo, PageInfo, PageReferences, StencilInfo } from '@wiki/app/entities';
import { PageApiService } from '@wiki/app/services';
import { DiscussionApiService } from '@wiki/app/services/discussion-api.service';
import { THE_IMAGE_SERVICE_TOKEN, THE_MODE_TOKEN } from '@worktile/theia';
import moment from 'moment';
import { produce } from 'ngx-tethys/util';
import { Observable, of } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { AttachmentScope, PageRelationReference, PageState, PageType, WikiPageContent } from '../interface';
import { IsDocumentPipe, IsTablePipe } from '../pipe/page.pipe';
import { RelationIdeaStore } from '../plugins/relation-idea/relation-idea.store';
import { RelationItemType } from '../plugins/relation-item/type';
import { RelationObjectiveStore } from '../plugins/relation-objective/relation-objective.store';
import { RelationPageStore } from '../plugins/relation-page/relation-page.store';
import { RelationTestCaseStore } from '../plugins/relation-test-case/relation-test-case.store';
import { RelationTicketStore } from '../plugins/relation-ticket/relation-ticket.store';
import { RelationWorkItemStore } from '../plugins/relation-work-item/relation-work-item.store';
import { AttachmentApiService } from '../services/attachment-api.service';
import { PageDocumentData, PageEventBus } from '../services/page-event-bus';
import { WikiPluginTypes } from '../types';
import { updateAttachment, updateAttachmentPermission } from '../util';
import { combineTags } from '../util/combine-tags';
import { TinyDate } from 'ngx-tethys/util';
import { CommonPageApiService } from '../services/page-api-service';

@Injectable()
export abstract class PageStore extends Store<PageState> implements OnDestroy {
    isSpecifVersion: boolean;

    isLatestVersionAttachment: boolean;

    public relationWorkItemStore = inject(RelationWorkItemStore, { optional: true });

    public relationPageStore = inject(RelationPageStore, { optional: true });

    public relationTestCaseStore = inject(RelationTestCaseStore, { optional: true });

    public relationIdeaStore = inject(RelationIdeaStore, { optional: true });

    public relationTicketStore = inject(RelationTicketStore, { optional: true });

    public relationObjectiveStore = inject(RelationObjectiveStore, { optional: true });

    public attachmentApiService = inject(AttachmentApiService, { optional: true });

    public modeConfig = inject(THE_MODE_TOKEN, { optional: true });
    public isDocument = inject(IsDocumentPipe);
    public isTable = inject(IsTablePipe);
    private feedReceiver = inject(StyxFeedReceiver);
    private styxAttachmentApiService = inject(StyxAttachmentApiService);
    protected router = inject(Router);
    protected destroyRef = inject(DestroyRef);
    protected appRootContext = inject(AppRootContext);
    protected pageEventBus = inject(PageEventBus);
    protected pageApiService = inject(PageApiService);
    protected commonPageApiService = inject(CommonPageApiService);
    protected globalUsersStore = inject(GlobalUsersStore);
    protected imageService = inject<StyxTheImageService>(THE_IMAGE_SERVICE_TOKEN, { optional: true });
    protected discussionApiService = inject(DiscussionApiService);

    constructor() {
        super({});
        if (this.pageEventBus) {
            this.subscribeGlobalPage();
            this.subscribeGlobalContent();
        }
        this.registerFeedEvents();
    }

    getRelationItemStore(type: RelationItemType) {
        if (type === WikiPluginTypes.relationWorkItem) {
            return this.relationWorkItemStore;
        }
        if (type === WikiPluginTypes.relationTestCase) {
            return this.relationTestCaseStore;
        }
        if (type === WikiPluginTypes.relationIdea) {
            return this.relationIdeaStore;
        }
        if (type === WikiPluginTypes.relationTicket) {
            return this.relationTicketStore;
        }
        if (type === WikiPluginTypes.relationObjective) {
            return this.relationObjectiveStore;
        }
    }

    protected setPublicImageToken(token: string) {
        if (token) {
            this.imageService?.setPublicImageToken({
                token,
                createTime: new TinyDate().getTime()
            });
        }
    }

    ngOnDestroy() {
        super.ngOnDestroy();
    }

    @Action()
    initialize(page: PageInfo, references: PageState, teamId?: Id, pageVersionId?: Id) {
        const tags = combineTags(page?.tag_ids, references?.tags);
        this.update({
            page: {
                ...page,
                refs: {
                    pilot: references?.space,
                    tags
                }
            },
            ...references
        });
        this.globalUsersStore.addUsers(references.members);
        if (this.isDocument.transform({ type: page.type })) {
            this.initRelationStore(
                page,
                {
                    relation_work_items: references?.relation_work_items,
                    relation_test_cases: references?.relation_test_cases,
                    relation_ideas: references?.relation_ideas,
                    relation_pages: references?.relation_pages,
                    relation_tickets: references?.relation_tickets,
                    relation_objectives: references?.relation_objectives
                },
                teamId,
                pageVersionId
            );
        }

        if (this.isTable.transform({ type: page.type })) {
            this.initTableRelationStore(
                page,
                {
                    relation_work_items: references?.relation_work_items,
                    relation_test_cases: references?.relation_test_cases,
                    relation_ideas: references?.relation_ideas,
                    relation_pages: references?.relation_pages,
                    relation_tickets: references?.relation_tickets,
                    relation_objectives: references?.relation_objectives
                },
                teamId,
                pageVersionId
            );
        }
    }

    initTableRelationStore(page: PageInfo, data?: PageRelationReference, teamId?: Id, pageVersionId?: Id) {
        this.commonPageApiService
            .fetchPageRelations(page.space_id, page._id)
            .pipe(
                tap((data: ResponseData) => {
                    this.initRelationStore(page, data.references, teamId, pageVersionId);
                })
            )
            .subscribe();
    }

    initRelationStore(page: PageInfo, data?: PageRelationReference, teamId?: Id, pageVersionId?: Id) {
        const pageType: PageType = page.is_stencil ? 'stencil' : page.is_published || this.snapshot.isShared ? 'default' : 'draft';
        const defaultParams = {
            principal_id: page._id,
            version_id: pageVersionId,
            team_id: teamId
        };
        if (this.relationWorkItemStore) {
            this.relationWorkItemStore.initializeRelationItem(
                this.snapshot.isShared,
                pageType,
                {
                    ...defaultParams,
                    target_name: AgileBroadObjectTypes.workItem
                },
                data?.relation_work_items
            );
        }
        if (this.relationTestCaseStore) {
            this.relationTestCaseStore.initializeRelationItem(
                this.snapshot.isShared,
                pageType,
                {
                    ...defaultParams,
                    target_name: TesthubBroadObjectTypes.testCase
                },
                data?.relation_test_cases
            );
        }
        if (this.relationIdeaStore) {
            this.relationIdeaStore.initializeRelationItem(
                this.snapshot.isShared,
                pageType,
                {
                    ...defaultParams,
                    target_name: ShipBroadObjectTypes.idea
                },
                data?.relation_ideas
            );
        }
        if (this.relationPageStore) {
            this.relationPageStore.initializeRelationPages(data?.relation_pages);
        }
        if (this.relationTicketStore) {
            this.relationTicketStore.initializeRelationItem(
                this.snapshot.isShared,
                pageType,
                {
                    ...defaultParams,
                    target_name: ShipBroadObjectTypes.ticket
                },
                data?.relation_tickets
            );
        }
        if (this.relationObjectiveStore) {
            this.relationObjectiveStore.initializeRelationItem(
                this.snapshot.isShared,
                pageType,
                {
                    ...defaultParams,
                    target_name: TeamsBroadObjectTypes.objective
                },
                data?.relation_objectives
            );
        }
    }

    abstract fetchPage<TOption>(pageId: string, options?: TOption): Observable<ResponseData<PageInfo | StencilInfo, PageReferences>>;

    fetchChildPage<TOption>(groupId: string, pageId: string): Observable<ResponseData<PageInfo | StencilInfo, PageReferences>> {
        return of(null);
    }

    subscribeGlobalPage() {
        this.pageEventBus
            .onPageInfoChange$()
            .pipe(
                tap((page: PageInfo) => {
                    // 未指定版本时，更新页面信息
                    if (!this.isSpecifVersion && this.snapshot.page && page._id === this.snapshot.page._id) {
                        this.update({ page: { ...this.snapshot.page, ...page } });
                    }
                    if (this.snapshot.parentPages) {
                        const parentIds = this.snapshot.parentPages.map(item => item._id);
                        if (parentIds.includes(page._id)) {
                            this.update({
                                parentPages: produce(this.snapshot.parentPages).update(page._id, page)
                            });
                        }
                    }
                }),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe();
    }

    subscribeGlobalContent() {
        this.pageEventBus
            .onPageDocumentChange$()
            .pipe(
                tap((data: PageDocumentData) => {
                    // 未指定版本时，更新页面内容
                    if (!this.isSpecifVersion && this.snapshot.page && data.pageId === this.snapshot.page._id) {
                        const relations: PageRelationReference = {
                            relation_work_items: data.relation_work_items,
                            relation_test_cases: data.relation_test_cases,
                            relation_ideas: data.relation_ideas,
                            relation_pages: data.relation_pages,
                            relation_tickets: data.relation_tickets,
                            relation_objectives: data.relation_objectives
                        };
                        if (this.isDocument.transform({ type: this.snapshot.page.type })) {
                            this.initRelationStore(this.snapshot.page, relations);
                        }
                        if (data.isPublish) {
                            this.pureUpdateContent(data.document, false);
                        }
                    }
                }),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe();
    }

    @Action()
    pureUpdatePage(page: Partial<PageInfo>) {
        this.update({ page: { ...this.snapshot.page, ...page } });
    }

    @Action()
    pureUpdatePageById(id: Id, page: Partial<PageInfo>) {
        if (this.snapshot.page && id === this.snapshot.page._id) {
            this.update({ page: { ...this.snapshot.page, ...page } });
        }
    }

    pureUpdateContent(content: WikiPageContent, isPublish = false) {
        this.update({ content: content, isPublish });
    }

    @Action()
    pureAddAttachment(attachment: AttachmentEntity) {
        this.update({
            page: {
                ...this.snapshot.page,
                attachments: produce(this.snapshot.page?.attachments).add(attachment),
                attachment_count: this.snapshot.page?.attachment_count + 1,
                none_media_attachment_count: this.snapshot.page?.none_media_attachment_count + 1
            }
        });
    }

    @Action()
    pureUpdateParticipants(pageId: Id, participants: MemberInfo[]) {
        if (pageId === this.snapshot.page._id) {
            this.globalUsersStore.addUsers(participants);
            this.update({
                page: {
                    ...this.snapshot.page,
                    participants: participants
                }
            });
        }
    }

    @Action()
    pureClearPage() {
        this.update({ content: null, page: null, parentPages: null, members: null });
    }

    @Action()
    pureUpdateShareState(isSharing: Is) {
        this.update({
            page: {
                ...this.snapshot.page,
                is_shared: isSharing
            }
        });
    }

    @Action()
    addAttachment(pageId: Id, driveEntity: DriveEntity, attachmentScope?: AttachmentScope) {
        // add drive entity to attachments at once, because it has been removed in uploading-items
        this.pureAddAttachment(driveEntity);
        return this.attachmentApiService.addAttachment(pageId, driveEntity._id, attachmentScope).pipe(
            map((data: ResponseData) => {
                const attachment: AttachmentEntity = {
                    ...data.value,
                    attachment_id: data.value._id,
                    updated_at: data.value.updated_at ? data.value.updated_at : moment().unix(),
                    updated_by: data.value.updated_by ? data.value.updated_by : this.appRootContext.globalInfo.me
                };
                this.pureRemoveAttachmentById(driveEntity._id);
                this.pureAddAttachment(attachment);
                return attachment;
            })
        );
    }

    @Action()
    modifyAttachmentName(pageId: string, attachmentId: string, title: string) {
        return this.attachmentApiService.modifyAttachmentName(pageId, attachmentId, title).pipe(
            tap(data => {
                this.update({
                    page: {
                        ...this.snapshot.page,
                        attachments: produce(this.snapshot.page.attachments).update(attachmentId, attachment => {
                            return { ...attachment, title };
                        })
                    }
                });
            })
        );
    }

    @Action()
    updateRelationTotal(pageId: string, relationType: string, count: number) {
        if (this.snapshot.page && pageId === this.snapshot.page._id) {
            this.update(state => {
                return {
                    page: {
                        ...state.page,
                        relation_total: {
                            ...state.page.relation_total,
                            [relationType]: count
                        }
                    }
                };
            });
        }
    }

    @Action()
    pureRemoveAttachmentById(attachmentId: Id) {
        this.update(state => ({
            page: {
                ...state.page,
                attachments: produce(state.page.attachments).remove(attachmentId),
                attachment_count: state.page?.attachment_count - 1,
                none_media_attachment_count: state.page?.none_media_attachment_count - 1
            }
        }));
    }

    @Action()
    deleteAttachments(pageId: string, attachmentIds: string[]) {
        return this.attachmentApiService.deleteAttachments(pageId, attachmentIds).pipe(
            tap(() => {
                attachmentIds.forEach(item => {
                    this.pureRemoveAttachmentById(item);
                });
            })
        );
    }

    @Action()
    updateAttachments(attachments: AttachmentEntity[]) {
        this.update({
            page: {
                ...this.snapshot.page,
                attachments: [...attachments],
                attachment_count: attachments.length
            }
        });
    }

    @Action()
    updatePage(page: PageInfo | StencilInfo): Observable<any> {
        return this.commonPageApiService.updatePage(page._id, { name: page.name }).pipe(
            tap((data: ResponseData) => {
                this.pureUpdatePageById(page._id, { name: page.name });
            })
        );
    }

    @Action()
    changeFullScreenState(state: boolean) {
        this.update({
            isFullScreen: state
        });
    }

    @Action()
    addFavorite(pageId: string) {
        return this.pageApiService.addFavorite(pageId).pipe(
            tap(() => {
                this.update({
                    page: {
                        ...this.snapshot.page,
                        is_favorite: Is.yes
                    }
                });
            })
        );
    }

    @Action()
    removeFavorite(pageId: string) {
        return this.pageApiService.removeFavorite(pageId).pipe(
            tap(() => {
                this.update({
                    page: {
                        ...this.snapshot.page,
                        is_favorite: Is.no
                    }
                });
            })
        );
    }

    @Action()
    pureAddDiscussions(discussions: DiscussionInfo[]) {
        this.update({
            discussions: produce(this.snapshot.discussions).add(discussions)
        });
    }

    @Action()
    pureRemoveDiscussions(discussionId?: Id) {
        if (discussionId) {
            this.update({
                discussions: this.snapshot.discussions.filter(item => item._id !== discussionId)
            });
        } else {
            this.update({
                discussions: this.snapshot.discussions.filter(item => item._id)
            });
        }
    }

    @Action()
    getResolvedDiscussionList(pageId: Id, nodeKey: string, resolvedAt?: string | number) {
        return this.discussionApiService.getDiscussionList(pageId, nodeKey, resolvedAt ? resolvedAt : '', 1).pipe(
            tap((result: { value: DiscussionInfo[] }) => {
                this.pureAddDiscussions(result.value);
            })
        );
    }

    @Action()
    updateDiscussionStatus(result: DiscussionInfo[]) {
        result.forEach((item: DiscussionInfo) => {
            this.update({
                discussions: produce(this.snapshot.discussions).update(item._id, item)
            });
        });
    }

    getOngoingDiscussions$() {
        return this.select$(state => state.discussions).pipe(
            map((entities: DiscussionInfo[]) => {
                if (entities) {
                    return entities.filter((discussion: DiscussionInfo) => !discussion.is_resolved);
                } else {
                    return [];
                }
            })
        );
    }

    @Action()
    pureUpdateAttachmentFromFeed(feed: FeedInfo) {
        const attachments = updateAttachment(this.snapshot.page.attachments, feed);
        if (attachments) {
            this.update({
                page: {
                    ...this.snapshot.page,
                    attachments
                }
            });
        }
    }

    @Action()
    updateAttachmentFromFeed(feed: FeedInfo) {
        const attachments = updateAttachmentPermission(this.snapshot.page.attachments, feed);
        if (attachments) {
            this.update({
                page: {
                    ...this.snapshot.page,
                    attachments
                }
            });
        }
    }

    @Action()
    getLatestAttachment(pageId: string, attachmentId: string) {
        const apiPrefix = `${API_PREFIX}/pages/${pageId}`;
        return this.styxAttachmentApiService.fetchAttachment(apiPrefix, attachmentId);
    }

    @Action()
    updateLatestVersionAttachmentFromFeed(feed: FeedInfo) {
        const { attachment_id, latest_version_file_id } = feed.data;
        const attachment = this.snapshot.page?.attachments.find(attachment => attachment._id === attachment_id);
        if (attachment && attachment.file_id !== latest_version_file_id) {
            return this.getLatestAttachment(this.snapshot.page._id, attachment_id).pipe(
                tap((data: ResponseData) => {
                    this.update({
                        page: {
                            ...this.snapshot.page,
                            attachments: produce(this.snapshot.page?.attachments).update(attachment_id, data.value)
                        }
                    });
                })
            );
        }
    }

    getPageAttachments(pageId?: string) {
        pageId = pageId || this.snapshot.page._id;
        return this.attachmentApiService.fetchAttachments(pageId).pipe(
            map((data: ResponseData<AttachmentEntity[]>) => {
                this.updateAttachments(data.value);
                return data.value;
            })
        );
    }

    registerFeedEvents() {
        this.feedReceiver
            .received({ ignoreApp: true })
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((feed: FeedInfo) => {
                switch (feed.event_key) {
                    case AttachmentFeedEvent.updateAttachmentFileVersion:
                        if (this.isLatestVersionAttachment) {
                            this.updateLatestVersionAttachmentFromFeed(feed);
                        } else {
                            this.updateAttachmentFromFeed(feed);
                        }
                        return;
                    case AttachmentFeedEvent.updateFile:
                        this.pureUpdateAttachmentFromFeed(feed);
                        return;
                }
            });
    }
}
