import { Injectable } from '@angular/core';
import { ResponseData } from '@atinc/ngx-styx';
import { Action, Store } from '@tethys/store';
import { EditingPageFrom } from '@wiki/app/constants/page';
import { DraftInfo } from '@wiki/app/entities/page-info';
import { Subject } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CommonPageApiService } from '../services/page-api-service';

export interface EditingPageState {
    from: EditingPageFrom | null;
    previousUrl?: string; // 新建页面 -> 直接点击取消 -> 路由需要跳转为上一个页面
}

@Injectable({
    providedIn: 'root'
})
export class CommonEditingPageStore extends Store<EditingPageState> {
    private initializeDecorate$ = new Subject<void>();

    constructor(private pageApiService: CommonPageApiService) {
        super({});
    }

    @Action()
    createPage(spaceId: string, page: DraftInfo, from: EditingPageFrom, previousUrl?: string) {
        return this.pageApiService.addPage(spaceId, page).pipe(
            tap((data: ResponseData) => {
                this.update({
                    from,
                    previousUrl
                });
            })
        );
    }

    @Action()
    clearFrom() {
        this.update({
            from: null
        });
    }

    updateDecorate() {
        this.initializeDecorate$.next();
    }

    getDecorateState() {
        return this.initializeDecorate$.asObservable();
    }
}
