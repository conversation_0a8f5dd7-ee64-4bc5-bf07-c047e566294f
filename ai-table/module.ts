import { NgModule } from '@angular/core';
import { AITableGridView } from './app/grid-view/grid.component';
import { AITableComponent } from './app/table.component';
import { AITableViews } from '@ai-table/components';
import { AITableCommonGrid } from '@ai-table/common-grid/common-grid';
import { StyxThyAvatarService } from '@atinc/ngx-styx';

const COMPONENTS = [AITableViews, AITableGridView, AITableComponent, AITableCommonGrid];

@NgModule({
    declarations: [],
    imports: [...COMPONENTS],
    exports: [...COMPONENTS],
    providers: [StyxThyAvatarService]
})
export class AITableModule {}
