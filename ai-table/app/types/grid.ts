import { AITableReferences } from '@ai-table/utils';
import { WorkItemTypeGroup } from '@atinc/ngx-styx';
import { Dictionary } from 'ngx-tethys/types';

export interface AITableCustomReferences extends AITableReferences {
    relation_tickets?: Dictionary<AITableCustomRelationInfo>;
    relation_work_items?: Dictionary<AITableCustomRelationInfo>;
    relation_test_cases?: Dictionary<AITableCustomRelationInfo>;
    relation_ideas?: Dictionary<AITableCustomRelationInfo>;
    relation_pages?: Dictionary<AITableCustomRelationInfo>;
    relation_objectives?: Dictionary<AITableCustomRelationInfo>;
    svgMap?: Map<string, string>;
}

export interface AITableCustomRelationInfo {
    _id: string;
    title: string;
    name?: string;
    type: string;
    whole_identifier: string;
    type_group?: WorkItemTypeGroup;
    number?: number;
}
