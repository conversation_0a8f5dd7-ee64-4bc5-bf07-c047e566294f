import { AITableActionIconConfig, AITableImage, AITableRect, AITableText } from '@ai-table/grid';
import { AITableField, AITableFieldType, AttachmentSettings, MemberSettings } from '@ai-table/utils';
import { RectConfig } from 'konva/lib/shapes/Rect';
import { TextConfig } from 'konva/lib/shapes/Text';
import { ImageConfig } from 'konva/lib/shapes/Image';
import { ShapeConfig } from 'konva/lib/Shape';
import { AITableCustomRelationInfo } from './grid';

export interface AITableMemberField extends AITableField {
    type: AITableFieldType.member;
    settings: MemberSettings;
}

export interface AITableAttachmentField extends AITableField {
    type: AITableFieldType.attachment;
    settings: AttachmentSettings;
}

export interface RelationItemConfig {
    bgRect?: AITableRect;
    tag?: {
        bgRect: AITableRect;
        text: AITableText;
    };
    icon?: Pick<AITableImage, 'x' | 'y' | 'width' | 'height' | 'url'>;
    identifier?: AITableText;
    title?: AITableText;
    relationInfo?: AITableCustomRelationInfo;
    closeActionConfig?: Partial<AITableActionIconConfig>;
}

export interface MoreCountItem {
    bgRect: AITableRect;
    text: AITableText;
}

export interface AITableRelationKonvaConfig extends ShapeConfig {
    bgRect: RectConfig & { relationInfo: AITableCustomRelationInfo };
    tag?: {
        bgRect: RectConfig;
        text: TextConfig;
    };
    icon?: ImageConfig;
    closeActionConfig?: Partial<AITableActionIconConfig>;
    identifier?: TextConfig;
    title?: TextConfig | null;
    relationInfo: AITableCustomRelationInfo;
}

export type RelationFieldValue = string[];
