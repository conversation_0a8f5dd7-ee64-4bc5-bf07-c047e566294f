import { ChangeDetectionStrategy, Component } from '@angular/core';
import { AITableViews } from './components';
import { RouterOutlet } from '@angular/router';

@Component({
    selector: 'ai-table',
    templateUrl: './table.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [AITableViews, RouterOutlet],
    host: {
        class: 'wiki-ai-table d-flex flex-column flex-fill',
    }
})
export class AITableComponent {}
