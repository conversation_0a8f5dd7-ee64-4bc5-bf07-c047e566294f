import {
    AI_TABLE_CELL_PADDING,
    AITableRender,
    DEFAULT_TEXT_ALIGN_LEFT,
    DEFAULT_TEXT_DECORATION,
    DEFAULT_TEXT_VERTICAL_ALIGN_MIDDLE,
    CellDrawer,
    DEFAULT_FONT_WEIGHT,
    AI_TABLE_FIELD_HEAD_HEIGHT,
    AI_TABLE_CELL_MULTI_PADDING_LEFT,
    Drawer,
    AITableText,
    AI_TABLE_ACTION_COMMON_SIZE,
    AI_TABLE_ACTION_COMMON_RIGHT_PADDING,
    AddOutlinedPath,
    Colors,
    AI_TABLE_ACTION_COMMON_RADIUS,
    AITableActionIconConfig,
    AI_TABLE_OFFSET,
    AITableRect,
    FONT_SIZE_SM,
    AI_TABLE_CELL_MULTI_ITEM_MARGIN_LEFT
} from '@ai-table/grid';
import { isUndefinedOrNull } from 'ngx-tethys/util';
import { MoreCountItem, RelationItemConfig } from '../../types/field';
import { AITableCustomReferences } from '../../types/grid';
import { CloseIconPath } from '../../constants';
import { WikiPluginTypes } from '@wiki/common/types';
import { hexToRgba } from '../../utils/draw';

export enum RelationHeaderType {
    icon = 'icon',
    tag = 'tag'
}

export function renderRelationCell(render: AITableRender<AITableCustomReferences>, drawer: CellDrawer) {
    const { references, x, y, field, transformValue, rowHeight, columnWidth, isActive, style } = render;
    if (isUndefinedOrNull(transformValue)) {
        return;
    }
    let options = {
        headerType: RelationHeaderType.icon,
        showAddAction: false,
        multilineRow: false,
        showClose: false
    };
    if (field.type === WikiPluginTypes.relationObjective) {
        options.headerType = RelationHeaderType.tag;
    }
    const { relationItems, moreCount } = getRelationItemsConfigs(render, drawer, options);
    if (relationItems.length > 0) {
        for (const relationItem of relationItems) {
            drawer.rect(relationItem.bgRect);
            if (relationItem.identifier) {
                drawer.text(relationItem.identifier);
            }
            if (relationItem.title) {
                drawer.text(relationItem.title);
            }
            if (relationItem.icon) {
                drawer.image({
                    ...relationItem.icon,
                    name: relationItem.icon.url
                });
            }
            if (relationItem.tag) {
                drawer.rect(relationItem.tag.bgRect);
                drawer.text(relationItem.tag.text);
            }
        }
    }
    if (moreCount) {
        drawer.rect(moreCount.bgRect);
        drawer.text(moreCount.text);
    }
}

export function getRelationItemsConfigs(
    render: AITableRender<AITableCustomReferences>,
    drawer: Drawer,
    options: {
        headerType: RelationHeaderType;
        showAddAction: boolean;
        multilineRow: boolean;
        showClose: boolean;
    } = {
        headerType: RelationHeaderType.icon,
        showAddAction: false,
        multilineRow: false,
        showClose: false
    }
) {
    const { showAddAction, multilineRow, headerType, showClose } = options;
    const { references, x, y, field, transformValue = [], rowHeight, columnWidth, isActive, style } = render;
    const itemHeight = 24;
    let currentX = AI_TABLE_CELL_PADDING;
    let currentY = (AI_TABLE_FIELD_HEAD_HEIGHT - itemHeight) / 2;
    const fontWeight = style?.fontWeight || DEFAULT_FONT_WEIGHT;
    const maxRelationContainerWidth = 9999; // 最大关联项容器宽度
    const minRelationContainerWidth = 100; // 最小关联项容器宽度
    const closeContainerWidth = 10; // 最小关联项容器宽度（实际值为在这个基础之上在+14）
    const fontSize = FONT_SIZE_SM;
    const relationHeaderMarginLeft = 12;
    let relationHeaderWidth = 14;

    let relationIdentifierMarginLeft = 4;
    const relationTitleMarginLeft = 4;
    const relationTitleMarginRight = 18;
    const relationItemHeight = 30;
    let countContainerWidth = 42 + AI_TABLE_CELL_MULTI_PADDING_LEFT;
    if (multilineRow) {
        countContainerWidth = 0;
    }

    const relationItems: RelationItemConfig[] = [];
    let moreCount: MoreCountItem | null = null;

    const textAlign = style?.textAlign || DEFAULT_TEXT_ALIGN_LEFT;

    let rowIndex = 0;
    for (const [index, relationId] of transformValue.entries()) {
        const relationInfo = references?.[field.type]?.[relationId];
        if (relationInfo) {
            const { title: titleString, type, whole_identifier, name } = relationInfo;
            if (!whole_identifier) {
                relationIdentifierMarginLeft = 0;
            }
            const relationItem: RelationItemConfig = {
                relationInfo
            };

            let tagString = '';
            let tagStringWidth = 0;

            if (headerType === RelationHeaderType.tag && field.type === WikiPluginTypes.relationObjective) {
                // relationHeaderWidth = 24;
                const { text: number, textWidth: numberWidth } = drawer.textEllipsis({
                    text: `O${relationInfo.number}`,
                    fontSize: fontSize,
                    fontWeight
                });
                tagString = number;
                tagStringWidth = numberWidth;
                relationHeaderWidth = numberWidth + 20;
            }

            // 剩余宽度
            let { relationContainerWidth, remainingWidth } = getNewRowWidth(
                columnWidth,
                currentX,
                showAddAction || false,
                maxRelationContainerWidth
            );

            // 是否显示更多计数
            let showMoreCount = false;
            if (index < transformValue.length - 1) {
                // 后续还有元素
                relationContainerWidth = Math.min(remainingWidth - countContainerWidth, maxRelationContainerWidth);

                if (relationContainerWidth < minRelationContainerWidth) {
                    // 容纳不下则 换行 或 展示count
                    if (multilineRow) {
                        rowIndex++;
                        currentX = AI_TABLE_CELL_PADDING;
                        currentY += relationItemHeight;
                        let { relationContainerWidth: newRelationContainerWidth, remainingWidth: newRemainingWidth } = getNewRowWidth(
                            columnWidth,
                            currentX,
                            showAddAction || false,
                            maxRelationContainerWidth
                        );
                        relationContainerWidth = newRelationContainerWidth;
                        remainingWidth = newRemainingWidth;
                    } else {
                        showMoreCount = true;
                    }
                }
            } else {
                // 最后一个元素
                if (remainingWidth < minRelationContainerWidth) {
                    // 容纳不下这一条，换行 或 展示count
                    if (multilineRow) {
                        rowIndex++;
                        currentX = AI_TABLE_CELL_PADDING;
                        currentY += relationItemHeight;
                        let { relationContainerWidth: newRelationContainerWidth, remainingWidth: newRemainingWidth } = getNewRowWidth(
                            columnWidth,
                            currentX,
                            showAddAction || false,
                            maxRelationContainerWidth
                        );
                        relationContainerWidth = newRelationContainerWidth;
                        remainingWidth = newRemainingWidth;
                    } else {
                        showMoreCount = true;
                    }
                }
            }

            // 不换行 需要渲染 count，则结束
            if (showMoreCount && !multilineRow) {
                // 关联项背景绘制
                const countString = `+${transformValue.length - index}`;
                const { text: countText, textWidth: countTextWidth } = drawer.textEllipsis({
                    text: countString,
                    fontSize: fontSize,
                    fontWeight
                });

                const textX = x + currentX + (countContainerWidth - countTextWidth) / 2;
                const textY = y + AI_TABLE_FIELD_HEAD_HEIGHT / 2;
                moreCount = {
                    bgRect: {
                        x: x + currentX,
                        y: y + currentY,
                        width: countContainerWidth,
                        height: itemHeight,
                        fill: drawer?.colors.gray100,
                        radius: 4
                    },
                    text: {
                        x: textX,
                        y: textY,
                        text: countString,
                        textAlign,
                        fontSize: fontSize,
                        fillStyle: drawer?.colors.gray800,
                        fontWeight,
                        textDecoration: DEFAULT_TEXT_DECORATION,
                        verticalAlign: DEFAULT_TEXT_VERTICAL_ALIGN_MIDDLE
                    }
                };
                break;
            }

            let relationTextMaxWidth =
                relationContainerWidth -
                relationHeaderMarginLeft -
                relationHeaderWidth -
                relationIdentifierMarginLeft -
                relationTitleMarginRight;

            if (showClose) {
                relationTextMaxWidth -= closeContainerWidth;
            }

            let identifierText = '';
            let identifierTextWidth = 0;
            if (whole_identifier) {
                const { text, textWidth } = drawer.textEllipsis({
                    text: whole_identifier,
                    maxWidth: relationTextMaxWidth,
                    fontSize: fontSize,
                    fontWeight
                });
                identifierText = text;
                identifierTextWidth = textWidth;
            }

            let titleText = '';
            let titleTextWidth = 0;
            const titleContainerMaxWidth = relationTextMaxWidth - identifierTextWidth - relationTitleMarginLeft;
            if (identifierTextWidth < relationTextMaxWidth && titleContainerMaxWidth > 15) {
                const titleTextInfo = drawer.textEllipsis({
                    text: titleString || name,
                    maxWidth: relationTextMaxWidth - identifierTextWidth - relationTitleMarginLeft,
                    fontSize: fontSize,
                    // maxWidth: 1,
                    fontWeight
                });
                titleText = titleTextInfo.text;
                titleTextWidth = titleTextInfo.textWidth;
            }

            let relationWidth =
                identifierTextWidth +
                titleTextWidth +
                relationTitleMarginLeft +
                relationHeaderWidth +
                relationHeaderMarginLeft +
                relationIdentifierMarginLeft +
                relationTitleMarginRight;

            if (showClose) {
                relationWidth += closeContainerWidth;
            }

            // 关联项背景绘制
            const bgRect = {
                x: x + currentX,
                y: y + currentY,
                width: relationWidth,
                height: itemHeight,
                fill: drawer?.colors.gray100,
                radius: 4
            };
            relationItem.bgRect = bgRect;

            if (headerType === RelationHeaderType.tag) {
                // 关联项 tag 绘制
                const textX = x + currentX + relationHeaderMarginLeft + (relationHeaderWidth - tagStringWidth) / 2;
                const textY = y + rowIndex * relationItemHeight + AI_TABLE_FIELD_HEAD_HEIGHT / 2;
                const tag: {
                    bgRect: AITableRect;
                    text: AITableText;
                } = {
                    bgRect: {
                        x: x + currentX + relationHeaderMarginLeft,
                        y: y + currentY + (itemHeight - 16) / 2,
                        width: relationHeaderWidth,
                        height: 16,
                        fill: hexToRgba(relationInfo.color, 0.1),
                        radius: 18
                    },
                    text: {
                        x: textX,
                        y: textY,
                        text: tagString,
                        textAlign,
                        fillStyle: relationInfo.color,
                        fontWeight,
                        fontSize: fontSize,
                        textDecoration: DEFAULT_TEXT_DECORATION as any,
                        verticalAlign: DEFAULT_TEXT_VERTICAL_ALIGN_MIDDLE as any
                    }
                };
                relationItem.tag = tag;
            }

            if (headerType === RelationHeaderType.icon) {
                // 关联项图标绘制
                const iconX = x + currentX + relationHeaderMarginLeft;
                const iconY = y + currentY + (itemHeight - relationHeaderWidth) / 2;

                // TODO: 图标后续处理
                const svgString = references.svgMap?.get(relationId) || '';
                let url = '';
                if (svgString) {
                    url = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgString)}`;
                }
                const icon = {
                    x: iconX,
                    y: iconY,
                    url,
                    width: relationHeaderWidth,
                    height: relationHeaderWidth
                };
                relationItem.icon = icon;
            }

            // 绘制 identifier 文本
            const identifierX = x + currentX + relationHeaderMarginLeft + relationHeaderWidth + relationIdentifierMarginLeft;
            const identifierY = y + AI_TABLE_FIELD_HEAD_HEIGHT / 2 + AI_TABLE_OFFSET + rowIndex * relationItemHeight;
            const identifier: AITableText = {
                x: identifierX,
                y: identifierY,
                text: identifierText,
                textAlign,
                fillStyle: drawer?.colors.gray600,
                fontWeight,
                fontSize: fontSize,
                textDecoration: DEFAULT_TEXT_DECORATION,
                verticalAlign: DEFAULT_TEXT_VERTICAL_ALIGN_MIDDLE
            };
            relationItem.identifier = identifier;

            // 绘制 title 文本
            const titleX =
                x +
                currentX +
                relationHeaderMarginLeft +
                relationHeaderWidth +
                relationIdentifierMarginLeft +
                identifierTextWidth +
                relationTitleMarginLeft;
            const titleY = y + AI_TABLE_FIELD_HEAD_HEIGHT / 2 + AI_TABLE_OFFSET + rowIndex * relationItemHeight;

            const title: AITableText = {
                x: titleX,
                y: titleY,
                text: titleText,
                textAlign,
                fillStyle: drawer?.colors.gray800,
                fontWeight,
                fontSize: fontSize,
                textDecoration: DEFAULT_TEXT_DECORATION,
                verticalAlign: DEFAULT_TEXT_VERTICAL_ALIGN_MIDDLE
            };
            relationItem.title = title;

            if (showClose) {
                const closeX = x + currentX + relationWidth - itemHeight;
                const closeY = y + currentY;
                const closeActionConfig: Partial<AITableActionIconConfig> = {
                    x: closeX,
                    y: closeY,
                    data: CloseIconPath,
                    fill: Colors.gray600,
                    hoverFill: '#ff5b57',
                    size: 12,
                    backgroundWidth: itemHeight,
                    backgroundHeight: itemHeight,
                    cornerRadius: AI_TABLE_ACTION_COMMON_RADIUS,
                    listening: true
                };
                relationItem.closeActionConfig = closeActionConfig;
            }
            currentX += relationWidth + AI_TABLE_CELL_MULTI_PADDING_LEFT;

            relationItems.push(relationItem);
        }
    }

    const offsetX = render.columnWidth - AI_TABLE_ACTION_COMMON_SIZE - AI_TABLE_ACTION_COMMON_RIGHT_PADDING;
    const offsetY = (rowHeight - AI_TABLE_ACTION_COMMON_SIZE) / 2;

    const addActionConfig: Partial<AITableActionIconConfig> = {
        x: offsetX,
        y: offsetY,
        data: AddOutlinedPath,
        fill: Colors.gray600,
        hoverFill: Colors.primary,
        backgroundWidth: AI_TABLE_ACTION_COMMON_SIZE,
        backgroundHeight: AI_TABLE_ACTION_COMMON_SIZE,
        cornerRadius: AI_TABLE_ACTION_COMMON_RADIUS,
        listening: true
    };

    const totalHeight = rowHeight + rowIndex * relationItemHeight - AI_TABLE_OFFSET * 2;

    const result = {
        relationItems,
        moreCount,
        addActionConfig: null,
        totalHeight
    };
    if (showAddAction) {
        result.addActionConfig = addActionConfig;
    }
    return result;
}

function getNewRowWidth(columnWidth: number, currentX: number, showAddAction: boolean, maxRelationContainerWidth: number) {
    let remainingWidth = columnWidth - currentX;
    // 需要 新增按钮时 减去 新增按钮的宽度
    if (showAddAction) {
        remainingWidth += -AI_TABLE_CELL_MULTI_ITEM_MARGIN_LEFT - AI_TABLE_ACTION_COMMON_SIZE - AI_TABLE_ACTION_COMMON_RIGHT_PADDING;
    } else {
        remainingWidth += -AI_TABLE_CELL_PADDING;
    }

    let relationContainerWidth = Math.min(remainingWidth, maxRelationContainerWidth);
    return { relationContainerWidth, remainingWidth };
}
