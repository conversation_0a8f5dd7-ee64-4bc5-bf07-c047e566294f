import {
    AITableFilterCondition,
    AITableFilterOperation,
    AITableReferences,
    AITableField,
    isEmpty,
    FieldBase,
    FieldOptions
} from '@ai-table/utils';
import { AITable, compareString, FieldOperable, hasIntersect, isMeetFilter } from '@ai-table/grid';
import { RelationFieldValue } from '../../types/field';
import { AITableCustomRelationInfo } from '../../types/grid';
import { RELATION_FIELD_STAT_TYPE_ITEMS } from '../../constants/field-stat';

export class RelationField extends FieldBase implements FieldOperable<string, RelationFieldValue> {
    constructor() {
        super(RELATION_FIELD_STAT_TYPE_ITEMS);
    }

    isValid(cellValue: RelationFieldValue): boolean {
        return Array.isArray(cellValue) || cellValue === null;
    }
    isMeetFilter(condition: AITableFilterCondition<string>, cellValue: RelationFieldValue, options?: FieldOptions) {
        if (options?.aiTable) {
            const references = options.aiTable.context.references();
            cellValue = cellValue?.filter(relationId => references?.[options.field.type]?.[relationId]);
        }
        switch (condition.operation) {
            case AITableFilterOperation.empty:
                return isEmpty(cellValue);
            case AITableFilterOperation.exists:
                return !isEmpty(cellValue);
            case AITableFilterOperation.in:
                return Array.isArray(condition.value) && hasIntersect(cellValue, condition.value);
            case AITableFilterOperation.nin:
                return Array.isArray(condition.value) && !hasIntersect(cellValue, condition.value);
            default:
                return isMeetFilter(condition, cellValue);
        }
    }

    compare(
        cellValue1: RelationFieldValue,
        cellValue2: RelationFieldValue,
        references: AITableReferences,
        sortKey: string,
        options: {
            aiTable: AITable;
            field: AITableField;
        }
    ): number {
        return null;
    }

    toFieldValue(
        plainText: string,
        targetField: AITableField,
        originData?: { field: AITableField; cellValue: RelationFieldValue },
        references?: AITableReferences
    ): RelationFieldValue | null {
        if (targetField.type === originData?.field?.type && originData?.cellValue) {
            return originData.cellValue;
        }
        return null;
    }

    cellFullText(transformValue: string[], field: AITableField, references?: AITableReferences): string[] {
        let fullText: string[] = [];
        if (transformValue?.length && references) {
            for (let index = 0; index < transformValue.length; index++) {
                const relationInfo = references?.[field.type]?.[transformValue[index]];
                if (relationInfo) {
                    const text = getRelationItemText(relationInfo);
                    if (text !== '') {
                        fullText.push(text);
                    }
                }
            }
        }
        return fullText;
    }
}

function getRelationItemText(relationInfo: AITableCustomRelationInfo): string {
    let text = '';
    if (relationInfo.whole_identifier) {
        text += relationInfo.whole_identifier;
        text += ' ';
    }
    if (relationInfo.title || relationInfo.name) {
        text += relationInfo.title || relationInfo.name;
    }
    return text;
}
