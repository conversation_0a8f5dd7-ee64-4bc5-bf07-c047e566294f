import { AITable } from '@ai-table/grid';
import { AI_TABLE_SERVICE_MAP } from '@ai-table/services/table.service';
import { ActionName, AITableAction } from '@ai-table/utils';
import { VIEW_ACTIONS, AIViewTable } from '@ai-table/state';

export const withRemoveView = (aiTable: AITable) => {
    const viewTable = aiTable as AIViewTable;
    const { apply } = viewTable;
    viewTable.apply = (action: AITableAction) => {
        if (VIEW_ACTIONS.includes(action.type as ActionName)) {
            if (action.type === ActionName.RemoveView) {
                const tableService = AI_TABLE_SERVICE_MAP.get(viewTable);
                let activeId = tableService?.activeViewId();
                const removeViewId = action.path[0];
                if (!activeId || activeId === removeViewId) {
                    activeId = getActiveViewId(viewTable, action as { type: ActionName; path: string[] });
                    activeId && tableService?.setActiveView(activeId);
                }
            }
        }
        apply(action);
    };
    return aiTable;
};

function getActiveViewId(aiTable: AIViewTable, action: { type: ActionName; path: string[] }) {
    const activeViewIndex = aiTable.views().findIndex(item => item._id === action.path[0]);
    if (activeViewIndex > -1) {
        if (activeViewIndex === 0) {
            return aiTable.views()[1]._id;
        } else {
            return aiTable.views()[activeViewIndex - 1]._id;
        }
    }
    return null;
}
