@use 'ngx-tethys/styles/variables.scss';
@use 'ngx-tethys/styles/basic.scss';
@use '../../../../../common/styles/variables.scss' as commonVariables;

$attachment-placeholder-width: 32px;

.wiki-table-cell-attachment--container {
    height: commonVariables.$attachment-dialog-height;

    .attachments {
        flex: 1;
        overflow: hidden;
        min-height: 100%;
    }
    .dialog-body {
        height: 100%;
        .attachment-file-header {
            height: 32px;
            padding: 0 $attachment-placeholder-width;
        }
    }
    .wiki-table-cell-editor-attachments {
        padding: 8px 0 0 0;
        overflow: hidden;

        .styx-attachments-normal {
            margin-left: $attachment-placeholder-width;
            .styx-attachments-header-operation {
                padding-right: $attachment-placeholder-width;
            }
            .attachments-table-header {
                padding-right: $attachment-placeholder-width;
            }
            .attachments-table-scroll-box {
                margin-right: $attachment-placeholder-width;
                padding-bottom: 20px;
            }
        }
    }
    // 上传时隐藏空状态
    .show-empty {
        .attachments-table-scroll-box {
            .thy-layout {
                display: none !important;
            }
        }
    }
    // 附件上传样式
    .file-upload-items {
        position: relative;
        .file-item-uploading-normal {
            .file-title {
                width: 100%;
                min-width: 128px;
            }
            .progress-operation-wrapper {
                min-width: 350px;
                .progress-wrapper {
                    justify-content: flex-start;
                    .progress {
                        width: 60%;
                    }
                }
            }
        }
    }
    .attachments-empty {
        text-align: center;
        .cloud-upload-icon {
            color: variables.$gray-200;
            font-size: 6rem;
        }
    }
    .styx-attachment-items {
        display: block;
    }
}
