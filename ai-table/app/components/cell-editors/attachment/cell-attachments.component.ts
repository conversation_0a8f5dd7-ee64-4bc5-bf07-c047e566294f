import {
    ChangeDetectorRef,
    Component,
    computed,
    ElementRef,
    HostBinding,
    inject,
    model,
    Input,
    OnInit,
    signal,
    WritableSignal,
    input
} from '@angular/core';
import {
    AttachmentEditorEventData,
    AttachmentEntity,
    AUDIO_PREVIEW_EXT_NAMES,
    DriveEntity,
    HasPermissionPipe,
    IMAGE_EXT_NAMES,
    NgxStyxSharedModule,
    openFileEditor,
    STYX_ATTACHMENT_UPDATABLE,
    StyxFileAttachmentsComponent,
    StyxFileUploader,
    StyxTranslateService,
    UtilService,
    VIDEO_PREVIEW_EXT_NAMES,
    WikiBroadObjectTypes
} from '@atinc/ngx-styx';
import { ThyButton } from 'ngx-tethys/button';
import { ThyDialogModule } from 'ngx-tethys/dialog';

import { ThyLayoutModule } from 'ngx-tethys/layout';
import { PageAttachmentEntity, TheExtensionMode } from '@wiki/common/interface';
import { ThyUploadModule, ThyUploadResponse, ThyUploadStatus } from 'ngx-tethys/upload';
import { ThyIconModule } from 'ngx-tethys/icon';
import { TranslateModule } from '@ngx-translate/core';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { AttachmentContext } from '@wiki/common/services/context/attachment-context';
import { WIKI_ATTACHMENT_CONTEXT } from '@wiki/common/services/context/attachment-context.token';
import { PageStore } from '@wiki/common/stores/page.store';
import { DialogDispatcher } from '@wiki/common/services';
import { I18nSourceDefinitionType } from '../../../../../src/app/constants/i18n-source';
import { nextTick } from 'process';
import { THE_MODE_TOKEN, TheModeConfig } from '@worktile/theia';
import { isSharedMode } from '@wiki/common/util/extension-mode';
import { ThyEmptyModule } from 'ngx-tethys/empty';
import { attachmentTab, AttachmentType } from '../../../../../src/app/constants';
import { ThyMenuModule } from 'ngx-tethys/menu';

/**
 * @private
 */
@Component({
    selector: 'edit-attachment',
    templateUrl: './cell-attachments.component.html',
    imports: [
        ThyLayoutModule,
        ThyDialogModule,
        ThyUploadModule,
        ThyIconModule,
        TranslateModule,
        StyxFileAttachmentsComponent,
        NgxStyxSharedModule,
        ThyButton,
        ThyEmptyModule,
        ThyMenuModule
    ],
    providers: [
        {
            provide: WIKI_ATTACHMENT_CONTEXT,
            useExisting: WikiPluginContext
        },
        HasPermissionPipe,
        DialogDispatcher,
        {
            provide: STYX_ATTACHMENT_UPDATABLE,
            useFactory: () => {
                return inject(DialogDispatcher);
            }
        }
    ]
})
export class CellAttachmentsComponent implements OnInit {
    @HostBinding('class') class = 'thy-dialog-content wiki-table-cell-attachment--container';

    get isSharedMode() {
        return isSharedMode(this.modeConfig?.mode as TheExtensionMode);
    }

    private fileUploader = inject(StyxFileUploader);
    private util = inject(UtilService);
    private pageStore = inject(PageStore);
    private attachmentContext = inject<AttachmentContext>(WIKI_ATTACHMENT_CONTEXT);
    private cdr = inject(ChangeDetectorRef);
    private hasPermission = inject(HasPermissionPipe);
    private translate = inject(StyxTranslateService);
    private dialogDispatcher = inject<DialogDispatcher>(STYX_ATTACHMENT_UPDATABLE);
    public elementRef = inject(ElementRef);
    private modeConfig = inject<TheModeConfig>(THE_MODE_TOKEN);

    readonly = input.required<boolean>();

    attachments = model.required<PageAttachmentEntity[]>();

    @Input() updateValue: (attachments: PageAttachmentEntity[]) => void;

    displayAttachments = computed<PageAttachmentEntity[]>(() => {
        return this.attachments().filter(item => this.attachmentExtFilter[this.activeTab()?.type](item.addition.ext));
    });

    get page() {
        return this.pageStore?.snapshot?.page;
    }

    get scopeId() {
        return `page-attachments-${this.attachmentContext.attachmentOriginId()}`;
    }

    apiPrefix: string = `/api/wiki/pages/${this.page._id}`;

    tableHeight = 480 - 96;

    uploading = false;

    protected activeTab: WritableSignal<attachmentTab> = signal(null);

    protected attachmentTabs: attachmentTab[] = [
        {
            title: this.translate.instant<I18nSourceDefinitionType>('styx.all'),
            type: AttachmentType.whole
        },
        {
            title: this.translate.instant<I18nSourceDefinitionType>('styx.file', { isPlural: false }),
            type: AttachmentType.file
        },
        {
            title: this.translate.instant<I18nSourceDefinitionType>('wiki.attachments.tabs.picture'),
            type: AttachmentType.picture
        },
        {
            title: this.translate.instant<I18nSourceDefinitionType>('wiki.attachments.tabs.audio'),
            type: AttachmentType.audio
        },
        {
            title: this.translate.instant<I18nSourceDefinitionType>('wiki.attachments.tabs.video'),
            type: AttachmentType.video
        }
    ];

    constructor() {}

    ngOnInit() {
        this.activeTab.set(this.attachmentTabs[0]);
        this.setUpdateDialogAttachments();
    }

    close() {
        this.util.dialog.close();
    }

    updateEditAttachments(attachments: PageAttachmentEntity[]) {
        this.attachments.set(attachments);
        nextTick(() => {
            this.updateValue(attachments);
            this.cdr.detectChanges();
        });
    }

    setActiveTab(tab: attachmentTab) {
        this.activeTab.set(tab);
    }

    private attachmentExtFilter = {
        [AttachmentType.whole]: (_: string) => true,
        [AttachmentType.file]: (ext: string) =>
            ![...IMAGE_EXT_NAMES, ...VIDEO_PREVIEW_EXT_NAMES, ...AUDIO_PREVIEW_EXT_NAMES].includes(ext.toLowerCase()),
        [AttachmentType.picture]: (ext: string) => IMAGE_EXT_NAMES.includes(ext.toLowerCase()),
        [AttachmentType.video]: (ext: string) => VIDEO_PREVIEW_EXT_NAMES.includes(ext.toLowerCase()),
        [AttachmentType.audio]: (ext: string) => AUDIO_PREVIEW_EXT_NAMES.includes(ext.toLowerCase())
    };

    addAttachmentsToPage(attachment: DriveEntity) {
        this.attachmentContext.addAttachment(attachment).subscribe((data: AttachmentEntity) => {
            this.uploading = false;
            this.updateEditAttachments([...this.attachments(), data]);
        });
    }

    uploadAttachments(event: { files: File[] }) {
        if (this.readonly() || !this.hasPermission.transform(this.pageStore.snapshot.page.permissions, 'page_edit')) {
            this.util.notify.error(this.translate.instant<I18nSourceDefinitionType>('styx.editorPermissionDeny'));
            return;
        }
        this.uploading = true;
        let uploadStatus: ThyUploadStatus;
        this.fileUploader
            .uploadAnonymousFiles(this.scopeId, event.files, {
                keepInQueue: false,
                onDone: this.addAttachmentsToPage.bind(this),
                appendProcess: true
            })
            .subscribe({
                next: (file: ThyUploadResponse) => {
                    uploadStatus = file.status;
                },
                error: error => {
                    console.log('error', error);
                    this.util.defaultErrorHandler()(error);
                },
                complete: () => {
                    // 手动取消上传时，上传状态是 uploading，没有 done 要结束上传 uploading
                    if (uploadStatus !== ThyUploadStatus.done) {
                        this.uploading = false;
                    }
                }
            });
        this.scrollToLatest();
    }

    openFileEditor(event: AttachmentEditorEventData) {
        const attachment = this.attachments().find(item => item._id === event.attachment_id);
        openFileEditor({
            principalType: WikiBroadObjectTypes.page,
            principalId: this.page._id,
            attachmentId: attachment._id,
            fileEntity: attachment
        });
    }

    scrollToLatest() {
        const scroll = (this.elementRef.nativeElement as HTMLElement).querySelector('.attachments-table-scroll');
        if (scroll) {
            setTimeout(() => {
                scroll.scrollTop = scroll.scrollHeight;
            });
        }
    }

    setUpdateDialogAttachments() {
        this.dialogDispatcher.updateDialogAttachments = (attachments: AttachmentEntity[]) => {
            this.updateEditAttachments(attachments);
        };
    }
}
