@if (attachments().length > 0) {
  <thy-layout>
    <thy-sidebar thyHasBorderRight="true" thyTheme="light" class="h-100" thyWidth="260">
      <thy-sidebar-header styxI18nTracking [thyTitle]="'wiki.page.attachment.name' | translate"></thy-sidebar-header>
      <thy-sidebar-content>
        <thy-menu thyTheme="loose" class="pt-0">
          @for (tab of attachmentTabs; track tab.type) {
            <a href="javascript:;" thyMenuItem [class.active]="tab.type === activeTab()?.type" (click)="setActiveTab(tab)">
              <span thyMenuItemName>{{ tab.title }}</span>
            </a>
          }
        </thy-menu>
      </thy-sidebar-content>
    </thy-sidebar>
    <thy-layout>
      <thy-dialog-header [thyTitle]="activeTab()?.title" (thyOnClose)="close()"></thy-dialog-header>
      <thy-dialog-body class="wiki-table-cell-editor-attachments styx-file-items">
        <div
          class="attachments"
          thyFileDrop
          styxFileUploadPaste
          (thyOnDrop)="uploadAttachments($event)"
          (styxSelectFileChange)="uploadAttachments($event)"
        >
          <div class="attachment-file-header d-flex align-items-center">
            <div
              class="text-desc"
              styxI18nTracking
              [innerHTML]="'wiki.page.attachment.count' | translate: { count: displayAttachments().length }"
            ></div>
            @if ((page.permissions | hasPermission: 'page_edit') && !readonly()) {
              <div class="ml-auto operations">
                <thy-file-select thyStopPropagation (thyOnFileSelect)="uploadAttachments($event)" [thyMultiple]="true">
                  <button thyButton="primary" thySize="md" styxI18nTracking>
                    <thy-icon thyIconName="cloud-upload"></thy-icon> {{ 'wiki.page.attachment.upload' | translate }}
                  </button>
                </thy-file-select>
              </div>
            }
          </div>
          <styx-file-attachments
            #fileAttachmentsComponent
            [styxAttachments]="displayAttachments()"
            [styxScopeId]="scopeId"
            [styxDisplayMode]="'normal'"
            [styxDisablePreview]="isSharedMode"
            [styxApiPrefix]="apiPrefix"
            [styxHeight]="tableHeight"
            [styxCheckable]="false"
            [styxShowEmpty]="true"
            [styxVersion]="false"
            [styxDownloadLog]="true"
            [styxCollapsible]="false"
            [styxDeletable]="(page.permissions | hasPermission: 'page_edit') && !readonly()"
            [styxEditable]="(page.permissions | hasPermission: 'page_edit') && !readonly()"
            [styxOfficeEditEnabled]="(page.permissions | hasPermission: 'page_edit') && !readonly()"
            (styxOpenFileEditor)="openFileEditor($event)"
          >
          </styx-file-attachments>
        </div>
      </thy-dialog-body>
    </thy-layout>
  </thy-layout>
} @else {
  <thy-dialog-header styxI18nTracking [thyTitle]="'wiki.page.attachment.name' | translate" (thyOnClose)="close()"></thy-dialog-header>
  <thy-layout
    class="attachments-empty d-flex justify-content-center"
    thyFileDrop
    styxFileUploadPaste
    (thyOnDrop)="uploadAttachments($event)"
    (styxSelectFileChange)="uploadAttachments($event)"
  >
    @if ((page.permissions | hasPermission: 'page_edit') && !readonly()) {
      <thy-icon thyIconName="cloud-upload-fill" class="cloud-upload-icon d-block"></thy-icon>
      <div class="text-desc mb-4">
        <span translate="wiki.page.attachment.dragHint"></span>
      </div>
      <div>
        <thy-file-select thyStopPropagation (thyOnFileSelect)="uploadAttachments($event)" [thyMultiple]="true">
          <button thyButton="primary-square" thySize="md" translate="wiki.page.attachment.upload"></button>
        </thy-file-select>
      </div>
    } @else {
      <thy-layout class="attachments-empty d-flex justify-content-center">
        <thy-empty></thy-empty>
      </thy-layout>
    }
  </thy-layout>
}
