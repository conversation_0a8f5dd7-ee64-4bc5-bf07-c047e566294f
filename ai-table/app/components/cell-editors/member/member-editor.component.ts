import { AbstractEditCellEditor } from '@ai-table/grid';
import { AITableMemberField } from '../../../types/field';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, Signal, signal, viewChild, WritableSignal } from '@angular/core';
import {
    MemberItem,
    PropertyItemMemberQuickEditorComponent,
    PropertyItemMembersQuickEditorNextComponent,
    UID,
    UserInfo
} from '@atinc/ngx-styx';
import { ThyClickDispatcher } from 'ngx-tethys/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MemberFieldValue } from '@ai-table/utils';

@Component({
    selector: 'member-cell-editor',
    templateUrl: './member-editor.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'd-block h-100 member-cell-editor'
    },
    imports: [PropertyItemMembersQuickEditorNextComponent, PropertyItemMemberQuickEditorComponent]
})
export class MemberCellEditorComponent extends AbstractEditCellEditor<MemberFieldValue, AITableMemberField> {
    isMultiple = computed(() => {
        return !!this.field().settings.is_multiple;
    });

    memberValue = computed(() => {
        if (this.isMultiple()) {
            return this.modelValue;
        } else {
            return this.modelValue?.length > 0 ? this.modelValue[0] : null;
        }
    });

    destroyRef = inject(DestroyRef);

    clickDispatcher = inject(ThyClickDispatcher);

    quickEditor = viewChild<PropertyItemMembersQuickEditorNextComponent>('quickEditor');

    override ngOnInit(): void {
        super.ngOnInit();
        this.clickDispatcher
            .clicked(0)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((event: Event) => {
                if (this.quickEditor().canClose(event)) {
                    this.closePopover();
                }
            });
    }

    valueChange(data: { members: UID[] } | { member?: UserInfo }): void {
        if (this.isMultiple()) {
            this.updateFieldValues.emit([
                {
                    value: (data as { members: UID[] }).members,
                    path: [this.record()._id, this.field()._id]
                }
            ]);
        } else {
            if ((data as { member?: UserInfo }).member) {
                this.updateFieldValues.emit([
                    {
                        value: [(data as { member: UserInfo }).member.uid],
                        path: [this.record()._id, this.field()._id]
                    }
                ]);
            } else {
                this.updateFieldValues.emit([
                    {
                        value: [],
                        path: [this.record()._id, this.field()._id]
                    }
                ]);
            }
        }
    }
}
