import { AbstractEditCellEditor, AITableQueries, FieldModelMap } from '@ai-table/grid';
import { AIT<PERSON><PERSON>ield, RichTextFieldValue } from '@ai-table/utils';
import { ChangeDetectionStrategy, Component, computed, inject, Input, input } from '@angular/core';
import { Thy<PERSON><PERSON>og<PERSON><PERSON>, ThyDialogFooter, ThyDialogHeader } from 'ngx-tethys/dialog';
import { createEmptyParagraph, THE_PRESET_CONFIG_TOKEN, TheDataMode, TheEditorComponent, TheOptions } from '@worktile/theia';
import {
    ApplicationContext,
    AppRootContext,
    helpers,
    MentionType,
    MentionPluginKey,
    StyxEditorPresetConfigFactory,
    StyxRichTextToolbar,
    StyxTranslateParserPipe,
    StyxTranslateService,
    UtilService
} from '@atinc/ngx-styx';
import { ThyButton } from 'ngx-tethys/button';
import { FormsModule } from '@angular/forms';

@Component({
    selector: 'cell-richtext',
    templateUrl: './cell-richtext.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'h-100 cell-richtext'
    },
    imports: [
        TheEditorComponent,
        ThyDialogHeader,
        ThyDialogBody,
        ThyDialogFooter,
        TheEditorComponent,
        FormsModule,
        ThyButton,
        StyxTranslateParserPipe
    ],
    providers: [
        {
            provide: THE_PRESET_CONFIG_TOKEN,
            useFactory: (applicationContext: ApplicationContext, appRootContext: AppRootContext, translate: StyxTranslateService) => {
                const config = StyxEditorPresetConfigFactory(applicationContext, appRootContext, translate);
                config.options.mode = TheDataMode.json;
                config.plugins = config.plugins.filter(
                    item => ![MentionPluginKey.workItem, MentionPluginKey.mention].includes(item.key as MentionPluginKey)
                );
                return config;
            },
            deps: [ApplicationContext, AppRootContext, StyxTranslateService]
        }
    ]
})
export class CellRichtextComponent extends AbstractEditCellEditor<RichTextFieldValue, AITableField> {
    util = inject(UtilService);

    private applicationContext = inject(ApplicationContext);

    private translate = inject(StyxTranslateService);

    readonly = input.required<boolean>();

    @Input() updateValue: (richText: string) => void;

    editOptions = computed<TheOptions>(() => {
        const toolbarGlobal = ['ai', ...StyxRichTextToolbar(this.applicationContext)];
        const toolbarGlobalFilter = helpers.filter(
            toolbarGlobal,
            item => ![MentionType.mention, MentionType.workItem].includes(item as MentionType)
        );
        return {
            mode: TheDataMode.json,
            placeholder: this.translate.instant('styx.enterContent'),
            readonly: this.readonly(),
            scrollContainer: '.dialog-body',
            toolbar: {
                global: toolbarGlobalFilter
            }
        };
    });

    ngOnInit() {
        super.ngOnInit();
        this.modelValue = computed(() => {
            let value = AITableQueries.getFieldValue(this.aiTable, [this.record()._id, this.field()._id]);
            const richTextField = FieldModelMap[this.field().type];
            if (!richTextField.isValid(value) || !value || (helpers.isArray(value) && value.length === 0)) {
                value = [createEmptyParagraph()];
            }
            return value;
        })();
    }

    close() {
        this.util.dialog.close();
    }

    override update() {
        super.update();
        this.util.dialog.close();
    }
}
