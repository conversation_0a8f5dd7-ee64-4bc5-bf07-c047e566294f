<thy-dialog-header styxI18nTracking [thyTitle]="field().name" (thyOnClose)="close()"></thy-dialog-header>

<thy-dialog-body class="py-0">
  <the-editor class="styx-textarea-cell-editor" name="description" [(ngModel)]="modelValue" [theOptions]="editOptions()"></the-editor>
</thy-dialog-body>

<thy-dialog-footer>
  <button thyButton="link-secondary" [disabled]="readonly()" (click)="close()" translate="common.cancel"></button>
  <button thyButton="primary" [disabled]="readonly()" styxI18nTracking [thyLoadingText]="'common.saving' | translate" (click)="update()">
    {{ 'common.save' | translate }}
  </button>
</thy-dialog-footer>
