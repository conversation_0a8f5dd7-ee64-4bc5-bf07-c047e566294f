import { TableService } from '@ai-table/services/table.service';
import { Actions, addView, removeView } from '@ai-table/state';
import { AITableView, AITableViewFields, AITableViewRecords } from '@ai-table/utils';
import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, TemplateRef, viewChild } from '@angular/core';
import {
    StyxAwesomeTextComponent,
    StyxAwesomeTextInputEvent,
    StyxConfirmService,
    StyxI18nTrackingDirective,
    StyxTranslateService,
    UtilService
} from '@atinc/ngx-styx';
import { TranslateModule } from '@ngx-translate/core';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { ThyAction } from 'ngx-tethys/action';
import {
    ThyDropdownDirective,
    ThyDropdownMenuComponent,
    ThyDropdownMenuItemDirective,
    ThyDropdownMenuItemNameDirective
} from 'ngx-tethys/dropdown';
import { ThyIcon } from 'ngx-tethys/icon';
import { ThyNav, ThyNavItemDirective } from 'ngx-tethys/nav';
import { ThyNotifyService } from 'ngx-tethys/notify';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { helpers } from 'ngx-tethys/util';
import { of } from 'rxjs';

@Component({
    selector: 'ai-table-views, [aiTableViews]',
    templateUrl: './views.component.html',
    host: {
        class: 'ai-table-views'
    },
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        ThyIcon,
        ThyAction,
        ThyNav,
        ThyNavItemDirective,
        ThyDropdownDirective,
        ThyDropdownMenuComponent,
        ThyDropdownMenuItemDirective,
        ThyDropdownMenuItemNameDirective,
        StyxAwesomeTextComponent,
        ThyDropdownMenuComponent,
        NgTemplateOutlet,
        TranslateModule,
        StyxI18nTrackingDirective
    ]
})
export class AITableViews {
    private notifyService = inject(ThyNotifyService);

    private translate = inject(StyxTranslateService);

    private nav = viewChild<ThyNav>('nav');

    private util = inject(UtilService);

    private thyPopover = inject(ThyPopover);

    private popoverRef: ThyPopoverRef<any>;

    private confirm = inject(StyxConfirmService);

    openedMoreViewId = '';

    editViewId = '';

    activeColor = '#3DD1AE';

    pauseReCalculate = false;

    public tableService = inject(TableService);

    public maxViews = 100;

    viewsMap = computed(() => {
        return helpers.keyBy(this.tableService.views(), '_id');
    });

    showExtraDivider = computed(() => {
        const activeViewId = this.tableService.activeViewId();
        const views = this.tableService.sortedViews() || [];
        return views.length > 0 && (this.nav().showMore() || activeViewId !== views[views.length - 1]._id);
    });

    viewChange(event: Event, item: AITableView) {
        this.editViewId = '';
        this.pauseReCalculate = false;
        if ((event.target as HTMLElement).closest('.more-icon')) {
            return;
        }
        this.tableService.setActiveView(item._id);
    }

    addView(type: 'add' | 'duplicate', viewId?: string) {
        const newView = addView(this.tableService.aiTable, type, viewId);
        if (newView) {
            this.tableService.setActiveView(newView._id);
        }
    }

    renameView(viewId: string) {
        this.pauseReCalculate = true;
        this.editViewId = viewId;
    }

    viewEditBlur(data: StyxAwesomeTextInputEvent, viewId: string) {
        const existViewName = this.tableService
            .sortedViews()
            .filter(item => item._id !== viewId)
            .map(item => item.name.trim());
        if (existViewName.includes(data.value.trim())) {
            this.notifyService.warning(this.translate.instant<I18nSourceDefinitionType>('wiki.aiTable.viewNameExist'));
            return;
        }

        this.editViewId = '';
        const view = this.tableService.sortedViews().find(item => item._id === viewId);
        if (data.value !== view.name || data.emoji !== view.emoji_icon) {
            Actions.setView(this.tableService.aiTable, { name: data.value, emoji_icon: data.emoji }, [viewId]);
        }

        this.pauseReCalculate = false;
    }

    viewEmojiChange(emoji: string, viewId: string) {
        const view = this.tableService.sortedViews().find(item => item._id === viewId);
        if (emoji !== view.emoji_icon) {
            Actions.setView(this.tableService.aiTable, { emoji_icon: emoji }, [viewId]);
        }
    }

    removeView(view: AITableView) {
        this.confirm.delete({
            definitionName: this.translate.instant<I18nSourceDefinitionType>('styx.view', { isPlural: false }),
            target: view.name,
            description: null,
            action: () => {
                const records = this.tableService.aiTable.records() as AITableViewRecords;
                const fields = this.tableService.aiTable.fields() as AITableViewFields;
                removeView(this.tableService.aiTable, records, fields, view._id);
                this.util.popover.closeAll();
                return of(true);
            }
        });
    }

    openMenu(event: Event, menu: TemplateRef<any>, viewId: string) {
        event.preventDefault();
        event.stopPropagation();

        if (this.openedMoreViewId === viewId) {
            this.popoverRef.close();
        }
        this.openedMoreViewId = viewId;

        this.popoverRef = this.thyPopover.open(menu, {
            origin: (event.target as HTMLElement).closest('.thy-action') as HTMLElement,
            placement: 'bottomRight',
            hasBackdrop: false,
            outsideClosable: true,
            insideClosable: true
        });

        this.popoverRef.afterClosed().subscribe(() => {
            this.openedMoreViewId = '';
        });
    }
}
