@use 'ngx-tethys/styles/variables.scss';


.views-card {
    .view-awesome-text {
        .styx-awesome-text-content {
            color: variables.$gray-700;
        }
    }
    .thy-nav-extra {
        justify-content: flex-start;
        position: relative;
        margin-left: 12px;
    }
    &.show-extra-divider {
        .thy-nav-extra {
            &::before {
                content: '';
                position: absolute;
                right: calc(100% + 12px);
                top: 50%;
                transform: translateY(-50%);
                display: inline-block;
                height: 1rem;
                vertical-align: middle;
                border-left: 1px solid variables.$gray-300;
            }
        }
    }
    .thy-nav-item {
        &.thy-nav-more-container {
            color: variables.$gray-700;
            &.thy-nav-origin-active,
            &:hover,
            &:active,
            &.active {
                color: variables.$primary;
            }
        }
        .styx-awesome-text {
            margin-left: -6px;
            .styx-awesome-text-content {
                max-width: 170px;
            }
        }
        &:not(.thy-nav-more-container) {
            &.active {
                padding-right: 14px;
                .more {
                    color: variables.$secondary;
                    &:hover,
                    &.active {
                        color: variables.$primary;
                    }
                }
            }
        }
    }
}

.more-views-container {
    max-height: 360px;
    overflow-y: scroll;
    margin-bottom: 12px;
    &.thy-dropdown-menu {
        padding-bottom: 0;
    }
}

.view-item-in-more {
    &.dropdown-menu-item {
        padding: 4px 8px 4px 16px;
        &.editing {
            padding: 4px 10px;
            &:hover {
                background-color: unset;
            }
        }
    }
    .styx-awesome-text {
        height: 32px;
        .styx-awesome-text-content {
            max-width: 180px;
        }
    }
    &.with-more-active {
        background-color: variables.$gray-100;
        .check-icon {
            display: none;
        }
    }
    .check-icon {
        display: inline-block;
        width: 32px;
        text-align: center;
    }
    .more-icon {
        &.active {
            display: inline-block;
        }
        &:not(.active) {
            display: none;
        }
    }
    &:hover {
        .check-icon {
            display: none;
        }
        .more-icon {
            display: inline-block;
        }
    }
}

.delete-view {
    &:hover {
        .thy-icon {
            color: variables.$danger !important;
        }
        color: variables.$danger !important;
    }
}
