<thy-nav
  #nav
  class="views-card"
  [class.show-extra-divider]="showExtraDivider()"
  thyType="card"
  [thyResponsive]="true"
  [thyPauseReCalculate]="pauseReCalculate"
  [thyPopoverOptions]="{
    insideClosable: false,
    outsideClosable: true,
    manualClosure: true,
    hasBackdrop: true
  }"
>
  @for (view of tableService.sortedViews(); track view._id; let idx = $index) {
    @let viewId = view._id;
    @let isActive = tableService.activeViewId() === viewId;

    <a thyNavItem class="cursor-pointer" [id]="viewId" [thyNavItemActive]="isActive" (click)="viewChange($event, view)">
      <styx-awesome-text
        class="view-awesome-text"
        [styxText]="view.name"
        [styxTextRequired]="true"
        [styxIcon]="view?.emoji_icon ? view.emoji_icon : 'ai-grid'"
        [styxCode]="view?.emoji_icon"
        [styxIconColor]="isActive ? activeColor : null"
        [styxAppendTemplate]="isActive ? append : null"
        [styxAutofocus]="true"
        [styxTooltips]="view.name"
        [styxEdit]="viewId === editViewId"
        (styxBlur)="viewEditBlur($event, viewId)"
        (styxEmojiChange)="viewEmojiChange($event, viewId)"
      >
        <ng-template #append>
          @if (!tableService.readonly()) {
            <thy-icon
              class="pl-2 more"
              thyIconName="more-vertical"
              thyActiveClass="active"
              [thyDropdown]="menu"
              thyPlacement="bottomLeft"
            ></thy-icon>
          }
        </ng-template>

        <thy-dropdown-menu #menu>
          <ng-template [ngTemplateOutlet]="menuContent" [ngTemplateOutletContext]="{ view: view }" ]></ng-template>
        </thy-dropdown-menu>
      </styx-awesome-text>
    </a>
  }

  <ng-template #morePopover let-context>
    <thy-dropdown-menu thyImmediateRender class="more-views-container">
      @for (item of context; track $index) {
        @let view = viewsMap()[item.id()] || {};
        @let viewId = view._id;
        @let isActive = tableService.activeViewId() === viewId;
        <a
          thyDropdownMenuItem
          class="view-item-in-more cursor-pointer"
          [class.editing]="viewId === editViewId"
          [class.with-more-active]="viewId === openedMoreViewId"
          (click)="viewChange($event, view)"
        >
          <styx-awesome-text
            [styxText]="view?.name"
            [styxTextRequired]="true"
            [styxIcon]="view?.emoji_icon ? view.emoji_icon : 'ai-grid'"
            [styxCode]="view?.emoji_icon"
            [styxTooltips]="view.name"
            [styxAppendTemplate]="append"
            [styxAutofocus]="true"
            [styxEdit]="viewId === editViewId"
            (styxBlur)="viewEditBlur($event, viewId)"
            (styxEmojiChange)="viewEmojiChange($event, viewId)"
          >
            <ng-template #append>
              @if (isActive) {
                <div class="check-icon"><thy-icon class="text-primary" thyIconName="check"></thy-icon></div>
              }
              @if (!tableService.readonly()) {
                <a
                  href="javascript:;"
                  class="more-icon"
                  [class.active]="viewId === openedMoreViewId"
                  thyAction
                  thyActionIcon="more-vertical"
                  thyActiveClass="active"
                  (click)="openMenu($event, menuTemplate, viewId)"
                ></a>
              }
            </ng-template>

            <ng-template #menuTemplate>
              <thy-dropdown-menu thyImmediateRender>
                <ng-template [ngTemplateOutlet]="menuContent" [ngTemplateOutletContext]="{ view: view }" ]></ng-template>
              </thy-dropdown-menu>
            </ng-template>
          </styx-awesome-text>
        </a>
      }
    </thy-dropdown-menu>
  </ng-template>
  @if (!tableService.readonly()) {
    <ng-template #extra>
      <div class="views-nav-add">
        <a
          href="javascript:;"
          thyAction
          thyIcon="plus"
          [thyDisabled]="tableService.views()?.length >= maxViews"
          (click)="addView('add')"
        ></a>
      </div>
    </ng-template>
  }
</thy-nav>

<ng-template #menuContent let-view="view">
  <a thyDropdownMenuItem href="javascript:;" (mousedown)="renameView(view?._id)">
    <thy-icon thyIconName="rename"></thy-icon>
    <span thyDropdownMenuItemName styxI18nTracking>{{ 'common.rename' | translate }}</span>
  </a>
  <a
    thyDropdownMenuItem
    href="javascript:;"
    [thyDisabled]="tableService.views()?.length >= maxViews"
    (click)="addView('duplicate', view?._id)"
  >
    <thy-icon thyIconName="copy"></thy-icon>
    <span thyDropdownMenuItemName styxI18nTracking>{{ 'wiki.aiTable.duplicateView' | translate }}</span>
  </a>
  @if (tableService.views()?.length > 1) {
    <a thyDropdownMenuItem href="javascript:;" class="delete-view" (click)="removeView(view)">
      <thy-icon thyIconName="trash"></thy-icon>
      <span thyDropdownMenuItemName styxI18nTracking>{{ 'common.delete' | translate }}</span>
    </a>
  }
</ng-template>
