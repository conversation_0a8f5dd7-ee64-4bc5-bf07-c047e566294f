import { TableService } from '@ai-table/services/table.service';
import { Actions, sortRecordsBySortInfo } from '@ai-table/state';

import {
    toAITableFilterConditions,
    toStyxFilterConditions,
    toStyxPropertyInfos,
    toStyxPropertyOperations,
    UserReferenceInfo
} from '@ai-table/utils/property';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    effect,
    inject,
    Injector,
    OnInit,
    output,
    untracked,
    viewChild
} from '@angular/core';
import {
    AppRootContext,
    GlobalUsersStore,
    helpers,
    QueryCondition,
    QuerySearchInfo,
    SortMenuInfo,
    StyxFilterComponent,
    StyxSearchComponent,
    StyxViewQueryService
} from '@atinc/ngx-styx';
import { StyxContentHeaderComponent } from '@atinc/ngx-styx/layout';
import { ThyLayout } from 'ngx-tethys/layout';
import { ThyPopoverConfig } from 'ngx-tethys/popover';
import { StyxCombinationSort, StyxSortInfo } from '../combination-sort/combination-sort.component';
import { of } from 'rxjs';
import * as _lodash from 'lodash';
import { SpaceStore } from '@wiki/app/stores/space.store';
import { TableMode } from '@ai-table/types';
import { EXCLUDING_SORT_FIELD_TYPES } from '../../constants';
import {
    AITableAction,
    AITableFieldType,
    AITableFilterConditions,
    AITableFilterLogical,
    AITableSearchOptions,
    AITableSortOptions,
    SortDirection
} from '@ai-table/utils';
import { buildSetRecordPositionsActon } from '@ai-table/state';

@Component({
    selector: 'ai-table-operation, [aiTableOperation]',
    templateUrl: './operation.component.html',
    host: {
        class: 'd-block ai-table-operation'
    },
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [ThyLayout, StyxContentHeaderComponent, StyxSearchComponent, StyxFilterComponent, StyxCombinationSort]
})
export class AITableOperation implements OnInit {
    searchChange = output<QuerySearchInfo>();

    popoverOptions: Partial<ThyPopoverConfig> = { placement: 'bottomRight', width: '240px', insideClosable: false };

    sortMenus: SortMenuInfo[];

    filterElement = viewChild<StyxFilterComponent>('filter');

    sortElement = viewChild<StyxCombinationSort>('sort');

    public styxViewQueryService = inject(StyxViewQueryService);

    public tableService = inject(TableService);

    public injector = inject(Injector);

    conditions = computed<AITableFilterConditions>(() => {
        const activeView = this.tableService.activeView();
        const { condition_logical, conditions } = activeView.settings || {};
        if (conditions) {
            return {
                condition_logical: condition_logical || AITableFilterLogical.and,
                conditions
            };
        } else {
            return {};
        }
    });

    sorts = computed<StyxSortInfo[]>(() => {
        return (this.tableService.activeView()?.settings?.sorts || []) as unknown as StyxSortInfo[];
    });

    keywords = computed<string>(() => {
        const activeView = this.tableService.activeView();
        return activeView.settings?.keywords || '';
    });

    fields = computed(() => {
        return toStyxPropertyInfos(
            this.tableService.fields().filter(field => !EXCLUDING_SORT_FIELD_TYPES.includes(field.type as AITableFieldType))
        );
    });

    private usersData: UserReferenceInfo = {
        meUID: '',
        usersWithDepartment: [],
        usersWithRole: []
    };

    constructor() {
        if (this.tableService.mode() && this.tableService.mode() !== TableMode.justShow) {
            const appRootContext = inject(AppRootContext);
            const spaceStore = inject(SpaceStore);
            const globalUsersStore = inject(GlobalUsersStore);

            this.usersData = {
                meUID: appRootContext?.me?.uid,
                usersWithDepartment: globalUsersStore.snapshot.teamUsers || [],
                usersWithRole: [...((spaceStore?.snapshot?.pilot?.members as { uid?: string; role_ids?: string[] }[]) || [])]
            };
        }

        effect(() => {
            this.initializeViewQuery();
        });

        effect(() => {
            if (this.conditions()) {
                const fieldMap = this.tableService.aiTable.fieldsMap();
                const queryConditions = toAITableFilterConditions(
                    this.styxViewQueryService.queryStore.snapshot.configs.conditions as QueryCondition[],
                    fieldMap,
                    this.usersData
                );
                if (!_lodash.isEqual(queryConditions, this.conditions())) {
                    this.styxViewQueryService.queryStore.updateConfig({
                        conditions: toStyxFilterConditions(this.conditions())
                    });
                }
            }
        });

        effect(() => {
            const viewQueryKeywords = this.styxViewQueryService.queryStore.snapshot.configs?.search?.keywords;
            if (!_lodash.isEqual(this.keywords(), viewQueryKeywords)) {
                this.styxViewQueryService.queryStore.updateConfig({
                    search: { keywords: this.keywords() }
                });
            }
        });

        effect(() => {
            const activeViewId = this.tableService.activeViewId();
            if (activeViewId) {
                untracked(() => {
                    const innerSorts = this.sortElement()?.innerSorts() || [];
                    if (!_lodash.isEqual(innerSorts, this.sorts())) {
                        this.sortElement()?.updateInnerSorts(this.sorts());
                    }
                });
            }
        });
    }

    sortChange = (sorts: StyxSortInfo[]) => {
        if (!_lodash.isEqual(sorts, this.tableService.activeView().settings?.sorts)) {
            const settings: Partial<AITableFilterConditions & AITableSortOptions> = {
                ...(this.tableService.activeView().settings || {}),
                sorts: helpers.cloneDeep(sorts) as any
            };
            if (sorts.length === 0 && settings.is_keep_sort) {
                settings.is_keep_sort = false;
            }
            Actions.setView(this.tableService.aiTable, { settings }, [this.tableService.activeViewId()]);
            if (!settings.is_keep_sort) {
                this.manualSortRecords();
            }
        }
    };

    keepSortChange = (keepSort: boolean, sorts: StyxSortInfo[]) => {
        const settings: Partial<AITableFilterConditions & AITableSortOptions> = {
            ...(this.tableService.activeView().settings || {}),
            is_keep_sort: keepSort,
            sorts: helpers.cloneDeep(sorts).map(sort => ({
                sort_by: sort.sort_by,
                direction: sort.direction as unknown as SortDirection
            }))
        };
        if (!keepSort) {
            this.manualSortRecords();
        }
        Actions.setView(this.tableService.aiTable, { settings }, [this.tableService.activeViewId()]);
    };

    manualSortRecords() {
        const aiTable = this.tableService.aiTable;
        const activeView = this.tableService.activeView();
        const sortKeysMap = this.tableService.fieldSortKeysMap;
        const records = this.tableService.records();
        const recordsIndexMap = new Map(records?.map((item, index) => [item._id, index]));
        const newSortedRecords = sortRecordsBySortInfo(aiTable, records, activeView, sortKeysMap);
        const actions: AITableAction[] = [];
        newSortedRecords.forEach((record, index) => {
            const action = buildSetRecordPositionsActon(aiTable, { [activeView._id]: index }, [recordsIndexMap.get(record._id)]);
            actions.push(action);
        });
        aiTable.apply(actions);
    }

    openViewGroup(event: Event) {}

    searchEnter(event: QuerySearchInfo) {
        const settings: Partial<AITableSearchOptions & AITableFilterConditions & AITableSortOptions> = {
            ...(this.tableService.activeView().settings || {}),
            keywords: event.keywords
        };
        Actions.setView(this.tableService.aiTable, { settings }, [this.tableService.activeViewId()]);
        this.searchChange.emit(event);
    }

    initializeViewQuery() {
        const aiTableFields = this.tableService.fields();
        this.styxViewQueryService.propertyService.clearAllProperties();
        this.styxViewQueryService.initialize({
            filterMode: 'customize',
            disabledLogic: false,
            selectableProperties: toStyxPropertyInfos(aiTableFields),
            selectableOperations: toStyxPropertyOperations(),
            save$: (styxConditions: QueryCondition[]) => {
                this.tableService.aiTable.recordsWillHidden.set([]);
                const fieldMap = this.tableService.aiTable.fieldsMap();
                const aiTableConditions = toAITableFilterConditions(styxConditions, fieldMap, this.usersData);
                const { conditions, condition_logical } = this.tableService.activeView().settings || {};
                const activeViewConditionLogical = aiTableConditions.condition_logical ?? AITableFilterLogical.and;
                if (!_lodash.isEqual(conditions, aiTableConditions.conditions) || condition_logical !== activeViewConditionLogical) {
                    const settings = {
                        ...(this.tableService.activeView().settings || {}),
                        ...aiTableConditions
                    };
                    Actions.setView(
                        this.tableService.aiTable,
                        {
                            settings
                        },
                        [this.tableService.activeViewId()]
                    );
                }
                return of(conditions);
            }
        });
    }

    ngOnInit(): void {
        this.styxViewQueryService.initializeQueryContent({ conditions: [] });
    }
}
