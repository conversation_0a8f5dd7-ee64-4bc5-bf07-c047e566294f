<thy-layout>
  <styx-content-header class="border-0">
    <ng-template #search>
      <styx-search
        [styxShowScope]="false"
        [styxSearchPopoverOptions]="popoverOptions"
        (styxSearchChange)="searchEnter($event)"
      ></styx-search>
    </ng-template>
    <ng-template #view>
      <styx-filter [styxPQLEnabled]="false" #filter></styx-filter>
      <styx-combination-sort
        #sort
        [styxFields]="fields()"
        [styxSorts]="sorts()"
        [styxIsKeepSort]="!!tableService.activeView()?.settings?.is_keep_sort"
        [styxSortsChange]="sortChange"
        [styxKeepSortChange]="keepSortChange"
      ></styx-combination-sort>
    </ng-template>
  </styx-content-header>
</thy-layout>
