import { ActiveIndexPipe, PropertyTypePipe, SelectableOptionsPipe } from '@ai-table/pipes/field.pipe';
import { CdkDragDrop, moveItemInArray, CdkDropList, CdkDrag } from '@angular/cdk/drag-drop';
import { NgClass } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    computed,
    DestroyRef,
    inject,
    Injector,
    input,
    Input,
    OnInit,
    signal,
    TemplateRef,
    ViewChild,
    ViewContainerRef,
    WritableSignal
} from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { Direction, helpers, Id, PropertyInfo, PropertyType, StyxI18nTrackingDirective, UtilService } from '@atinc/ngx-styx';
import { TranslateModule } from '@ngx-translate/core';
import { ThyAction } from 'ngx-tethys/action';
import { ThyBadge } from 'ngx-tethys/badge';
import { T<PERSON><PERSON><PERSON>on } from 'ngx-tethys/button';
import { Thy<PERSON><PERSON>ogBody, ThyDialogFooter, ThyDialogHeader } from 'ngx-tethys/dialog';
import { ThyIcon } from 'ngx-tethys/icon';
import { ThyPopover } from 'ngx-tethys/popover';
import { ThySegment, ThySegmentEvent, ThySegmentItem } from 'ngx-tethys/segment';
import { ThySelect } from 'ngx-tethys/select';
import { ThyOption } from 'ngx-tethys/shared';
import { ThySwitch } from 'ngx-tethys/switch';
import { ThyTooltipDirective } from 'ngx-tethys/tooltip';

// 因为会往组件库提，所以暂时放这里
export interface StyxSortInfo {
    sort_by: Id;
    direction: Direction;
}

@Component({
    selector: 'styx-combination-sort',
    templateUrl: './combination-sort.component.html',
    host: {
        class: 'styx-combination-sort'
    },
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        ThyDialogBody,
        ThyDialogHeader,
        ThyDialogFooter,
        ThySwitch,
        ThyButton,
        ThySelect,
        ThySegment,
        ThySegmentItem,
        ThyIcon,
        ThyAction,
        FormsModule,
        ThyOption,
        NgClass,
        SelectableOptionsPipe,
        PropertyTypePipe,
        ActiveIndexPipe,
        CdkDropList,
        CdkDrag,
        ThyBadge,
        ThyTooltipDirective,
        TranslateModule,
        StyxI18nTrackingDirective
    ]
})
export class StyxCombinationSort implements OnInit {
    @ViewChild('sortPanel') sortTemplate: TemplateRef<any>;

    styxSorts = input<StyxSortInfo[]>([]);

    styxFields = input<PropertyInfo[]>([]);

    @Input() styxIsKeepSort: boolean;

    @Input() styxSortsChange = (sorts: StyxSortInfo[]) => {};

    @Input() styxKeepSortChange = (isKeepSort: boolean, sorts: StyxSortInfo[]) => {};

    newSortId = signal(null);

    PropertyType = PropertyType;

    Direction = Direction;

    selectableFields = computed(() => {
        return this.styxFields().filter(item => !this.innerSorts().some(rule => rule.sort_by === item.key));
    });

    count = computed(() => {
        return this.innerSorts().length;
    });

    innerSorts: WritableSignal<StyxSortInfo[]>;

    private thyPopover = inject(ThyPopover);

    private util = inject(UtilService);

    private cdr = inject(ChangeDetectorRef);

    private viewContainerRef = inject(ViewContainerRef);

    public injector = inject(Injector);

    public destroyRef = inject(DestroyRef);

    ngOnInit(): void {
        this.innerSorts = signal(helpers.cloneDeep(this.styxSorts()));
        const styxFields$ = toObservable(this.styxFields, { injector: this.injector });
        const styxSorts$ = toObservable(this.styxSorts, { injector: this.injector });
        styxFields$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(styxFields => {
            this.updateInnerSorts(this.innerSorts());
        });
        styxSorts$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(styxSorts => {
            this.updateInnerSorts(styxSorts);
        });
    }

    updateInnerSorts(sorts: StyxSortInfo[]) {
        const newSorts = sorts.filter(sort => {
            return this.styxFields().some(item => item.key === sort.sort_by);
        });
        this.innerSorts.set(helpers.cloneDeep(newSorts));
    }

    openViewSort(event: Event) {
        this.thyPopover.open(this.sortTemplate, {
            origin: event.currentTarget as HTMLElement,
            placement: 'bottomLeft',
            width: '480px',
            insideClosable: false,
            originActiveClass: 'active',
            viewContainerRef: this.viewContainerRef,
            initialState: {}
        });
    }

    sortByChange(id: string, sort: StyxSortInfo) {
        this.innerSorts.update(sorts => {
            const updateIndex = sorts.indexOf(sort);
            sorts[updateIndex].sort_by = id;
            return [...sorts];
        });

        if (this.styxIsKeepSort) {
            this.sortInfoChange();
        }
    }

    sortInfoChange() {
        this.styxSortsChange(this.innerSorts());
    }

    onOk() {
        this.sortInfoChange();
        this.util.popover.close();
    }

    onClose() {
        this.util.popover.close();
    }

    trackBy = (index, item) => {
        return item.sort_by ?? index;
    };

    deleteSort(index: number) {
        this.innerSorts.update(item => {
            item.splice(index, 1);
            return [...item];
        });
        if (this.styxIsKeepSort) {
            this.sortInfoChange();
        }
    }

    selectedChange(event: ThySegmentEvent, sort: StyxSortInfo) {
        this.innerSorts.update((sorts: StyxSortInfo[]) => {
            const updateIndex = sorts.indexOf(sort);
            sorts[updateIndex] = {
                sort_by: sort.sort_by,
                direction: event.activeIndex === 1 ? Direction.descending : Direction.ascending
            };
            return [...sorts];
        });
        if (this.styxIsKeepSort) {
            this.sortInfoChange();
        }
    }

    keepShortChange() {
        this.styxKeepSortChange(this.styxIsKeepSort, this.innerSorts());
        this.cdr.markForCheck();
    }

    addSortInfo(value) {
        const defaultSortInfo = {
            sort_by: value,
            direction: Direction.ascending
        };
        this.innerSorts.update(item => {
            item.push(defaultSortInfo);
            return [...item];
        });
        setTimeout(() => {
            this.newSortId.set(null);
        }, 0);
        if (this.styxIsKeepSort) {
            this.sortInfoChange();
        }
    }

    drop(event: CdkDragDrop<StyxSortInfo[]>) {
        moveItemInArray(this.innerSorts(), event.previousIndex, event.currentIndex);
        if (this.styxIsKeepSort) {
            this.sortInfoChange();
        }
    }
}
