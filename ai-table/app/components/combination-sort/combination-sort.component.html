<a href="javascript:;" thyAction (click)="openViewSort($event)" styxI18nTracking>
  <thy-icon thyIconName="sort"></thy-icon>
  {{ 'styx.sort' | translate }}
  @if (styxIsKeepSort && count() > 0) {
    <thy-badge thyType="primary" [thyCount]="count()" thyTextColor="#6698FF" thyBackgroundColor="#eff4ff" class="ml-2"></thy-badge>
  }
</a>

<ng-template #sortPanel>
  <thy-dialog-header>
    <ng-template #dialogHeader>
      <h3 class="dialog-title" translate="styx.sort"></h3>
      @if (innerSorts().length) {
        <div class="d-flex align-items-center">
          <span class="mr-2 font-size-sm" translate="wiki.aiTable.sort.autoSort"></span>
          <thy-switch [(ngModel)]="styxIsKeepSort" thySize="xs" (ngModelChange)="keepShortChange()"></thy-switch>
        </div>
      }
    </ng-template>
  </thy-dialog-header>
  <thy-dialog-body class="px-2 pt-1" [ngClass]="styxIsKeepSort ? 'pb-8' : 'pb-0'">
    @let hasSorts = !!innerSorts().length;
    <div cdkDropList (cdkDropListDropped)="drop($event)" [class.mb-3]="hasSorts">
      @for (sort of innerSorts(); track trackBy(idx, sort); let idx = $index) {
        <div class="d-flex align-items-center py-1 mb-2 sort-item" cdkDrag>
          <thy-icon thyIconName="drag" class="draggable-icon"></thy-icon>
          <thy-select
            [ngModel]="sort.sort_by"
            class="mr-4"
            (ngModelChange)="sortByChange($event, sort)"
            styxI18nTracking
            [thyEmptyStateText]="'common.noResult' | translate"
            [thyShowSearch]="true"
          >
            @for (option of sort.sort_by | selectableOptions: styxFields() : innerSorts(); track option._id) {
              <thy-option [thyRawValue]="option" [thyValue]="option._id" thyShowOptionCustom="true" [thyLabelText]="option.name">
                <span thyText class="w-100 text-truncate">{{ option?.name }} </span>
              </thy-option>
            }
          </thy-select>
          <thy-segment
            class="mr-3 sort-segment"
            [thyActiveIndex]="sort.direction | activeIndex"
            (thySelectChange)="selectedChange($event, sort)"
          >
            @let type = sort.sort_by | propertyType: styxFields();
            @switch (type) {
              @case (
                type === PropertyType.date || type === PropertyType.number || type === PropertyType.rate || type === PropertyType.progress
                  ? type
                  : ''
              ) {
                <thy-segment-item [thyValue]="Direction.ascending"
                  >1
                  <thy-icon thyIconName="arrow-right" class="mx-2"></thy-icon>
                  9
                </thy-segment-item>
                <thy-segment-item [thyValue]="Direction.descending"
                  >9
                  <thy-icon thyIconName="arrow-right" class="mx-2"></thy-icon>
                  1
                </thy-segment-item>
              }
              @case (PropertyType.select) {
                <thy-segment-item [thyValue]="Direction.ascending">{{ 'wiki.aiTable.sort.optionAsc' | translate }}</thy-segment-item>
                <thy-segment-item [thyValue]="Direction.descending">{{ 'wiki.aiTable.sort.optionDesc' | translate }}</thy-segment-item>
              }
              @default {
                <thy-segment-item [thyValue]="Direction.ascending"
                  >A
                  <thy-icon thyIconName="arrow-right" class="mx-2"></thy-icon>
                  Z
                </thy-segment-item>
                <thy-segment-item [thyValue]="Direction.descending"
                  >Z
                  <thy-icon thyIconName="arrow-right" class="mx-2"></thy-icon>
                  A
                </thy-segment-item>
              }
            }
          </thy-segment>
          <a
            thyAction
            class="mr-6"
            thyIcon="trash"
            thyType="danger"
            href="javascript:;"
            styxI18nTracking
            [thyTooltip]="'common.delete' | translate"
            (click)="deleteSort(idx)"
          ></a>
        </div>
      }
    </div>
    <thy-select
      class="add-sort-select"
      [class.mt-1]="!hasSorts"
      [(ngModel)]="newSortId"
      styxI18nTracking
      [thyEmptyStateText]="'common.noResult' | translate"
      [thyPlaceHolder]="'wiki.aiTable.sort.selectColumn' | translate"
      [thyShowSearch]="true"
      (ngModelChange)="addSortInfo($event)"
    >
      @for (option of selectableFields(); track option._id) {
        <thy-option [thyRawValue]="option" [thyValue]="option._id" thyShowOptionCustom="true" [thyLabelText]="option.name">
          <span thyText class="w-100 text-truncate">{{ option?.name }}</span>
        </thy-option>
      }
    </thy-select>
  </thy-dialog-body>

  @if (!styxIsKeepSort) {
    <thy-dialog-footer thyAlign="right">
      <button class="cancel" thyButton="link-secondary" (click)="onClose()" translate="common.cancel"></button>
      <button class="confirm" thyButton="primary" (click)="onOk()" translate="common.ok"></button>
    </thy-dialog-footer>
  }
</ng-template>
