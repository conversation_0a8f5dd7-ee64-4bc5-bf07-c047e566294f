<ai-table-field-setting
  #fieldSetting
  [aiTable]="aiTable()"
  [isUpdate]="isUpdate()"
  [aiEditField]="aiEditField()"
  [aiExternalTemplate]="externalTemplate"
  [aiReferences]="aiReferences()"
  (setField)="setField($event)"
  (addField)="addField($event)"
></ai-table-field-setting>

<ng-template #externalTemplate>
  @if (fieldSetting.aiEditField() | toStyxPropertyType | propertyIsShowCustom) {
    <thy-form-group class="mx-0">
      <ng-template #formGroup>
        <styx-property-list-configurator
          #selectConfig
          [editable]="true"
          [wrapShow]="true"
          [showLabel]="true"
          [property]="fieldSetting.aiEditField().settings"
          [labelIsRequired]="true"
          [selectCustomizable]="true"
          [pieceEnabled]="true"
          [styxDisabledAdd]="isDisabledAddOption"
          (propertyChange)="propertyChange($event)"
        ></styx-property-list-configurator>
      </ng-template>
    </thy-form-group>
  }
</ng-template>
