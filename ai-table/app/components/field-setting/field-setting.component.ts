import {
    AITableField,
    AITableFieldType,
    AITableRecordUpdatedInfo,
    AITableReferences,
    SelectSettings,
    SetFieldOptions,
    UpdateFieldValueOptions
} from '@ai-table/utils';
import { ToPropertyTypePipe } from '@ai-table/pipes/field.pipe';
import { AITable, AITableActions, AITableFieldSetting, idCreator } from '@ai-table/grid';
import { addFields, AIViewTable, updateFieldAndValues, updateFieldValues } from '@ai-table/state';
import { booleanAttribute, ChangeDetectionStrategy, Component, effect, inject, input, model } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { PropertyIsShowCustomPipe, PropertyListConfiguratorComponent } from '@atinc/ngx-styx/property';
import { ThyFormModule } from 'ngx-tethys/form';
import { ThyPopoverRef } from 'ngx-tethys/popover';
import * as _ from 'lodash';
import { AppRootContext, PropertyInfo } from '@atinc/ngx-styx';
import { AI_TABLE_MAX_EDIT_FIELD_SETTING_OPTIONS } from '@ai-table/constants';

@Component({
    selector: 'wiki-ai-table-field-setting',
    templateUrl: './field-setting.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        FormsModule,
        ThyFormModule,
        PropertyIsShowCustomPipe,
        ToPropertyTypePipe,
        PropertyListConfiguratorComponent,
        AITableFieldSetting
    ],
    host: {
        class: 'wiki-ai-table-field-setting'
    }
})
export class WikiAITableFieldSetting {
    readonly aiTable = input.required<AITable>();

    readonly aiReferences = input.required<AITableReferences>();

    readonly isUpdate = input<boolean, unknown>(false, { transform: booleanAttribute });

    aiEditField = model.required<AITableField>();

    isDisabledAddOption = false;

    protected thyPopoverRef = inject(ThyPopoverRef<WikiAITableFieldSetting>);

    public appRootContext = inject(AppRootContext);

    private isSwitchType: boolean;

    constructor() {
        effect(() => {
            const options = (this.aiEditField().settings as SelectSettings)?.options || [];
            this.isDisabledAddOption = options.length >= AI_TABLE_MAX_EDIT_FIELD_SETTING_OPTIONS;
        });
    }

    setField(event: { fieldOptions: SetFieldOptions; isSwitchType: boolean }) {
        const { fieldOptions, isSwitchType } = event;
        this.isSwitchType = isSwitchType;
        this.aiEditField.set(fieldOptions.field);
        this.editFieldProperty(fieldOptions);
    }

    addField(field: AITableField) {
        this.aiEditField.set(field);
        this.editFieldProperty();
    }

    private getUpdatedInfo(): AITableRecordUpdatedInfo {
        const uid = this.appRootContext.me.uid;
        const time = new Date().getTime();
        return { updated_at: time, updated_by: uid };
    }

    private editFieldProperty(fieldOptions?: SetFieldOptions) {
        if (this.aiEditField().type === AITableFieldType.select) {
            const options = (this.aiEditField().settings as SelectSettings)?.options || [];
            for (let i = 0; i < options.length; i++) {
                if (options[i]._id == options[i].text) {
                    options[i]._id = idCreator();
                }
            }
        }
        if (this.isUpdate()) {
            const updatedInfo = this.isSwitchType ? undefined : this.getUpdatedInfo();
            const actions = {
                updateFieldValues: (value: UpdateFieldValueOptions[]) => {
                    updateFieldValues(this.aiTable() as AIViewTable, value, updatedInfo);
                }
            } as AITableActions;

            updateFieldAndValues(this.aiTable() as AIViewTable, this.aiReferences(), actions, fieldOptions);
        } else {
            addFields(this.aiTable() as AIViewTable, { originId: null, defaultValue: { ...this.aiEditField() } });
        }
        this.thyPopoverRef.close();
    }

    propertyChange(property: PropertyInfo) {
        this.isDisabledAddOption = property.options.length >= AI_TABLE_MAX_EDIT_FIELD_SETTING_OPTIONS;
        setTimeout(() => {
            this.thyPopoverRef.updatePosition();
        }, 0);
    }
}
