import { inject, Injectable, ViewContainerRef } from '@angular/core';
import { TableService } from './table.service';
import { KoEventObjectOutput } from '@ai-table/grid/angular-konva';
import {
    AITableField,
    AITableFieldType,
    AITableRecordUpdatedInfo,
    AITableReferences,
    AttachmentFieldValue,
    RichTextFieldValue,
    UpdateFieldValueOptions
} from '@ai-table/utils';
import {
    AppRootContext,
    FileEntity,
    getDrivePreviewUrl,
    getFileImageOriginUrl,
    getFileImagePreviewUrl,
    humanizeBytes,
    Id,
    isImageByExt,
    ShipBroadObjectTypes,
    StyxBusinessObjectDispatcher,
    StyxRelationItemInfo,
    StyxTranslateService,
    UtilService
} from '@atinc/ngx-styx';
import { ThyImageService } from 'ngx-tethys/image';
import { CellAttachmentsComponent } from '../components/cell-editors/attachment/cell-attachments.component';
import { PageAttachmentEntity, TheExtensionMode } from '@wiki/common/interface';
import { updateFieldValues } from '@ai-table/state';
import { ThyDialogSizes } from 'ngx-tethys/dialog';
import { CellRichtextComponent } from '../components/cell-editors/richtext/cell-richtext.component';
import { THE_MODE_TOKEN, TheModeConfig } from '@worktile/theia';
import { isSharedMode } from '@wiki/common/util/extension-mode';
import { AI_TABLE_CELL, AI_TABLE_CELL_ATTACHMENT_ADD, AI_TABLE_CELL_EDIT, AITableQueries, expandCell } from '@ai-table/grid';
import {
    TARGET_NAME_AI_TABLE_CELL_MORE_COUNT,
    TARGET_NAME_AI_TABLE_CELL_RELATION_ITEM_DELETE,
    TARGET_NAME_AI_TABLE_CELL_RELATION_ADD
} from '../constants';
import { CommonBroadObjectService } from '@wiki/common/services';
import { PageStore } from '@wiki/common/stores/page.store';
import { RelationItemType } from '@wiki/common/plugins/relation-item/type';
import { getCommonRelationOption, WikiPluginTypes } from '@wiki/common/types';
import { of } from 'rxjs';
import { RelationPageInfo } from '@wiki/common/plugins/relation-page';
import { AITableCustomReferences } from '../types/grid';

@Injectable()
export class TableEventService {
    public viewContainerRef = inject(ViewContainerRef);

    private tableService = inject(TableService);

    private appRootContext = inject(AppRootContext);

    private thyImageService = inject(ThyImageService);

    private util = inject(UtilService);

    private modeConfig = inject<TheModeConfig>(THE_MODE_TOKEN);

    private broadObjectService = inject(CommonBroadObjectService);

    private pageStore = inject(PageStore);

    private translate = inject(StyxTranslateService);

    private styxBusinessObjectDispatcher = inject(StyxBusinessObjectDispatcher);

    get isSharedMode() {
        return isSharedMode(this.modeConfig?.mode as TheExtensionMode);
    }

    get pageId() {
        return this.pageStore.state().page._id;
    }

    get aiTable() {
        return this.tableService.aiTable;
    }

    constructor() {}

    private getRelationOption(relationType: RelationItemType) {
        return getCommonRelationOption(this.translate)[relationType];
    }

    private updateRelationFieldValue(
        field: AITableField,
        originValue: any[],
        relationItems: StyxRelationItemInfo[],
        path: [string, string]
    ) {
        const [recordId, fieldId] = path;
        const newRelationItemIds = relationItems.map(item => item._id);
        this.updateFieldValues([
            {
                value: newRelationItemIds,
                path: [recordId, fieldId]
            }
        ]);
        if (field.type === WikiPluginTypes.relationPage) {
            newRelationItemIds.forEach(id => {
                this.pageStore.relationPageStore?.fetchRelationPage(id).subscribe({
                    next: (page: RelationPageInfo) => {
                        if (page) {
                            this.pageStore.relationPageStore.addRelationPage(page);
                        }
                    }
                });
            });
            return;
        }
        const relationItemStore = this.pageStore.getRelationItemStore(field.type as RelationItemType);
        const relationOption = this.getRelationOption(field.type as RelationItemType);
        return relationItemStore.getRelationItemById(relationOption.broadObjectType, newRelationItemIds.join(',')).subscribe();
    }

    getUpdatedInfo(): AITableRecordUpdatedInfo {
        const uid = this.appRootContext.me.uid;
        const time = new Date().getTime();
        return { updated_at: time, updated_by: uid };
    }

    updateFieldValues(value: UpdateFieldValueOptions[]) {
        updateFieldValues(this.aiTable, value, this.getUpdatedInfo());
    }

    onClick(e: KoEventObjectOutput<MouseEvent>) {
        const { targetName, fieldId, recordId, source } = e.targetNameDetail;

        if (targetName === AI_TABLE_CELL) {
            const value = AITableQueries.getFieldValue(this.aiTable, [recordId, fieldId]) || [];
            const field = this.aiTable.fieldsMap()[fieldId];
            if (field?.type === AITableFieldType.attachment && source) {
                if (source === AI_TABLE_CELL_ATTACHMENT_ADD) {
                    this.openCellAttachments(fieldId, recordId);
                } else if (!this.isSharedMode) {
                    const file = e.event.target.attrs.attachmentInfo;
                    this.openAttachmentPreview(file);
                }
            }

            if (field?.type === AITableFieldType.richText && source) {
                if (source === AI_TABLE_CELL_EDIT) {
                    this.openCellRichtext(fieldId, recordId);
                }
            }

            if (
                [
                    WikiPluginTypes.relationTicket,
                    WikiPluginTypes.relationWorkItem,
                    WikiPluginTypes.relationTestCase,
                    WikiPluginTypes.relationIdea,
                    WikiPluginTypes.relationObjective,
                    WikiPluginTypes.relationPage
                ].includes(field?.type as RelationItemType) &&
                source
            ) {
                if (source === TARGET_NAME_AI_TABLE_CELL_RELATION_ADD) {
                    this.openCellAddRelation(field, [recordId, fieldId]);
                } else if (source === TARGET_NAME_AI_TABLE_CELL_MORE_COUNT) {
                    expandCell(this.aiTable, [recordId, fieldId]);
                } else if (source === TARGET_NAME_AI_TABLE_CELL_RELATION_ITEM_DELETE) {
                    const relationItem = e.event.target.attrs.source;
                    const newRelationItemIds = value.filter(item => item !== relationItem._id);
                    this.updateFieldValues([
                        {
                            value: newRelationItemIds,
                            path: [recordId, fieldId]
                        }
                    ]);
                } else {
                    const relationOption = this.getRelationOption(field.type as RelationItemType);
                    this.styxBusinessObjectDispatcher.openObject(relationOption.broadObjectType, source, {
                        scrollCommentId: null
                    });
                }
            }
        }
    }

    onDbClick(e: KoEventObjectOutput<MouseEvent>) {
        const { targetName, fieldId, recordId } = e.targetNameDetail;

        if (targetName === AI_TABLE_CELL) {
            const field = this.aiTable.fieldsMap()[fieldId];
            if (field?.type === AITableFieldType.attachment) {
                this.openCellAttachments(fieldId, recordId);
            }

            if (field?.type === AITableFieldType.richText) {
                this.openCellRichtext(fieldId, recordId);
            }

            if (
                [
                    WikiPluginTypes.relationTicket,
                    WikiPluginTypes.relationWorkItem,
                    WikiPluginTypes.relationTestCase,
                    WikiPluginTypes.relationIdea,
                    WikiPluginTypes.relationObjective,
                    WikiPluginTypes.relationPage
                ].includes(field?.type as RelationItemType)
            ) {
                expandCell(this.aiTable, [recordId, fieldId]);
            }
        }
    }

    openAttachmentPreview(fileEntity: FileEntity) {
        if (isImageByExt(fileEntity?.addition?.ext)) {
            this.openImagePreview(fileEntity);
        } else {
            window.open(getDrivePreviewUrl(fileEntity), '_blank');
        }
    }

    openImagePreview(file: PageAttachmentEntity) {
        const images = [file].map((attachment, index) => {
            return {
                src: getFileImagePreviewUrl(attachment, this.appRootContext.globalInfo.config),
                alt: attachment.title,
                name: attachment.title,
                size: humanizeBytes(attachment?.addition?.size),
                origin: {
                    src: getFileImageOriginUrl(attachment, this.appRootContext.globalInfo.config)
                }
            };
        });
        this.thyImageService.preview(images);
    }

    openCellAttachments(fieldId: Id, recordId: Id) {
        const cellValue = AITableQueries.getFieldValue(this.aiTable, [recordId, fieldId]);
        const attachments = (cellValue || []).map(item => this.tableService.references().attachments[item]).filter(item => !!item);
        const editDialogRef = this.util.dialog.open(CellAttachmentsComponent, {
            initialState: {
                readonly: this.tableService.readonly(),
                attachments: attachments,
                updateValue: (attachments: PageAttachmentEntity[]) => {
                    const result: UpdateFieldValueOptions<AttachmentFieldValue> = {
                        value: attachments.map(attachment => attachment._id),
                        path: [recordId, fieldId]
                    };
                    this.updateFieldValues([result]);
                }
            },
            size: ThyDialogSizes.maxLg,
            viewContainerRef: this.viewContainerRef,
            restoreFocus: false
        });
    }

    openCellRichtext(fieldId: Id, recordId: Id) {
        const editDialogRef = this.util.dialog.open(CellRichtextComponent, {
            initialState: {
                aiTable: this.aiTable,
                fieldId: fieldId,
                recordId: recordId!,
                references: this.tableService.references(),
                readonly: this.tableService.readonly()
            },
            size: ThyDialogSizes.superLg,
            viewContainerRef: this.viewContainerRef,
            restoreFocus: false
        });

        editDialogRef.componentInstance.updateFieldValues.subscribe((value: UpdateFieldValueOptions<RichTextFieldValue>[]) => {
            this.updateFieldValues(value);
        });
    }

    openCellAddRelation(field: AITableField, path: [string, string]) {
        const references = this.tableService.references();
        const fieldType = field.type as WikiPluginTypes;
        const [recordId, fieldId] = path;
        const value = AITableQueries.getFieldValue(this.aiTable, [recordId, fieldId]) || [];
        const relationInfos = value.map(item => references[fieldType][item]).filter(item => !!item);
        switch (fieldType) {
            case WikiPluginTypes.relationWorkItem:
                this.broadObjectService.openWorkItemSelection(relationInfos, (relationItems: StyxRelationItemInfo[]) => {
                    this.updateRelationFieldValue(field, value, relationItems, [recordId, fieldId]);
                    return of(true);
                });
                break;
            case WikiPluginTypes.relationTestCase:
                this.broadObjectService.openTestCaseSelection(
                    relationInfos,
                    (relationItems: StyxRelationItemInfo[]) => {
                        this.updateRelationFieldValue(field, value, relationItems, [recordId, fieldId]);
                        return of(true);
                    },
                    { principalId: this.pageId }
                );
                break;
            case WikiPluginTypes.relationIdea:
                this.broadObjectService.openShipSelection(
                    ShipBroadObjectTypes.idea,
                    relationInfos,
                    (relationItems: StyxRelationItemInfo[]) => {
                        this.updateRelationFieldValue(field, value, relationItems, [recordId, fieldId]);
                        return of(true);
                    },
                    { principalId: this.pageId }
                );
                break;
            case WikiPluginTypes.relationTicket:
                this.broadObjectService.openShipSelection(
                    ShipBroadObjectTypes.ticket,
                    relationInfos,
                    (relationItems: StyxRelationItemInfo[]) => {
                        this.updateRelationFieldValue(field, value, relationItems, [recordId, fieldId]);
                        return of(true);
                    },
                    { principalId: this.pageId }
                );
                break;
            case WikiPluginTypes.relationObjective:
                this.broadObjectService.openObjectiveSelection(
                    relationInfos,
                    (relationItems: StyxRelationItemInfo[]) => {
                        this.updateRelationFieldValue(field, value, relationItems, [recordId, fieldId]);
                        return of(true);
                    },
                    { principalId: this.pageId }
                );
                break;

            case WikiPluginTypes.relationPage:
                this.broadObjectService.openPageSelection(
                    (relationItems: StyxRelationItemInfo[]) => {
                        this.updateRelationFieldValue(field, value, relationItems, [recordId, fieldId]);
                        return of(true);
                    },
                    true,
                    relationInfos
                );
                break;
        }
    }
}
