import { computed, effect, inject, Injectable, Signal, signal, WritableSignal } from '@angular/core';
import { WebsocketProvider } from '@wiki/common/util/shared/y-websocket';
import {
    AIViewTable,
    YjsAITable,
    applyYjsEvents,
    createSharedType,
    buildFieldsByView,
    buildRecordsByView,
    getDataBySharedType,
    getFieldsSizeMap,
    sortViews
} from '@ai-table/state';
import { catchError, map, Observable, of } from 'rxjs';
import { getProvider } from '@ai-table/utils/provider';
import { cache, GlobalUsersStore, helpers, ResponseData, StyxThyAvatarService, StyxTranslateService } from '@atinc/ngx-styx';
import {
    AITableRecords,
    AITableFields,
    AITableValue,
    AITableFieldType,
    AITableFieldsSizeMap,
    AITableView,
    AITableViewRecords,
    AITableViewFields,
    SharedType,
    AITableViews
} from '@ai-table/utils';
import { AITableContent } from '@wiki/common/interface';
import { AITableApiService, CommonPageApiService } from '@wiki/common/services';
import { TableMode } from '@ai-table/types';
import { WikiErrorService } from '@wiki/app/services/error.service';
import { PageStore } from '@wiki/common/stores/page.store';
import { AITableAvatarSize } from '@ai-table/grid';
import { getCommonRelationOption, WikiPluginTypes } from '@wiki/common/types';
import { AITableCustomReferences } from '../types/grid';
import { SvgService } from '@wiki/common/services/business-object-brand.service';
import { RelationItemInfo, RelationItemType } from '@wiki/common/plugins/relation-item/type';
import { AI_TABLE_MAX_FIELD_COUNT, AI_TABLE_MAX_RECORD_COUNT } from '@ai-table/constants/ai-table';

export const AI_TABLE_SERVICE_MAP = new WeakMap<AIViewTable, TableService>();

export const AI_TABLE_ACTIVE_VIEW_MAP = 'ai-table-active-view-map';

@Injectable()
export class TableService {
    public userStore = inject(GlobalUsersStore);

    public styxAvatarService = inject(StyxThyAvatarService);

    public pageStore = inject(PageStore);

    constructor() {
        effect(() => {
            if (this.initialized()) {
                if (this.provider) {
                    if (this.readonly()) {
                        this.mode.set(TableMode.collaborationAndReadonly);
                    } else {
                        this.mode.set(TableMode.collaboration);
                    }
                } else {
                    this.mode.set(TableMode.justShow);
                }
            }
        });
    }

    canUndo: WritableSignal<boolean> = signal<boolean>(false);

    canRedo: WritableSignal<boolean> = signal<boolean>(false);

    readonly: WritableSignal<boolean> = signal(false);

    maxRecords: WritableSignal<number> = signal(AI_TABLE_MAX_RECORD_COUNT);

    maxFields: WritableSignal<number> = signal(AI_TABLE_MAX_FIELD_COUNT);

    hiddenIndexColumn: WritableSignal<boolean> = signal(false);

    hiddenRowDrag: WritableSignal<boolean> = signal(false);

    mode: WritableSignal<TableMode> = signal(TableMode.unknown);

    views: WritableSignal<AITableView[]> = signal(null);

    records: WritableSignal<AITableViewRecords> = signal(null);

    fields: WritableSignal<AITableViewFields> = signal(null);

    fieldsSizeMap: WritableSignal<AITableFieldsSizeMap> = signal(null);

    initialized: WritableSignal<boolean> = signal(false);

    aiTable!: AIViewTable;

    provider!: WebsocketProvider | null;

    sharedType!: SharedType | null;

    activeViewId: WritableSignal<string> = signal('');

    svgMap: Map<string, string> = new Map();

    activeViewCacheKey: string;

    isLocalChanged: WritableSignal<boolean> = signal(false);

    isRemoteChanged: WritableSignal<boolean> = signal(false);

    private roomId?: string;

    private aiTableApiService = inject(AITableApiService);

    private errorService = inject(WikiErrorService);

    private commonPageApiService = inject(CommonPageApiService);

    private svgService = inject(SvgService);

    private translate = inject(StyxTranslateService);

    private memberSortKey = 'display_name_pinyin';

    private getRelationOption(relationType: RelationItemType) {
        return getCommonRelationOption(this.translate)[relationType];
    }

    public fieldSortKeysMap: Partial<Record<AITableFieldType, string>> = {
        [AITableFieldType.createdBy]: this.memberSortKey,
        [AITableFieldType.updatedBy]: this.memberSortKey,
        [AITableFieldType.member]: this.memberSortKey
    };

    activeView = computed(() => {
        return this.views().find(view => view._id === this.activeViewId()) as AITableView | undefined;
    });

    sortedViews = computed(() => {
        return sortViews(this.views());
    });

    activeViewShortId = computed(() => {
        return this.activeView()?.short_id;
    });

    renderRecords = computed(() => {
        return buildRecordsByView(
            this.aiTable,
            this.records(),
            this.fields(),
            this.activeView(),
            this.fieldSortKeysMap
        ) as AITableViewRecords;
    });

    renderFields = computed(() => {
        return buildFieldsByView(this.aiTable, this.fields(), this.activeView()) as AITableViewFields;
    });

    renderFieldsSizeMap = computed(() => {
        return getFieldsSizeMap(this.renderFields(), this.activeView());
    });

    aiBuildRenderDataFn: Signal<(records: AITableRecords, fields: AITableFields) => AITableValue> = computed(() => {
        return () => {
            return {
                records: this.renderRecords(),
                fields: this.renderFields(),
                fieldsSizeMap: this.renderFieldsSizeMap()
            };
        };
    });

    keywords = computed(() => {
        return this.activeView().settings?.keywords;
    });

    references = computed<AITableCustomReferences>(() => {
        const teamMembersMap = helpers.keyBy(this.userStore.snapshot.teamUsers, 'uid');
        const users = this.userStore.snapshot.teamUsers.map(user => {
            let avatar = user.avatar ? this.styxAvatarService.avatarSrcTransform(user.avatar, AITableAvatarSize.size24) : user.avatar;
            if (avatar && avatar.indexOf('http') !== -1) {
                avatar = avatar + `?v=0`;
            }
            return {
                ...user,
                display_name_pinyin: teamMembersMap[user.uid]?.display_name_pinyin || user.display_name_pinyin || user.display_name,
                avatar: avatar
            };
        });
        const ticketStore = this.pageStore.getRelationItemStore(WikiPluginTypes.relationTicket);
        const workItemStore = this.pageStore.getRelationItemStore(WikiPluginTypes.relationWorkItem);
        const testCaseStore = this.pageStore.getRelationItemStore(WikiPluginTypes.relationTestCase);
        const ideaStore = this.pageStore.getRelationItemStore(WikiPluginTypes.relationIdea);
        const objectiveStore = this.pageStore.getRelationItemStore(WikiPluginTypes.relationObjective);
        const pageStore = this.pageStore.relationPageStore;
        Object.values(this.pageStore.relationPageStore.getRelationPages() || {}).forEach(entity => {
            this.addRelationSvgToMap(WikiPluginTypes.relationPage, entity);
        });
        const attachments = this.pageStore.state().page.attachments;

        const result = {
            members: helpers.keyBy(users, 'uid'),
            attachments: helpers.keyBy(attachments, '_id') as any,
            svgMap: this.svgMap,
            [WikiPluginTypes.relationTicket]: helpers.keyBy(ticketStore.entities() || [], '_id'),
            [WikiPluginTypes.relationWorkItem]: helpers.keyBy(workItemStore.entities() || [], '_id'),
            [WikiPluginTypes.relationTestCase]: helpers.keyBy(testCaseStore.entities() || [], '_id'),
            [WikiPluginTypes.relationIdea]: helpers.keyBy(ideaStore.entities() || [], '_id'),
            [WikiPluginTypes.relationObjective]: helpers.keyBy(objectiveStore.entities() || [], '_id'),
            [WikiPluginTypes.relationPage]: pageStore.getRelationPages()
        };
        return result;
    });

    private initSvgMap(types: RelationItemType[]) {
        const svgMap = this.svgMap;
        types.forEach(type => {
            const relationStore = this.pageStore.getRelationItemStore(type);
            relationStore.entities$.subscribe(entities => {
                entities.forEach(entity => {
                    this.addRelationSvgToMap(type, entity);
                });
            });
        });

        this.svgService.getSvgByName('close').subscribe(svgString => {
            svgMap.set('close', svgString.outerHTML);
        });
    }

    private addRelationSvgToMap(type: RelationItemType, entity: RelationItemInfo) {
        const svgMap = this.svgMap;
        if (svgMap.has(entity._id)) {
            return;
        }
        const relationOption = this.getRelationOption(type);
        this.svgService.getSvgDataByRelation(relationOption.broadObjectType, entity).then(svgObject => {
            let { iconColor, svgString } = svgObject;
            // emoji 自带color
            if (!svgString.includes('fill="')) {
                svgString = svgString.replace('<path', `<path fill="${iconColor}" `);
            }
            if (svgString.includes('xlink:href') && !svgString.includes('xmlns:xlink="http:')) {
                svgString = svgString.replace('xmlns="http', 'xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http');
            }

            svgMap.set(entity._id, svgString);
        });
    }

    setInitialized(initialized: boolean) {
        this.initialized.set(initialized);
    }

    setReadonly(readonly: boolean) {
        this.readonly.set(readonly);
    }

    setHiddenIndexColumn(hidden: boolean) {
        this.hiddenIndexColumn.set(hidden);
    }

    setHiddenRowDrag(hidden: boolean) {
        this.hiddenRowDrag.set(hidden);
    }

    setCacheKey(userId: string, pageId: string) {
        this.activeViewCacheKey = userId + pageId;
    }

    setActiveView(activeViewId: string) {
        this.activeViewId.set(activeViewId);
        const activeViewMap = cache.get(AI_TABLE_ACTIVE_VIEW_MAP) || {};
        cache.set(AI_TABLE_ACTIVE_VIEW_MAP, {
            ...activeViewMap,
            [this.activeViewCacheKey]: activeViewId
        });
    }

    setAITable(aiTable: AIViewTable) {
        this.aiTable = aiTable;
    }

    setRecords(records: AITableViewRecords) {
        this.records.set(records);
    }

    setFields(fields: AITableViewFields) {
        this.fields.set(fields);
    }

    setViews(views: AITableViews) {
        this.views.set(views);
    }

    setLocalChanged(changed: boolean) {
        this.isLocalChanged.set(changed);
    }

    setRemoteChanged(changed: boolean) {
        this.isRemoteChanged.set(changed);
    }

    initializeCollaborationByPageId(pageId: string) {
        this.commonPageApiService.fetchCollaborationConnectInfo(pageId).subscribe({
            next: (data: ResponseData<{ token: string; uri: string }>) => {
                const collaborationInfo = data.value;
                this.initializeCollaboration(pageId, collaborationInfo, () => {
                    return this.commonPageApiService.fetchCollaborationConnectInfo(pageId).pipe(
                        map((collaborationData: ResponseData<{ token: string; uri: string }>) => {
                            return collaborationData.value.token;
                        }),
                        catchError(() => {
                            // handle error to avoid break reconnect
                            return of('');
                        })
                    );
                });
            },
            error: error => {
                this.errorService.defaultErrorHandler(error);
            }
        });
    }

    initializeCollaboration(room: string, collaborationInfo: { uri: string; token: string }, refetchToken: () => Observable<string>) {
        this.initSvgMap([
            WikiPluginTypes.relationWorkItem,
            WikiPluginTypes.relationTestCase,
            WikiPluginTypes.relationIdea,
            WikiPluginTypes.relationTicket
        ]);
        if (this.provider) {
            this.disconnect();
            return;
        }

        if (!this.sharedType) {
            this.sharedType = createSharedType();
            this.sharedType.observeDeep((events: any) => {
                if (!YjsAITable.isLocal(this.aiTable)) {
                    if (!this.initialized()) {
                        const data = getDataBySharedType(this.sharedType!);
                        this.setViews(data.views);
                        this.setFields(data.fields);
                        this.setRecords(data.records);
                        this.initialized.set(true);
                    } else {
                        applyYjsEvents(this.aiTable, this.sharedType!, events);
                    }
                }
            });
        }
        this.provider = getProvider(this.sharedType.doc!, collaborationInfo, refetchToken, room);
        this.provider.connect();
        this.roomId = room;
    }

    initializeContent(content: AITableContent) {
        this.initSvgMap([
            WikiPluginTypes.relationWorkItem,
            WikiPluginTypes.relationTestCase,
            WikiPluginTypes.relationIdea,
            WikiPluginTypes.relationTicket
        ]);
        this.setViews(content.views);
        this.setFields(content.fields);
        this.setRecords(content.records);
        this.setReadonly(true);
        this.initialized.set(true);
    }

    public fetchSaveVersion(roomId: string) {
        this.aiTableApiService.saveVersion(roomId).subscribe({
            error: err => {
                this.errorService.defaultErrorHandler(err);
            }
        });
    }

    disconnect() {
        if (this.provider) {
            this.provider.destroy();
            this.provider = null;
            this.sharedType = null;
        }
    }
}
