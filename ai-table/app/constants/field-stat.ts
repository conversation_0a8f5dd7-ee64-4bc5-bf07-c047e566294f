import { AITableFieldStatTypeItemInfo, AITableStatType, AITableUtilsI18nKey, DEFAULT_FIELD_STAT_TYPE_MAP } from '@ai-table/utils';

export const RELATION_FIELD_STAT_TYPE_ITEMS: AITableFieldStatTypeItemInfo[] = [
    DEFAULT_FIELD_STAT_TYPE_MAP[AITableStatType.None]!,
    DEFAULT_FIELD_STAT_TYPE_MAP[AITableStatType.CountAll]!,
    DEFAULT_FIELD_STAT_TYPE_MAP[AITableStatType.Filled]!,
    DEFAULT_FIELD_STAT_TYPE_MAP[AITableStatType.Empty]!,
    DEFAULT_FIELD_STAT_TYPE_MAP[AITableStatType.PercentFilled]!,
    DEFAULT_FIELD_STAT_TYPE_MAP[AITableStatType.PercentEmpty]!
];
