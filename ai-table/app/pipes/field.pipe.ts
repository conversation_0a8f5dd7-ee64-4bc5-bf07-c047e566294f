import { StyxSortInfo } from '@ai-table/components';
import { AITableField } from '@ai-table/utils';
import { toStyxPropertyType } from '@ai-table/utils/property';
import { Pipe, PipeTransform } from '@angular/core';
import { Direction, PropertyInfo } from '@atinc/ngx-styx';

@Pipe({
    name: 'toStyxPropertyType',
    standalone: true
})
export class ToPropertyTypePipe implements PipeTransform {
    transform(field: AITableField): number {
        return toStyxPropertyType(field);
    }
}

@Pipe({
    name: 'selectableOptions',
    standalone: true
})
export class SelectableOptionsPipe implements PipeTransform {
    transform(currentSort: string, fields: PropertyInfo[], sorts: StyxSortInfo[]): PropertyInfo[] {
        const otherSorts = sorts.filter(item => item.sort_by !== currentSort);
        return fields.filter(item => !otherSorts.some(rule => rule.sort_by === item.key));
    }
}

@Pipe({
    name: 'activeIndex',
    standalone: true
})
export class ActiveIndexPipe implements PipeTransform {
    transform(key: Direction): number {
        return Direction.ascending === key ? 0 : 1;
    }
}

@Pipe({ name: 'propertyType', standalone: true })
export class PropertyTypePipe implements PipeTransform {
    transform(key: string, fields: PropertyInfo[]) {
        const field = fields.find(item => item.key === key);
        return field?.type;
    }
}
