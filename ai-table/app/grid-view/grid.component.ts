import { StyxSortInfo, WikiAITableFieldSetting } from '@ai-table/components';
import { MemberCellEditorComponent } from '@ai-table/components/cell-editors/member/member-editor.component';
import { AITableOperation } from '@ai-table/components/operation/operation.component';
import {
    AITableField,
    AITableFieldType,
    AITableView,
    AITableAction,
    ActionName,
    AddFieldOptions,
    UpdateFieldValueOptions,
    AddRecordOptions,
    MoveFieldOptions,
    SetFieldWidthOptions,
    MoveRecordOptions,
    AITableAttachmentInfo,
    DateFieldValue,
    AITableRecordUpdatedInfo,
    ViewSettings,
    SortDirection,
    TrackableEntity,
    isUndefinedOrNull,
    AITableReferences,
    AITableFieldGroup,
    FieldValue,
    AITableFieldOption,
    isProgressAndReturnValue
} from '@ai-table/utils';
import { withRemoveView } from '@ai-table/plugins/view.plugin';
import { AI_TABLE_SERVICE_MAP, TableService } from '@ai-table/services/table.service';
import {
    AIViewTable,
    applyActionOps,
    Actions,
    withState,
    YjsAITable,
    EditFieldPropertyItem,
    CopyFieldPropertyItem,
    DividerMenuItem,
    addRecords,
    updateFieldValues,
    addFields,
    buildRemoveFieldItem,
    RemoveRecordsItem,
    CopyCellsItem,
    PasteCellsItem,
    moveFields,
    moveRecords,
    UndoManagerService,
    InsertUpwardRecords,
    InsertDownwardRecords
} from '@ai-table/state';
import { TableMode } from '@ai-table/types';
import { toStyxFilterConditions } from '@ai-table/utils/property';
import {
    ChangeDetectionStrategy,
    Component,
    inject,
    OnInit,
    WritableSignal,
    DestroyRef,
    ChangeDetectorRef,
    Signal,
    signal,
    viewChild,
    ViewContainerRef,
    computed
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
    AppRootContext,
    Direction,
    GlobalUsersStore,
    helpers,
    ResponseData,
    StyxFilterComponent,
    StyxFilterPanelComponent,
    StyxThyAvatarService,
    StyxTranslateService,
    UtilService,
    StyxViewFilterPropertyService,
    StyxViewQueryService,
    StyxViewQueryStore,
    StyxViewFilterConfigService,
    PublicPathPipe
} from '@atinc/ngx-styx';
import { getUnixTime } from 'date-fns';
import { ThyDatePickerFormatPipe } from 'ngx-tethys/date-picker';
import { PageStore } from '../../../common/stores/page.store';
import {
    AI_TABLE_RELATION_DEFAULT_WIDTH,
    AI_TABLE_RELATION_MIN_WIDTH,
    EXCLUDING_SORT_FIELD_TYPES,
    FileIconPath,
    AI_TABLE_MAX_EDIT_FIELD_SETTING_OPTIONS,
    RelationIdeaIconPath,
    RelationTestCaseIconPath,
    RelationTicketIconPath,
    RelationWorkItemIconPath,
    TargetIconPath
} from '../constants';
import { KoEventObjectOutput } from '@ai-table/grid/angular-konva';
import { TableEventService } from '../services/table-event.service';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { RenderScene, TheiaConverter } from '@atinc/selene';
import { filter, fromEvent } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AIFieldConfig, AITable, AITableActions, AITableGrid, FieldModelMap } from '@ai-table/grid';
import { CommonPageApiService, PageEventBus } from '@wiki/common/services';
import { PageInfo } from '@wiki/app/entities';
import { RelationField } from '../custom-field/relation/field-model';
import { renderRelationCell } from '../custom-field/relation/render';
import { AITableCellRelation } from '../custom-field/relation/cover-render';
import { getCommonRelationOption, WikiPluginTypes } from '@wiki/common/types';
import { plainToTheia, ElementKinds } from '@worktile/theia';
import { RelationItemType } from '@wiki/common/plugins/relation-item/type';
import { RelationPageInfo } from '@wiki/common/plugins/relation-page';

@Component({
    selector: 'ai-table-grid-view',
    templateUrl: './grid.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [AITableGrid, AITableOperation],
    providers: [
        StyxViewQueryService, // 初始化筛选器
        StyxViewFilterPropertyService, // properties、operators 等互不影响
        StyxViewFilterConfigService, // save$ 等互不影响
        StyxViewQueryStore, // 用户条件缓存等处理
        TableEventService,
        UndoManagerService,
        PublicPathPipe
    ],
    host: {
        class: 'wiki-ai-table-grid-view d-flex flex-column flex-fill'
    }
})
export class AITableGridView implements OnInit {
    translate = inject(StyxTranslateService);

    aiTable: AIViewTable;

    plugins = [withState, withRemoveView];

    tableOperation = viewChild(AITableOperation);

    public util = inject(UtilService);

    private undoManagerService = inject(UndoManagerService);

    datePickerFormatPipe = new ThyDatePickerFormatPipe();

    maxSelectOptions = AI_TABLE_MAX_EDIT_FIELD_SETTING_OPTIONS;

    fieldConfig: Signal<AIFieldConfig> = computed(() => {
        const readonly = this.tableService.readonly();
        const onlyOneField = this.tableService.fields().length === 1;
        const aiFieldConfig: AIFieldConfig = {
            hiddenIndexColumn: this.tableService.hiddenIndexColumn(),
            hiddenRowDrag: this.tableService.activeView()?.settings?.is_keep_sort,
            fieldSettingComponent: WikiAITableFieldSetting,
            filterFieldOptions: (fieldOptions: AITableFieldOption[]) => {
                let fieldOptionMap: Map<string, AITableFieldOption> = new Map();
                fieldOptions.forEach(item => {
                    const key = item?.settings?.['is_multiple'] ? `${item.type}${item?.settings?.['is_multiple']}` : item.type;
                    fieldOptionMap.set(key, item);
                });
                const fieldOptionKeys = [
                    AITableFieldType.text,
                    AITableFieldType.richText,
                    AITableFieldType.select,
                    `${AITableFieldType.select}true`,
                    AITableFieldType.number,
                    AITableFieldType.date,
                    AITableFieldType.member,
                    AITableFieldType.progress,
                    AITableFieldType.rate,
                    AITableFieldType.link,
                    AITableFieldType.attachment,

                    WikiPluginTypes.relationIdea,
                    WikiPluginTypes.relationTicket,
                    WikiPluginTypes.relationWorkItem,
                    WikiPluginTypes.relationTestCase,
                    WikiPluginTypes.relationObjective,
                    WikiPluginTypes.relationPage,

                    AITableFieldType.createdBy,
                    AITableFieldType.createdAt,
                    AITableFieldType.updatedBy,
                    AITableFieldType.updatedAt
                ];
                return fieldOptionKeys.map(key => fieldOptionMap.get(key));
            },

            customFields: {
                [WikiPluginTypes.relationWorkItem]: {
                    fieldOption: {
                        type: WikiPluginTypes.relationWorkItem,
                        group: AITableFieldGroup.advanced,
                        name: this.translate.instant<I18nSourceDefinitionType>('styx.workItem', { isTitle: true, isPlural: false }),
                        icon: 'task-board',
                        path: RelationWorkItemIconPath,
                        width: AI_TABLE_RELATION_DEFAULT_WIDTH,
                        minWidth: AI_TABLE_RELATION_MIN_WIDTH
                    },
                    fieldModel: new RelationField(),
                    render: renderRelationCell,
                    coverRender: AITableCellRelation,
                    getDefaultFieldValue: (field: AITableField) => {
                        return [];
                    }
                },
                [WikiPluginTypes.relationTicket]: {
                    fieldOption: {
                        type: WikiPluginTypes.relationTicket,
                        group: AITableFieldGroup.advanced,
                        name: this.translate.instant<I18nSourceDefinitionType>('wiki.resource.workOrder'),
                        icon: 'ticket',
                        path: RelationTicketIconPath,
                        width: AI_TABLE_RELATION_DEFAULT_WIDTH,
                        minWidth: AI_TABLE_RELATION_MIN_WIDTH
                    },
                    fieldModel: new RelationField(),
                    render: renderRelationCell,
                    coverRender: AITableCellRelation,
                    getDefaultFieldValue: (field: AITableField) => {
                        return [];
                    }
                },
                [WikiPluginTypes.relationTestCase]: {
                    fieldOption: {
                        type: WikiPluginTypes.relationTestCase,
                        group: AITableFieldGroup.advanced,
                        name: this.translate.instant<I18nSourceDefinitionType>('wiki.resource.testCase'),
                        icon: 'test-case',
                        path: RelationTestCaseIconPath,
                        width: AI_TABLE_RELATION_DEFAULT_WIDTH,
                        minWidth: AI_TABLE_RELATION_MIN_WIDTH
                    },
                    fieldModel: new RelationField(),
                    render: renderRelationCell,
                    coverRender: AITableCellRelation,
                    getDefaultFieldValue: (field: AITableField) => {
                        return [];
                    }
                },
                [WikiPluginTypes.relationIdea]: {
                    fieldOption: {
                        type: WikiPluginTypes.relationIdea,
                        group: AITableFieldGroup.advanced,
                        name: this.translate.instant<I18nSourceDefinitionType>('wiki.resource.productRequirement'),
                        icon: 'bulb',
                        path: RelationIdeaIconPath,
                        width: AI_TABLE_RELATION_DEFAULT_WIDTH,
                        minWidth: AI_TABLE_RELATION_MIN_WIDTH
                    },
                    fieldModel: new RelationField(),
                    render: renderRelationCell,
                    coverRender: AITableCellRelation,
                    getDefaultFieldValue: (field: AITableField) => {
                        return [];
                    }
                },
                [WikiPluginTypes.relationObjective]: {
                    fieldOption: {
                        type: WikiPluginTypes.relationObjective,
                        group: AITableFieldGroup.advanced,
                        name: this.translate.instant<I18nSourceDefinitionType>('wiki.resource.objective'),
                        icon: 'target',
                        path: TargetIconPath,
                        width: AI_TABLE_RELATION_DEFAULT_WIDTH,
                        minWidth: AI_TABLE_RELATION_MIN_WIDTH
                    },
                    fieldModel: new RelationField(),
                    render: renderRelationCell,
                    coverRender: AITableCellRelation,
                    getDefaultFieldValue: (field: AITableField) => {
                        return [];
                    }
                },
                [WikiPluginTypes.relationPage]: {
                    fieldOption: {
                        type: WikiPluginTypes.relationPage,
                        group: AITableFieldGroup.advanced,
                        name: this.translate.instant<I18nSourceDefinitionType>('styx.page', { isTitle: true, isPlural: false }),
                        icon: 'file',
                        path: FileIconPath,
                        width: AI_TABLE_RELATION_DEFAULT_WIDTH,
                        minWidth: AI_TABLE_RELATION_MIN_WIDTH
                    },
                    fieldModel: new RelationField(),
                    render: renderRelationCell,
                    coverRender: AITableCellRelation,
                    getDefaultFieldValue: (field: AITableField) => {
                        return [];
                    }
                }
            },
            fieldMenus: (aiTable: AITable) => {
                const result =
                    this.tableService.mode() !== TableMode.justShow
                        ? [
                              {
                                  ...EditFieldPropertyItem(aiTable, this.actions, this.tableService.references() as AITableReferences),
                                  hidden: () => readonly
                              },
                              {
                                  ...CopyFieldPropertyItem(aiTable, (fieldOptions: AddFieldOptions) => {
                                      this.addField(fieldOptions);
                                  }),
                                  hidden: () => readonly
                              },
                              { ...DividerMenuItem, hidden: () => readonly },
                              {
                                  type: 'sortByAsc',
                                  name: (field: AITableField) => {
                                      const fieldType = field?.type;
                                      switch (fieldType) {
                                          case AITableFieldType.progress:
                                          case AITableFieldType.rate:
                                          case AITableFieldType.number:
                                          case AITableFieldType.date:
                                          case AITableFieldType.createdAt:
                                          case AITableFieldType.updatedAt:
                                              return this.translate.instant<I18nSourceDefinitionType>('wiki.aiTable.sort.numberAsc');
                                          case AITableFieldType.select:
                                              return this.translate.instant<I18nSourceDefinitionType>('wiki.aiTable.sort.selectAsc');
                                          default:
                                              return this.translate.instant<I18nSourceDefinitionType>('wiki.aiTable.sort.defaultAsc');
                                      }
                                  },
                                  icon: 'sort',
                                  exec: (aiTable: AITable, field: Signal<AITableField>) => {
                                      this.handleSortMenu(field(), Direction.ascending);
                                  },
                                  hidden: (aiTable: AITable, field: Signal<AITableField>) =>
                                      EXCLUDING_SORT_FIELD_TYPES.includes(field().type as AITableFieldType)
                              },
                              {
                                  type: 'sortByDesc',
                                  name: (field: AITableField) => {
                                      const fieldType = field?.type;
                                      switch (fieldType) {
                                          case AITableFieldType.progress:
                                          case AITableFieldType.rate:
                                          case AITableFieldType.number:
                                          case AITableFieldType.date:
                                          case AITableFieldType.createdAt:
                                          case AITableFieldType.updatedAt:
                                              return this.translate.instant<I18nSourceDefinitionType>('wiki.aiTable.sort.numberDesc');
                                          case AITableFieldType.select:
                                              return this.translate.instant<I18nSourceDefinitionType>('wiki.aiTable.sort.selectDesc');
                                          default:
                                              return this.translate.instant<I18nSourceDefinitionType>('wiki.aiTable.sort.defaultDesc');
                                      }
                                  },
                                  icon: 'sort-reverse',
                                  exec: (aiTable: AITable, field: Signal<AITableField>) => {
                                      this.handleSortMenu(field(), Direction.descending);
                                  },
                                  hidden: (aiTable: AITable, field: Signal<AITableField>) =>
                                      EXCLUDING_SORT_FIELD_TYPES.includes(field().type as AITableFieldType)
                              },
                              {
                                  type: 'addCondition',
                                  name: this.translate.instant<I18nSourceDefinitionType>('wiki.aiTable.filter.byColumn'),
                                  icon: 'filter-line',
                                  exec: (aiTable: AITable, field: WritableSignal<AITableField>) => {
                                      const filterComponent = this.tableOperation().filterElement();
                                      const filterConditions = [
                                          {
                                              field_id: field()._id,
                                              operation: undefined,
                                              value: undefined
                                          }
                                      ];
                                      const { condition_logical, conditions } = this.tableService.activeView().settings || {};
                                      filterComponent.queryStore.updateConfig({
                                          conditions: toStyxFilterConditions({
                                              condition_logical: condition_logical,
                                              conditions: [...(conditions || []), ...filterConditions]
                                          })
                                      });

                                      this.openFilterPanel(filterComponent);
                                  },
                                  hidden: (aiTable: AITable, field: WritableSignal<AITableField>) => {
                                      const { conditions } = this.tableOperation().conditions() || {};
                                      if (conditions) {
                                          return !!conditions.find(item => item.field_id === field()._id);
                                      }
                                      return false;
                                  }
                              },
                              {
                                  type: 'removeCondition',
                                  name: this.translate.instant<I18nSourceDefinitionType>('wiki.aiTable.filter.removeCondition'),
                                  icon: 'filter-line',
                                  exec: (aiTable: AITable, field: WritableSignal<AITableField>) => {
                                      const tableOperationComponent = this.tableOperation();
                                      const filterComponent = this.tableOperation().filterElement();
                                      const { conditions } = tableOperationComponent.conditions();
                                      const filterConditions = conditions.filter(item => item.field_id !== field()._id);
                                      const settings = {
                                          ...(this.tableService.activeView().settings || {}),
                                          conditions: filterConditions
                                      };
                                      Actions.setView(
                                          this.tableService.aiTable,
                                          {
                                              settings
                                          },
                                          [this.tableService.activeViewId()]
                                      );
                                      filterComponent.queryStore.updateConfig({
                                          conditions: toStyxFilterConditions({
                                              condition_logical: settings.condition_logical,
                                              conditions: settings.conditions
                                          })
                                      });
                                  },
                                  hidden: (aiTable: AITable, field: WritableSignal<AITableField>) => {
                                      const { conditions } = this.tableOperation().conditions() || {};
                                      if (conditions) {
                                          return !conditions.find(item => item.field_id === field()._id);
                                      }
                                      return true;
                                  }
                              },
                              { ...DividerMenuItem, hidden: () => readonly || onlyOneField },
                              {
                                  ...buildRemoveFieldItem(aiTable, () => {
                                      return this.getUpdatedInfo();
                                  }),
                                  hidden: () => readonly || onlyOneField
                              }
                          ]
                        : [];
                return result;
            },
            fieldRenderers: {
                [AITableFieldType.date]: {
                    toText: (field: AITableField, value: DateFieldValue) => {
                        if (isUndefinedOrNull(value)) {
                            return value;
                        }
                        return this.datePickerFormatPipe.transform(value.timestamp as any);
                    }
                },
                [AITableFieldType.createdAt]: {
                    toText: (field: AITableField, value: DateFieldValue) => {
                        if (isUndefinedOrNull(value)) {
                            return value;
                        }
                        return this.datePickerFormatPipe.transform(value.timestamp as any);
                    }
                },
                [AITableFieldType.updatedAt]: {
                    toText: (field: AITableField, value: DateFieldValue) => {
                        if (isUndefinedOrNull(value)) {
                            return value;
                        }
                        return this.datePickerFormatPipe.transform(value.timestamp as any);
                    }
                },
                [AITableFieldType.member]: {
                    editor: MemberCellEditorComponent
                },
                [AITableFieldType.richText]: {
                    toText: (field: AITableField, value: any) => {
                        const richTextField = FieldModelMap[field.type];
                        if (!richTextField.isValid(value) || !value || (helpers.isArray(value) && value.length === 0)) {
                            return '';
                        }
                        const plainText = TheiaConverter.renderTheiaByScene(value, RenderScene.descriptionDisplay);
                        return plainText;
                    },
                    toFieldValue: (text: string, cellValue: FieldValue) => {
                        if (cellValue?.text && cellValue?.url) {
                            const { text, url } = cellValue;
                            return [
                                {
                                    type: ElementKinds.paragraph,
                                    children: [
                                        {
                                            type: 'link',
                                            url,
                                            children: [
                                                {
                                                    type: 'text',
                                                    text
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ];
                        } else {
                            if (text) {
                                return plainToTheia(text);
                            }
                            return null;
                        }
                    }
                }
            }
        };
        return aiFieldConfig;
    });

    actions: AITableActions = {
        updateFieldValues: (data: UpdateFieldValueOptions[]) => {
            this.updateFieldValues(data);
        },
        setField: (field: AITableField) => {
            this.setField(field);
        },
        addRecord: (data: AddRecordOptions) => {
            this.addRecord(data);
        },
        addField: (data: AddFieldOptions) => {
            this.addField(data);
        }
    };

    contextMenuItems = (aiTable: AITable) => {
        const copyAndPasteDisabled = this.tableService.readonly() || !aiTable.selection().selectedCells.size;
        const insertHidden = this.tableService.readonly() || aiTable.selection().selectedRecords.size;
        const insertDisabled = this.tableService.maxRecords() <= aiTable.records().length;
        return [
            {
                ...CopyCellsItem(aiTable, this.actions),
                disabled: () => copyAndPasteDisabled,
                hidden: () => copyAndPasteDisabled
            },
            {
                ...PasteCellsItem(aiTable, this.actions),
                disabled: () => copyAndPasteDisabled,
                hidden: () => copyAndPasteDisabled
            },
            {
                ...DividerMenuItem,
                disabled: (aiTable: AITable, targetName: string, position: { x: number; y: number }) => false,
                hidden: (aiTable: AITable, targetName: string, position: { x: number; y: number }) => copyAndPasteDisabled
            },
            {
                ...InsertUpwardRecords(aiTable, this.actions),
                disabled: (aiTable: AITable, targetName: string, position: { x: number; y: number }) => insertDisabled,
                hidden: (aiTable: AITable, targetName: string, position: { x: number; y: number }) => insertHidden
            },
            {
                ...InsertDownwardRecords(aiTable, this.actions),
                disabled: (aiTable: AITable, targetName: string, position: { x: number; y: number }) => insertDisabled,
                hidden: (aiTable: AITable, targetName: string, position: { x: number; y: number }) => insertHidden
            },
            {
                ...DividerMenuItem,
                disabled: (aiTable: AITable, targetName: string, position: { x: number; y: number }) => false,
                hidden: (aiTable: AITable, targetName: string, position: { x: number; y: number }) => insertHidden
            },
            {
                ...RemoveRecordsItem(aiTable, this.actions),
                hidden: () => this.tableService.readonly(),
                disabled: () => this.tableService.readonly()
            }
        ];
    };

    attachments: Signal<AITableAttachmentInfo[]> = signal([]);

    public route = inject(ActivatedRoute);

    public destroyRef = inject(DestroyRef);

    public cdr = inject(ChangeDetectorRef);

    public tableService = inject(TableService);

    public tableEventService = inject(TableEventService);

    public viewContainerRef = inject(ViewContainerRef);

    public userStore = inject(GlobalUsersStore);

    public appRootContext = inject(AppRootContext);

    styxAvatarService = inject(StyxThyAvatarService);

    public pageStore = inject(PageStore);

    public commonPageApiService = inject(CommonPageApiService);

    private globalPageStore = inject(PageEventBus);

    private publicPathPipe = inject(PublicPathPipe);

    ngOnInit(): void {
        this.bindShortcuts();
    }

    bindShortcuts() {
        fromEvent<KeyboardEvent>(document, 'keydown')
            .pipe(
                filter(event => event.ctrlKey || event.metaKey),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(async event => {
                if (this.tableService.readonly()) {
                    return;
                }
                const isUndoKey = event.key === 'z';
                const isSaveKey = event.key === 's';
                if (isSaveKey) {
                    // TODO: 不合理逻辑，需要删除
                    this.save();
                    const pageId = this.pageStore.state().page._id;
                    this.tableService.fetchSaveVersion(pageId);
                    event.preventDefault();
                    return;
                }
                if (isUndoKey) {
                    if (event.shiftKey) {
                        // 重做操作
                        this.redo();
                    } else {
                        // 撤销操作
                        this.undo();
                    }
                }
            });
    }

    save() {
        const page = this.pageStore.state().page;
        this.commonPageApiService.updatePage(page._id, { name: page.name, emoji_icon: page.emoji_icon }).subscribe({
            next: (data: ResponseData<PageInfo>) => {
                this.globalPageStore.emitPageInfo({
                    ...page,
                    ...helpers.omit(data.value, ['attachments'])
                });
            },
            error: error => {}
        });
    }

    redo() {
        this.undoManagerService.redo();
    }

    undo() {
        this.undoManagerService.undo();
    }

    openFilterPanel(filterComponent: StyxFilterComponent) {
        const filterElement = (filterComponent as any).viewContainerRef.element.nativeElement as HTMLElement;
        const origin = filterElement.querySelector('a.thy-action') as HTMLElement;
        const filterRef = this.util.popover.open(StyxFilterPanelComponent, {
            placement: 'bottomLeft',
            viewContainerRef: this.viewContainerRef,
            origin,
            manualClosure: true,
            originActiveClass: 'active',
            panelClass: 'styx-filter-overlay',
            autoAdaptive: true,
            initialState: {
                styxPQLEnabled: filterComponent.styxPQLEnabled,
                styxResetEnabled: filterComponent.styxResetEnabled,
                styxPlaceholder: filterComponent.styxPlaceholder
            }
        });
        filterRef.afterClosed().subscribe(() => {
            const { condition_logical, conditions } = this.tableService.activeView().settings || {};
            if ((conditions || []).length !== filterComponent.count) {
                filterComponent.queryStore.updateConfig({
                    conditions: toStyxFilterConditions({
                        condition_logical: condition_logical,
                        conditions: conditions || []
                    })
                });
            }
        });
    }

    handleSortMenu(field: AITableField, direction: Direction) {
        const sortComponent = this.tableOperation().sortElement();
        const { is_keep_sort, sorts } = this.tableService.activeView().settings || {};
        let updateView = false;
        const addedSort: StyxSortInfo = {
            sort_by: field._id,
            direction
        };
        const newSorts = (helpers.cloneDeep(sorts) as unknown as StyxSortInfo[]) || [];
        const exitSortIndex = newSorts.findIndex(item => item.sort_by === field._id);
        if (exitSortIndex > -1) {
            if (newSorts[exitSortIndex].direction !== direction) {
                newSorts.splice(exitSortIndex, 1, addedSort);
                updateView = true;
            }
        } else {
            newSorts.push(addedSort);
            updateView = true;
        }
        if (updateView) {
            sortComponent.updateInnerSorts(newSorts);
            if (is_keep_sort) {
                const settings: Partial<ViewSettings> = {
                    ...(this.tableService.activeView().settings || {}),
                    sorts: newSorts.map(sort => ({
                        sort_by: sort.sort_by,
                        direction: sort.direction as unknown as SortDirection
                    }))
                };
                Actions.setView(this.tableService.aiTable, { settings }, [this.tableService.activeViewId()]);
            }
        }

        const sortElement = (sortComponent as any).viewContainerRef.element.nativeElement as HTMLElement;
        const origin = sortElement.querySelector('a.thy-action') as HTMLElement;
        sortComponent.openViewSort({ currentTarget: origin } as unknown as Event);
    }

    tableInitialized(aiTable: AIViewTable) {
        this.aiTable = aiTable;
        this.aiTable.views = this.tableService.views;
        this.aiTable.activeViewId = this.tableService.activeViewId;
        this.aiTable.viewsMap = computed(() => {
            return this.tableService.views().reduce(
                (object, item) => {
                    object[item._id] = item;
                    return object;
                },
                {} as { [kay: string]: AITableView }
            );
        });
        this.tableService.setAITable(aiTable);
        this.initializeUndoManager();
        this.aiTable.onChange = () => {
            this.validateNeedUpdateReferences(this.aiTable.actions);
            if (this.tableService.sharedType) {
                if (!YjsAITable.isRemote(this.aiTable) && !YjsAITable.isUndo(this.aiTable)) {
                    YjsAITable.asLocal(this.aiTable, () => {
                        applyActionOps(this.aiTable, this.tableService.sharedType!, this.aiTable.actions);
                    });
                }
                if (YjsAITable.isRemote(this.aiTable)) {
                    this.tableService.setRemoteChanged(true);
                } else {
                    this.tableService.setLocalChanged(true);
                }
            }
        };
        AI_TABLE_SERVICE_MAP.set(aiTable, this.tableService);
    }

    initializeUndoManager() {
        if (!this.aiTable) {
            return;
        }
        this.undoManagerService.initialize(this.tableService.sharedType, this.aiTable);
    }

    validateNeedUpdateReferences(actions: AITableAction[]) {
        let notFindAttachmentsInReferences = [];
        let notFindRelationInReferences: {
            [key: string]: string[];
        } = {};
        actions.forEach(action => {
            if (action.type === ActionName.UpdateFieldValue) {
                const [recordId, fieldId] = action.path;
                const fields = this.tableService.renderFields() || [];
                const field = fields.find(item => item._id === fieldId);
                if (field && field.type === AITableFieldType.attachment) {
                    const tmpFilterArr =
                        action.newFieldValue?.filter(attachmentId => !this.tableService.references().attachments[attachmentId]) || [];
                    notFindAttachmentsInReferences.push(...tmpFilterArr);
                }

                if (
                    [
                        WikiPluginTypes.relationTicket,
                        WikiPluginTypes.relationWorkItem,
                        WikiPluginTypes.relationTestCase,
                        WikiPluginTypes.relationIdea,
                        WikiPluginTypes.relationObjective,
                        WikiPluginTypes.relationPage
                    ].includes(field?.type as RelationItemType)
                ) {
                    const tmpFilterArr =
                        action.newFieldValue?.filter(relationId => !this.tableService.references()[field?.type]?.[relationId]) || [];
                    if (notFindRelationInReferences[field?.type]) {
                        notFindRelationInReferences[field?.type].push(...tmpFilterArr);
                    } else {
                        notFindRelationInReferences[field?.type] = tmpFilterArr;
                    }
                }
            }
        });

        if (notFindAttachmentsInReferences.length > 0) {
            this.pageStore.getPageAttachments().subscribe();
        }

        if (Object.keys(notFindRelationInReferences).length > 0) {
            Object.keys(notFindRelationInReferences).forEach(key => {
                if (key === WikiPluginTypes.relationPage) {
                    notFindRelationInReferences[key].forEach(id => {
                        this.pageStore.relationPageStore?.fetchRelationPage(id).subscribe({
                            next: (page: RelationPageInfo) => {
                                if (page) {
                                    this.pageStore.relationPageStore.addRelationPage(page);
                                }
                            }
                        });
                    });
                } else {
                    const relationItemStore = this.pageStore.getRelationItemStore(key as RelationItemType);
                    const relationOption = getCommonRelationOption(this.translate)[key];
                    return relationItemStore
                        .getRelationItemById(relationOption.broadObjectType, notFindRelationInReferences[key].join(','))
                        .subscribe();
                }
            });
        }
    }

    addRecord(data: AddRecordOptions) {
        const uid = this.appRootContext.me.uid;
        const time = getUnixTime(new Date());
        const trackableEntity: TrackableEntity = {
            created_at: time,
            created_by: uid,
            updated_at: time,
            updated_by: uid
        };
        addRecords(this.aiTable, trackableEntity, data);
    }

    getUpdatedInfo(): AITableRecordUpdatedInfo {
        const uid = this.appRootContext.me.uid;
        const time = new Date().getTime();
        return { updated_at: time, updated_by: uid };
    }

    updateFieldValues(value: UpdateFieldValueOptions[]) {
        updateFieldValues(this.aiTable, value, this.getUpdatedInfo());
    }

    addField(data: AddFieldOptions) {
        addFields(this.aiTable, data);
    }

    setField(field: AITableField) {
        Actions.setField(this.aiTable, field, [field._id]);
    }
    onClick(e: KoEventObjectOutput<MouseEvent>) {
        this.tableEventService.onClick(e);
    }

    onDbClick(e: KoEventObjectOutput<MouseEvent>) {
        this.tableEventService.onDbClick(e);
    }

    moveField(data: MoveFieldOptions) {
        moveFields(this.aiTable, data);
    }

    setFieldWidth(data: SetFieldWidthOptions) {
        Actions.setFieldWidth(this.aiTable, data.path, data.width);
    }

    dragMoveRecords(data: MoveRecordOptions) {
        moveRecords(this.aiTable, data, this.getUpdatedInfo());
    }

    getI18nTextByKey = (key: string) => {
        return this.translate.instant(`wiki.aiTable.${key}`);
    };
}
