@if (tableService.records() && tableService.fields()) {
  <div aiTableOperation></div>
  <div class="wiki-ai-table-grid flex-fill">
    <ai-table-grid
      [aiReadonly]="tableService.readonly()"
      [aiFieldConfig]="fieldConfig()"
      [aiContextMenuItems]="contextMenuItems"
      [(aiRecords)]="tableService.records"
      [(aiFields)]="tableService.fields"
      [aiMaxRecords]="tableService.maxRecords()"
      [aiMaxFields]="tableService.maxFields()"
      [aiMaxSelectOptions]="maxSelectOptions"
      [(aiFieldsSizeMap)]="tableService.renderFieldsSizeMap"
      [aiKeywords]="tableService.keywords()"
      [aiPlugins]="plugins"
      [aiReferences]="tableService.references()"
      (aiTableInitialized)="tableInitialized($event)"
      [aiBuildRenderDataFn]="tableService.aiBuildRenderDataFn()"
      (aiAddRecord)="addRecord($event)"
      (aiAddField)="addField($event)"
      (aiSetField)="setField($event)"
      (aiClick)="onClick($event)"
      (aiDbClick)="onDbClick($event)"
      (aiUpdateFieldValues)="updateFieldValues($event)"
      (aiMoveField)="moveField($event)"
      (aiSetFieldWidth)="setFieldWidth($event)"
      (aiMoveRecords)="dragMoveRecords($event)"
      [aiGetI18nTextByKey]="getI18nTextByKey"
    ></ai-table-grid>
  </div>
}
