import { ChangeDetectionStrategy, Component, effect, inject, input, OnDestroy, untracked } from '@angular/core';
import { AITableGridView } from '../grid-view/grid.component';
import { AI_TABLE_ACTIVE_VIEW_MAP, TableService } from '../services/table.service';
import { AITableContent } from '@wiki/common/interface';
import { cache } from '@atinc/ngx-styx';
import { AITableViews } from '@ai-table/components';
import { ThyLoading } from 'ngx-tethys/loading';

@Component({
    selector: 'ai-table-common-grid',
    templateUrl: './common-grid.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [AITableGridView, AITableViews, ThyLoading],
    providers: [TableService],
    host: {
        class: 'wiki-ai-table-common-grid d-flex flex-column flex-fill'
    }
})
export class AITableCommonGrid implements OnDestroy {
    pageId = input<string>();

    readonly = input<boolean>();

    content = input<AITableContent>();

    hiddenIndexColumn = input<boolean>();

    hiddenRowDrag = input<boolean>();

    public tableService = inject(TableService);

    constructor() {
        effect(() => {
            if (this.tableService.initialized()) {
                untracked(() => {
                    let activeViewId = this.tableService.sortedViews()[0]._id;
                    const activeViewMap = cache.get(AI_TABLE_ACTIVE_VIEW_MAP);
                    if (activeViewMap && activeViewMap[this.tableService.activeViewCacheKey]) {
                        activeViewId = activeViewMap[this.tableService.activeViewCacheKey];
                    }
                    if (
                        !activeViewId ||
                        (activeViewId && this.tableService.sortedViews().findIndex(item => item._id === activeViewId) < 0)
                    ) {
                        activeViewId = this.tableService.sortedViews()[0]._id;
                    }
                    this.tableService.setActiveView(activeViewId);
                });
            }
        });

        effect(() => {
            const content = this.content();
            if (content) {
                content.views.forEach(view => {
                    view.settings = {};
                });
                this.tableService.initializeContent(content);
            } else if (this.pageId()) {
                this.tableService.disconnect();
                this.tableService.setInitialized(false);
                this.tableService.initializeCollaborationByPageId(this.pageId());
                this.tableService.setReadonly(this.readonly());
            } else {
                this.tableService.setInitialized(false);
            }
            this.tableService.setHiddenIndexColumn(this.hiddenIndexColumn());
            this.tableService.setHiddenRowDrag(this.hiddenRowDrag());
        });
    }

    ngOnDestroy(): void {
        this.tableService.disconnect();
    }
}
