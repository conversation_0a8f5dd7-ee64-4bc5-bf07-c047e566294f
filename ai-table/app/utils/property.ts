import {
    AITableField,
    AITable<PERSON>ields,
    AITableFieldType,
    AITableFilterOperation,
    AITableFilterConditions,
    AITableFilterLogical
} from '@ai-table/utils';
import { UserProfileInfo } from '@atinc/ngx-styx';
import {
    LogicalOperator,
    OperationSymbol,
    PropertyInfo,
    PropertyType,
    QueryCondition,
    ScopeDesignationType,
    UID,
    ME_UID,
    operationSymbolByIdMap
} from '@atinc/ngx-styx/core';
import _ from 'lodash';
import { SafeAny } from 'ngx-tethys/types';
import { WikiPluginTypes } from '@wiki/common/types';

export interface UserReferenceInfo {
    meUID: string;
    usersWithRole: { uid?: string; role_ids?: string[] }[];
    usersWithDepartment: UserProfileInfo[];
}

const toStyxPropertyTypeMap = {
    [AITableFieldType.richText]: PropertyType.text,
    [AITableFieldType.text]: PropertyType.text,
    [AITableFieldType.select]: PropertyType.select,
    [AITableFieldType.number]: PropertyType.number,
    [AITableFieldType.createdAt]: PropertyType.date,
    [AITableFieldType.updatedAt]: PropertyType.date,
    [AITableFieldType.date]: PropertyType.date,
    [AITableFieldType.member]: PropertyType.members,
    [AITableFieldType.updatedBy]: PropertyType.member,
    [AITableFieldType.createdBy]: PropertyType.member,
    [AITableFieldType.link]: PropertyType.link,
    [AITableFieldType.progress]: PropertyType.progress,
    [AITableFieldType.rate]: PropertyType.rate,
    [AITableFieldType.attachment]: PropertyType.special,
    [WikiPluginTypes.relationWorkItem]: PropertyType.special,
    [WikiPluginTypes.relationTicket]: PropertyType.special,
    [WikiPluginTypes.relationTestCase]: PropertyType.special,
    [WikiPluginTypes.relationIdea]: PropertyType.special,
    [WikiPluginTypes.relationObjective]: PropertyType.special,
    [WikiPluginTypes.relationPage]: PropertyType.special
};

const toStyxFilterLogicalMap = {
    [AITableFilterLogical.and]: LogicalOperator.and,
    [AITableFilterLogical.or]: LogicalOperator.or
};

const toStyxFilterOperationMap = {
    [AITableFilterOperation.besides]: OperationSymbol.besides,
    [AITableFilterOperation.between]: OperationSymbol.between,
    [AITableFilterOperation.contain]: OperationSymbol.contain,
    [AITableFilterOperation.empty]: OperationSymbol.empty,
    [AITableFilterOperation.eq]: OperationSymbol.eq,
    [AITableFilterOperation.exists]: OperationSymbol.exists,
    [AITableFilterOperation.gt]: OperationSymbol.gt,
    [AITableFilterOperation.gte]: OperationSymbol.gte,
    [AITableFilterOperation.in]: OperationSymbol.in,
    [AITableFilterOperation.lt]: OperationSymbol.lt,
    [AITableFilterOperation.lte]: OperationSymbol.lte,
    [AITableFilterOperation.ne]: OperationSymbol.ne,
    [AITableFilterOperation.nin]: OperationSymbol.nin,
    [AITableFilterOperation.notContain]: OperationSymbol.notContain
};

export function toStyxPropertyType(field: AITableField): PropertyType {
    return toStyxPropertyTypeMap[field.type];
}

export function toStyxPropertyInfos(fields: AITableFields): PropertyInfo[] {
    return fields.map(field => {
        return {
            ...field,
            ...(field.settings || {}),
            fieldType: field.type,
            key: field._id,
            type: toStyxPropertyType(field)
        };
    }) as PropertyInfo[];
}

export function toStyxPropertyOperations() {
    return {
        [PropertyType.special]: [operationSymbolByIdMap.empty, operationSymbolByIdMap.exists]
    };
}

export function toStyxFilterOperation(operation: AITableFilterOperation) {
    return toStyxFilterOperationMap[operation];
}

export function toStyxFilterLogical(logic: AITableFilterLogical) {
    return toStyxFilterLogicalMap[logic] || LogicalOperator.and;
}

export function toStyxFilterConditions(aiTableConditions: AITableFilterConditions): QueryCondition[] {
    if (!aiTableConditions?.conditions) {
        return [];
    }
    return aiTableConditions.conditions.map(item => {
        return {
            property_key: item.field_id as string,
            operation: toStyxFilterOperation(item.operation),
            logic: toStyxFilterLogical(aiTableConditions.condition_logical),
            value: (item as SafeAny).originValue || item.value
        };
    });
}

export function toAITableFilterLogical(logic: LogicalOperator) {
    return Object.keys(toStyxFilterLogicalMap).find(key => toStyxFilterLogicalMap[key] === logic) as AITableFilterLogical;
}

export function toAITableFilterOperation(operation: OperationSymbol): AITableFilterOperation {
    return Object.keys(toStyxFilterOperationMap).find(key => toStyxFilterOperationMap[key] === operation) as AITableFilterOperation;
}

export function toAITableFilterConditions(
    styxConditions: QueryCondition[],
    fieldsMap: { [key: string]: AITableField },
    usersData: UserReferenceInfo
): AITableFilterConditions {
    const conditions = styxConditions
        .map(item => {
            const fieldType = fieldsMap[item.property_key].type;
            if (isMemberField(fieldType)) {
                const itemValue = item.value as { id: string; type: ScopeDesignationType; data: SafeAny }[];
                return {
                    field_id: item.property_key,
                    operation: toAITableFilterOperation(item.operation),
                    value: toAITableMemberConditionValue(itemValue, usersData),
                    originValue: (itemValue || []).map(item => {
                        return _.pick(item, ['id', 'type']);
                    })
                };
            }

            return {
                field_id: item.property_key,
                operation: toAITableFilterOperation(item.operation),
                value: item.value
            };
        })
        .filter(item => item.operation);
    return {
        conditions,
        condition_logical: toAITableFilterLogical(styxConditions[0]?.logic) || AITableFilterLogical.and
    };
}

function isMemberField(fieldType: AITableFieldType | string): boolean {
    if (fieldType === AITableFieldType.member || fieldType === AITableFieldType.updatedBy || fieldType === AITableFieldType.createdBy) {
        return true;
    }
    return false;
}

function toAITableMemberConditionValue(
    originValue: { id: string; type: ScopeDesignationType; data: SafeAny }[],
    usersData: UserReferenceInfo
): UID[] {
    const { meUID, usersWithRole, usersWithDepartment } = usersData;
    let memberIds: UID[] = [];

    (originValue || []).forEach(item => {
        if (item.type === ScopeDesignationType.user) {
            memberIds.push(item.id);
        } else if (item.type === ScopeDesignationType.userGroup) {
            memberIds = memberIds.concat(item.data?.uids || []);
        } else if (item.type === ScopeDesignationType.department) {
            const uids = usersWithDepartment
                .filter(user => {
                    return user.department_id === item.id;
                })
                .map(item => item.uid);
            memberIds = memberIds.concat(uids);
        } else if (item.type === ScopeDesignationType.role) {
            if (item.id === ME_UID && meUID) {
                // 当前用户
                memberIds.push(meUID);
            } else {
                usersWithRole.forEach(member => {
                    if (member.role_ids && member.role_ids.length > 0 && member.role_ids.some(roleId => roleId === item.id)) {
                        memberIds.push(member.uid);
                    }
                });
            }
        } else {
        }
    });

    memberIds = _.uniq(memberIds);
    return memberIds;
}
