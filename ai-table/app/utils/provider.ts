import { PageTypes } from '@wiki/app/constants';
import { WebsocketProvider } from '@wiki/common/util/shared/y-websocket';
import { Observable } from 'rxjs';
import * as Y from 'yjs';

export const getProvider = (
    doc: Y.Doc,
    collaborationInfo: { uri: string; token: string },
    refetchToken: () => Observable<string>,
    room: string
) => {
    const provider = new WebsocketProvider(collaborationInfo.uri, refetchToken, room, doc, {
        params: {
            token: collaborationInfo.token,
            client: doc.clientID,
            page_type: PageTypes.table
        }
    });
    return provider;
};
