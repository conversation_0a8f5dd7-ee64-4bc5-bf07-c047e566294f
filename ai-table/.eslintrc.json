{"extends": "../.eslintrc.json", "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["ai-table/tsconfig.(app|spec).json"]}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "AITable", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "AITable", "style": "kebab-case"}]}}, {"files": ["*.html"], "rules": {}}]}